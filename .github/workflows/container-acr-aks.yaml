name: container-acr-aks
on:
  push:
    branches: [ dev, prod ]
  workflow_dispatch:

env:
  DOCKER_FILE_PATH: "./"

jobs:
  # registry-build-scan-push:
  #   uses: upl-dna-apps/app-devops/.github/workflows/build-and-scan-container-image-v2.yaml@main
  #   with:
  #     environment: ${{ github.ref_name }}
  #     DOCKER_FILE_PATH: "./" 
  #   secrets: inherit

  build-scan-push-container-image:
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name }}
    steps:
    
      - uses: actions/checkout@v3

      - name: Display Environment Variables
        run: | 
              echo "ENVIRONMENT: ${{ github.ref_name }}"
              echo "ACR_SERVER_NAME: ${{ vars.ACR_SERVER_NAME }}"
              echo "ACR_USERNAME: ${{ vars.ACR_USERNAME }}"
              echo "DOCKER_FILE_PATH: ${{ env.DOCKER_FILE_PATH }}"
              echo "PRISMA_API_URL: ${{ vars.PRISMA_API_URL }}"
              echo "APP_SERVICE_NAME: ${{ vars.APP_SERVICE_NAME }}"
              echo "APP_VERSION: ${{ github.sha }}"
              echo ${{ vars.ACR_SERVER_NAME }}/${{ vars.APP_SERVICE_NAME }}:${{ github.sha }}  

      - name: Create .env file
        run: |
          cat <<EOF > .env
          REACT_APP_BASE_URL=${{vars.REACT_APP_BASE_URL}}
          REACT_APP_AUTH_TOKEN=${{secrets.REACT_APP_AUTH_TOKEN}}
          EOF

      - name: Verify .env file
        run: cat .env   

      - name: Replace placeholders with secrets
        run: |
          sed -i "s|SECRET_KEY|${{ secrets.SECRET_KEY }}|g" src/environments/environment.prod.ts
          sed -i "s|BASE_URL|${{ vars.BASEURL }}|g" src/environments/environment.prod.ts
   
      - name: Build Docker Image
        run: |
              docker image build ${{ env.DOCKER_FILE_PATH }} -t ${{ vars.ACR_SERVER_NAME }}/${{ vars.APP_SERVICE_NAME }}:${{ github.sha }}
  
      # - name: Scan Docker Image Via Prisma
      #   continue-on-error: true
      #   id: scan        
      #   uses: bridgecrewio/checkov-action@master
      #   env:
      #     PRISMA_API_URL: ${{ vars.PRISMA_API_URL }}
      #   with:
      #     api-key: ${{ secrets.PRISMA_API_KEY }}
      #     soft_fail: false
      #     quiet: true
      #     docker_image: ${{ vars.ACR_SERVER_NAME }}/${{ vars.APP_SERVICE_NAME }}:${{ github.sha }}
      #     dockerfile_path: ${{ env.DOCKER_FILE_PATH }}/Dockerfile # path to the Dockerfile        
      #     download_external_modules: true
      #     log_level: DEBUG
      #     output_format: cli,sarif
      #     output_file_path: console,results.sarif

      # - name: Upload Sarif Report
      #   uses: github/codeql-action/upload-sarif@v3
      #   # Results are generated only on a success or failure
      #   # this is required since GitHub by default won't run the next step
      #   # when the previous one has failed. Security checks that do not pass will 'fail'.
      #   # An alternative is to add `continue-on-error: true` to the previous step
      #   # Or 'soft_fail: true' to checkov.
      #   if: success() || failure()
      #   with:
      #     sarif_file: results.sarif

      - name: Login to Azure Container Registry
        uses: azure/docker-login@v2
        with:
          login-server: ${{ vars.ACR_SERVER_NAME }}
          username:  ${{ vars.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}          

      - name: Push Docker Image to Azure Container Registry
        run: |
              docker push ${{ vars.ACR_SERVER_NAME }}/${{ vars.APP_SERVICE_NAME }}:${{ github.sha }}

  deploy-container-aks:
    needs: build-scan-push-container-image
    uses: upl-dna-apps/app-devops/.github/workflows/deploy-container-image-aks-public.yml@main
    with:
      environment: ${{ github.ref_name }}
    secrets: inherit
