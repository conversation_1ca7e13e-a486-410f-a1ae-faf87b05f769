# Use a compatible Node.js version (18.19 or later)
FROM node:20-alpine as build

# Set the working directory
WORKDIR /usr/local/app

# Copy the source code
COPY ./ /usr/local/app/

# Install dependencies
RUN npm install -f

# Build the Angular app
RUN npm run build

# Stage 2: Use nginx to serve the app
FROM nginx:1.19-alpine

# Copy the build output to nginx's html directory
COPY --from=build /usr/local/app/dist/dig-pgmlider-frontend /usr/share/nginx/html
COPY ./nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80
