# Lider-Program-App

This project was generated with Angular CLI version 6.1.0-beta.0.

You need to set up your development environment before you can do anything. 

Install Node.js® and npm if they are not already on your machine. here is link
https://nodejs.org/en/download/package-manager/

Verify that you are running at least node > 8.0.x and npm > 5.x.x by running node -v and npm -v in a terminal/console window. Older versions produce errors, but newer versions are fine.

`npm install npm@latest -g`

This will install the latest official, tested version of npm.

Then install the Angular CLI globally.

`npm install -g @angular/cli`

After cli successfully installed, hit the below commands to setup the project.

`npm install or npm i`

## Development server
To Serve the application Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory. Use the `--prod` flag for a production build.
