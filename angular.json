{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"upl-lider-program": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["lodash", "j<PERSON>y", "moment-timezone", "moment", "file-saver", "highcharts/modules/variable-pie", "highcharts/highstock", "highcharts", "ag-grid-community"], "outputPath": "dist/dig-pgmlider-frontend", "index": "src/index.html", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/assets", {"glob": "**/*", "input": "node_modules/font-awesome/fonts", "output": "assets/fonts"}, "src/favicon.ico", "src/assets"], "styles": ["node_modules/normalize.css/normalize.css", "node_modules/bootstrap/scss/bootstrap.scss", "./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/ngx-toastr/toastr.css", "node_modules/font-awesome/css/font-awesome.min.css", "node_modules/ionicons/scss/ionicons.scss", "src/app/theme/theme.scss", "src/app/theme/sass/_layout.scss", "src/app/theme/sass/_typography.scss", "src/app/theme/sass/_buttons.scss", "src/app/theme/sass/_icons.scss", "src/app/theme/sass/_table.scss", "src/app/theme/sass/_form.scss", "src/app/theme/sass/_modal.scss", "src/app/theme/sass/_preloader.scss", "src/app/theme/sass/_ionicons.scss", "src/app/theme/sass/_auth.scss", "src/app/theme/sass/bootstrap-overrides/overrides.scss", "src/styles.scss", "src/assets/css/brands-dropdown.css"], "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/jquery-slimscroll/jquery.slimscroll.js", "node_modules/tether/dist/js/tether.js", "node_modules/bootstrap/dist/js/bootstrap.js"], "main": "src/main.ts", "stylePreprocessorOptions": {"includePaths": ["src/app/theme/sass", "src/app/shared/styles"]}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "8mb", "maximumError": "8mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "upl-lider-program:build:production"}, "development": {"buildTarget": "upl-lider-program:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "upl-lider-program:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/assets", {"glob": "**/*", "input": "node_modules/font-awesome/fonts", "output": "assets/fonts"}, "src/favicon.ico", "src/assets"], "styles": ["node_modules/normalize.css/normalize.css", "node_modules/bootstrap/scss/bootstrap.scss", "./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/ngx-toastr/toastr.css", "node_modules/font-awesome/css/font-awesome.min.css", "node_modules/ionicons/scss/ionicons.scss", "src/app/theme/theme.scss", "src/styles.scss", "src/assets/css/brands-dropdown.css"], "scripts": ["src/assets", "src/favicon.ico"]}}}}}, "cli": {"analytics": "5b68b633-bd1a-4be4-8a60-fb9f0cb376c4"}, "stylePreprocessorOptions": {"includePaths": ["src/app/theme/sass", "src"]}}