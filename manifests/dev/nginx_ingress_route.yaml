apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pgmlider-nginx-ingress
  namespace: pgmlider
  annotations:
    # SSL & Rewrite
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-rewrite-log: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"

    # CORS
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://dev.pgmlider.upl-ltd.com" #updated the origin from * to frontend url for security config
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST"
    nginx.ingress.kubernetes.io/deny-methods: "DELETE, PATCH, TRACE, CONNECT"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Authorization, Content-Type"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"

    #File Upload LimitAdd 
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    

    # Security Headers (Only safe ones enabled)
    #nginx.ingress.kubernetes.io/force-ssl-redirect: "true"  # Can cause 503 if SSL is misconfigured
    # nginx.ingress.kubernetes.io/hsts: "true"  # HSTS can break HTTP traffic in dev
    # nginx.ingress.kubernetes.io/hsts-max-age: "63072000"
    # nginx.ingress.kubernetes.io/hsts-include-subdomains: "true"
    # nginx.ingress.kubernetes.io/hsts-preload: "true"
    # nginx.ingress.kubernetes.io/x-content-type-options: "nosniff"  # Safe
    # nginx.ingress.kubernetes.io/x-frame-options: "DENY"  # Safe
    # nginx.ingress.kubernetes.io/referrer-policy: "no-referrer"  # Safe
    # nginx.ingress.kubernetes.io/content-security-policy: |
    #   default-src 'self';
    #   script-src 'self' https://code.jquery.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net;
    #   style-src 'self' https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net 'unsafe-inline';
    #   font-src 'self' https://fonts.gstatic.com;
    #   img-src 'self' data:;
    #   object-src 'none';
    #   form-action 'self';
    #   base-uri 'self';
    #   frame-ancestors 'none';
    # nginx.ingress.kubernetes.io/x-xss-protection: "1; mode=block"  # Safe
    # #nginx.ingress.kubernetes.io/content-security-policy: "default-src 'self'; script-src 'self'; object-src 'none'; base-uri 'self';"  # Likely causing 503

spec:
  ingressClassName: nginx
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: pgmlider-frontend-service
            port:
              number: 80
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: pgmlider-backend-service
            port:
              number: 80
