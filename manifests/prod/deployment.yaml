apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgmlider-frontend-deployment
spec:
  replicas: 2
  selector:
    matchLabels:
      app: pgmlider-frontend
  template:
    metadata:
      labels:
        app: pgmlider-frontend
    spec:
      nodeSelector:
        app: pgmlider
      containers:
      - name: pgmlider-frontend
        image: dnaacrweucpappsprod.azurecr.io/pgmlider-frontend:v1
        ports:
        - containerPort: 80
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 1
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 1                  
---
apiVersion: v1
kind: Service
metadata:
  name: pgmlider-frontend-service
spec:
  type: ClusterIP
  selector:
    app: pgmlider-frontend
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 80
  
