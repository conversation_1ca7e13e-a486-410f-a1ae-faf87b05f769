server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;
    server_tokens off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

    add_header Allow "GET, POST, PUT" always;

    # Allow only specific HTTP methods
    if ($request_method !~ ^(GET|POST|PUT)$) {
        return 405;
    }

    root /usr/share/nginx/html;

    # Serve static assets (css, js, fonts, images) with correct MIME type
    location ~* \.(?:css|js|json|woff2?|ttf|otf|eot|svg|jpg|jpeg|png|gif|ico)$ {
        try_files $uri =404;
        expires 1y;
        access_log off;
        add_header Cache-Control "public";
    }

    # Main entry point for Angular app
    location / {
        try_files $uri $uri/ /index.html;
        index index.html;
    }

    # Optional: Prevent .htaccess and hidden file access
    location ~ /\. {
        deny all;
    }
}
