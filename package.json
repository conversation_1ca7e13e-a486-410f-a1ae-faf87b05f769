{"name": "lider-program", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@agm/core": "^3.0.0-beta.0", "@angular/animations": "^19.2.2", "@angular/cdk": "^19.2.3", "@angular/common": "^19.1.7", "@angular/compiler": "^19.1.7", "@angular/core": "^19.1.7", "@angular/flex-layout": "^15.0.0-beta.42", "@angular/forms": "^19.1.7", "@angular/material": "^19.2.3", "@angular/material-moment-adapter": "^19.1.5", "@angular/platform-browser": "^19.1.7", "@angular/platform-browser-dynamic": "^19.1.7", "@angular/router": "^19.2.5", "@fortawesome/fontawesome-free": "^6.7.2", "@grapecity/wijmo": "^5.20251.34", "@grapecity/wijmo.xlsx": "^5.20251.34", "@kolkov/angular-editor": "^3.0.0-beta.0", "@ng-bootstrap/ng-bootstrap": "^1.1.2", "@ng-icons/ionicons": "^23.1.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@popperjs/core": "^2.11.6", "@syncfusion/ej2-angular-richtexteditor": "^21.1.41", "@types/highcharts": "^7.0.0", "@types/jquery": "^3.5.16", "@types/lodash": "^4.14.191", "@types/node": "^18.13.0", "angular-highcharts": "^15.0.1", "angular-jquery-slimscroll": "^1.1.5", "angular-mydatepicker": "^0.11.5", "angular2-multiselect-dropdown": "^10.0.0", "angular2-text-mask": "^9.0.0", "animate.css": "^4.1.1", "axios": "^1.3.2", "bootstrap": "^4.0.0-alpha.6", "chart.js": "^4.0.0", "core-js": "^3.27.2", "crypto-js": "^4.1.1", "echarts": "^5.4.2", "file-saver": "^2.0.5", "flag-icons": "^6.6.6", "font-awesome": "^4.7.0", "highcharts": "^10.3.3", "highcharts-3d": "^0.1.7", "highcharts-angular": "^3.1.2", "highcharts-exporting": "^0.1.7", "highcharts-offline-exporting": "^0.1.7", "https": "^1.0.0", "ionicons": "^2.0.1", "jquery": "^3.6.3", "jquery-slimscroll": "^1.3.8", "jszip": "^3.10.1", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mydatepicker": "^2.6.11", "ng-click-outside2": "^11.1.0", "ng-image-slider": "^7.0.1", "ng-lazyload-image": "^9.1.3", "ng-multiselect-dropdown": "^1.0.0", "ngx-bootstrap": "^10.2.0", "ngx-csv": "^0.3.2", "ngx-dropzone": "^3.1.0", "ngx-echarts": "^19.0.0", "ngx-infinite-scroll": "^15.0.0", "ngx-modal": "0.0.29", "ngx-pagination": "^6.0.3", "ngx-restangular": "^6.0.0", "ngx-select-dropdown": "^3.2.0", "ngx-toastr": "^16.2.0", "ngx-uploader": "^11.0.0", "normalize.css": "^8.0.1", "ol": "^10.5.0", "overlapping-marker-spiderfier": "^1.1.4", "popper.js": "^1.16.1", "rxjs": "~7.8.0", "terser": "^5.16.3", "tether": "^2.0.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.8", "@angular/cli": "~19.1.8", "@angular/compiler-cli": "^19.1.7", "@angular/localize": "^19.1.7", "@types/crypto-js": "^4.1.2", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "^5.5.4"}}