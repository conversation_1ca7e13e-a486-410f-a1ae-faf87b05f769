import { EventEmitter, Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Utility } from '../shared/utility/utility';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  baseUrl = environment.baseUrl;
  authorizationToken!: any;
  currentToken: string = "";
  _disabledSidebar: EventEmitter<any> = new EventEmitter(true);
  randomNumber!: number;
  cropTypeData: any;
  brandID!: number;
  _closeAddBrandDialog: EventEmitter<any> = new EventEmitter(true);


  constructor(private http: HttpClient, private utility: Utility) {
    this.randomNumber = Math.floor(1000 + Math.random() * 9000);
  }

  fetchSsoInfo() {  
    return this.http
      .get(`${this.baseUrl}auth/auth-info`)
      .pipe(catchError(this.handleError));
  }

  setCropType(data: any) {
    this.cropTypeData = data;
  }

  getCropType() {
    return this.cropTypeData;
  }

  setEditBrandID(id: number) {
    this.brandID = id;
  }

  getEditBrandID() {
    return this.brandID;
  }

  getMilestones(data: any) {
    const headers = this.authorizationKey();
    return this.http
      .get(
        `${this.baseUrl}milestones?customerTypeId=${data.customerTypeId}` +
          "&query=" +
          data.searchedValue +
          "&page=" +
          data.currentPage +
          "&size=" +
          data.pageLimit +
          "&searchedValue=" +
          data.searchedValue +
          "&startDate=" +
          data.startDate +
          "&endDate=" +
          data.endDate,
        { headers,responseType: 'text' }
      )
      .pipe(catchError(this.handleError));
  }

  getAllProductByProvincesCode(): Observable<any> {
    let URL = "skus/all";
    const headers = this.authorizationKey();
    return this.http
      .get(this.baseUrl + URL, { headers: headers ,responseType: 'text'})
      .pipe(catchError(this.handleError));
  }

  getAllTarget(data: any) {
    const headers = this.authorizationKey();
    return this.http
      .get(
        `${this.baseUrl}targets?` +
          "query=" +
          data.searchedValue +
          "&size=" +
          data.pageLimit +
          "&page=" +
          data.currentPage +
          "&searchedValue=" +
          data.searchedValue ,
        {
          headers,responseType: 'text'
        }
      )
      .pipe(catchError(this.handleError));
  }
  submitTargeData(data: any) {
    const headers = this.authorizationKey();
    return this.http
      .post(`${this.baseUrl}targets`, data, { headers ,responseType: 'text'})
      .pipe(catchError(this.handleError));
  }

  getTargetDataById(data: number) {
    const headers = this.authorizationKey();
    return this.http
      .get(`${this.baseUrl}targets/` + data, {
        headers,responseType: 'text'
      })
      .pipe(catchError(this.handleError));
  }
  handleError(errorRes: HttpErrorResponse) {
    return throwError(errorRes);
  }

  authorizationKey() {
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    this.authorizationToken = userToken;
    //  this.authorizationToken = JSON.parse(
    //   localStorage.getItem("userToken") || "{}"
    // );
    if (this.authorizationToken && this.authorizationToken.token) {
      this.currentToken = this.authorizationToken.token;
    }
    
    const headers: HttpHeaders = this.createHeader(
      this.currentToken
    );
    return headers;
  }

  languageID(): any{
    const currentLanguage = this.utility.decryptLSDAta(localStorage.getItem('currentLanguage') || 'id');
    let id = currentLanguage;
    if(id=='id'){
      return "ind";
    }else{
      return "en";
    }
  }
  private createHeader(authorizationToken: any): HttpHeaders {

    let headers = new HttpHeaders();
    if (authorizationToken) {
      headers = headers.set('Authorization', `Bearer ${authorizationToken}`);
    } 
    return headers;
  }
    // return new HttpHeaders({
    //   Authorization: `Bearer ${authorizationToken}`,
    //   'Accept-Language':  this.languageID(),
    //   responseType: "text"
    // });
  

  // const headers = new HttpHeaders().set('ngrok-skip-browser-warning', 'true');

  userLogout(): Observable<any> {
    const headers = this.authorizationKey();

    return this.http
      .get(`${this.baseUrl}/logout`, { headers,responseType: 'text' })
      .pipe(catchError(this.handleError));
  }

  fetchUserTokenUsingSsoInfo(requestBody: any): Observable<any> {  
    let httpHeaders = new HttpHeaders();
    httpHeaders = httpHeaders.append("rememberMe", "true");
    return this.http
      .post(`${this.baseUrl}auth/authenticate`, requestBody, {
        headers: httpHeaders
      })
      .pipe(catchError(this.handleError));
  }

  fetchUserCurrentDetails(authorizationToken: any): Observable<any> {
    const headers: HttpHeaders = this.createHeader(authorizationToken);
    return this.http
      .get(`${this.baseUrl}get-user-by-id`, { headers ,responseType: 'text' })
      .pipe(catchError(this.handleError));
  }
}
