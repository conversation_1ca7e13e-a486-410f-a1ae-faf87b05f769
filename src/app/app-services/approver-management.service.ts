import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { environment } from "src/environments/environment";
import { Utility } from "../shared/utility/utility";

@Injectable({
  providedIn: 'root'
})

export class ApproverManagementService {
  baseurl: any = environment.baseUrl;
  getAllApproverURL: any = "approval-levels/getAllLevels"
  getAllActivityURL: any = "approval-activity/list";
  postApproverURL: any = "approval-levels";
  deleteApproverURL: any = "approval-levels/delete-level-by-id";
  
  constructor(private http: HttpClient,private utility:Utility) {

  }

  getApproverData(data: any){
    let URL = this.getAllApproverURL;
    const headers = this.authorizationKey();
    
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }


  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
    });
  }
  
  getActivityList() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + this.getAllActivityURL, {
      headers: headers,
      responseType: 'text',
    });
  }

  createApprovalLevel(requestPayload: any) {
    const payload = this.utility.encryptString(requestPayload)

    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + this.postApproverURL, 
      payload, 
      {
        headers: headers,
        responseType: 'text',
      }
    );
  }

  updateApprovalLevel(requestPayload: any) {
    const payload = this.utility.encryptString(requestPayload)
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + this.postApproverURL + '/' + requestPayload.id, 
      payload, 
      {
        headers: headers,
        responseType: 'text',
      }
    );
  }

  deleteApprovalLevel(requestPayload: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl + this.deleteApproverURL + '/' + requestPayload,
      {
        headers: headers,
        responseType: 'text' as 'json'
      }
    );
  }
}
