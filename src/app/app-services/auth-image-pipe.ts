import { Pipe, PipeTransform } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Utility } from '../shared/utility/utility';

@Pipe({
  name: 'authImage',
  standalone : true
})
export class AuthImagePipe implements PipeTransform {
  constructor(private http: HttpClient, private utility:Utility) {}

  removeDoubleQuotes(str: string): string {
    const result = str.replace(/^"(.*)"$/, '$1');
    return result;
  }

  async transform(src: string): Promise<string> {
    const headers = this.authorizationKey();
    try {
      const response = await this.http
        .get(src, { headers, responseType: 'blob' })
        .toPromise();
      const imageBlob = response as Blob;
      if (!imageBlob) {
        throw new Error('Image blob is undefined');
      }
      const reader = new FileReader();
      return new Promise<string>((resolve, reject) => {
        reader.onloadend = () => resolve(reader.result as string);
        reader.readAsDataURL(imageBlob);
      });
    } catch (error) {
      return 'assets/fallback.png';
    }
  }

  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;   
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken.token);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
    });
  }
}
