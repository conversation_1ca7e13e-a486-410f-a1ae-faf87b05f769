import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Pipe, PipeTransform } from '@angular/core';
import { AuthenticationHelper } from '../helpers/authentication';
import { Observable, lastValueFrom } from 'rxjs';
import { map } from 'rxjs/operators';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { Utility } from '../shared/utility/utility';

@Pipe({
  name: 'AuthImagePipe',
  standalone : true
})
export class AuthImagePipe implements PipeTransform {

  constructor(
    private http: HttpClient,private sanitizer: DomSanitizer, private utility:Utility
) {}

public uploadImageEcho1( src:string): Observable<any>{
 
  src= this.removeDoubleQuotes(src); 
  const headers = this.authorizationKey();
  return this.http.get(src, { headers, responseType: 'blob' });
}



removeDoubleQuotes(str: string): string {
  const result = str.replace(/^"(.*)"$/, '$1');
  return result;
}

async transform(src: string): Promise<string> {  
  src = this.removeDoubleQuotes(src); 
  const headers = this.authorizationKey();
  const imageResponse = this.http.get(src, { headers, responseType: 'blob' });
  const response = await lastValueFrom(imageResponse); 
  const imageBlob = response as Blob;
  if (!imageBlob) {
    throw new Error('Image blob is undefined');
  }
   
  await new Promise<void>((resolve) => {
    setTimeout(() => {
      resolve();
    }, 10000);  
  });
  
  const reader = new FileReader();
  return new Promise<string>((resolve, reject) => { 
    reader.readAsDataURL(imageBlob);
  }); 
}

 
  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;  
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`
    });
  }

}
