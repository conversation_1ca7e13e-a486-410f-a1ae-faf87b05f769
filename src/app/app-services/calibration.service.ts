import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Utility } from '../shared/utility/utility';

@Injectable({
  providedIn: 'root',
})
export class CalibrationService {
  baseurl: any = environment.baseUrl;
  getAllscanningCategoryUrl: any = 'scan-categories';
  getAllBusinessUnitUrl: any = 'calibrations';
  downloadTemplateUrl: any = 'calibrations/download-template';
  sendAllCalibrationDetails: any = 'calibrations';
  getAllBusinessUnitDropdownUrl: any = 'scan-categories';
  downloadCalibrationFileUrl: any = 'calibrations/download-file/';

  constructor(private http: HttpClient, private utility:Utility) {}

  returnScanningCategoryData() {
    let URL = this.getAllscanningCategoryUrl;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnAllBusinessUnitData(data: any) {
    let URL =
      this.getAllBusinessUnitUrl + '?scanCategoryId=' + data.scanCategoryId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnBusinessUnitDropdownData() {
    let URL = this.getAllBusinessUnitDropdownUrl;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnAddCalibrationData(formData: any): Observable<any> {
    let URL = this.sendAllCalibrationDetails;
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + URL, formData, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnTemplateData() {
    let URL = this.downloadTemplateUrl;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  downloadTemplate() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + this.downloadTemplateUrl, {
      responseType: 'blob',
      headers: headers,
    });
  }

  downloadCalibrationFile(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl + this.downloadCalibrationFileUrl + data.id,
      { responseType: 'blob', headers: headers }
    );
  }

  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;  
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
    });
  }
}
