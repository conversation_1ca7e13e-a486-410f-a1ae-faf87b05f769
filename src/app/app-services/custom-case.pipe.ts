import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'customCase',
})
export class CustomCasePipe implements PipeTransform {
  transform(dataList: any[], pipeWords: string[]): any[] {
    if (!Array.isArray(dataList)) {
      return dataList;
    }
    return dataList.map((item) => {
      if (item.name && typeof item.name === 'string') {
        item.name = item.name
          .toLowerCase()
          .split(' ')
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        pipeWords.forEach((pipeWord) => {
          const regex = new RegExp(`\\b${pipeWord}\\b`, 'gi');
          item.name = item.name.replace(regex, (match: string) =>
            match.toLowerCase()
          );
        });
      }
      return item;
    });
  }
}
