import { EventEmitter, Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  baseUrl = environment.baseUrl;
  _FirstName: EventEmitter<any> = new EventEmitter(true);
  _brandName : EventEmitter<any> = new EventEmitter(true);
  _SeasonId: EventEmitter<any> = new EventEmitter(true);
  seasonId!: number;
  scanningCategoryId!: number;
  zonesByBUCodeURL: any = 'zones/drop-down/by-scan-category/';
  regionByBUAndZoneCodes: any = 'regions/drop-down/by-scan-category/';
  territoriesByRegionCodeURL: any =
    'territories/drop-down/by-scan-category/';
  seasonByBU: any = 'scanning-categories/by-scan-category/';
  geographicPerformanceURL = 'analytical-dashboard/geographic-performance';
  getAllTopTenRegionsURL =
    'analytical-dashboard/top-ten-regions-territories';
  getAllTopTenTerritoriesURL =
    'analytical-dashboard/top-ten-regions-territories';
  getAllQuantityTrendURL = 'analytical-dashboard/quantity-trend';
  getAllRewardTrendURL = 'analytical-dashboard/reward-trend';
  getAllUserTrendURL = 'analytical-dashboard/user-trend';
  getAllTopTenUserURL = 'analytical-dashboard/top-ten-users';
  getAllTopTenProductURL = 'analytical-dashboard/top-ten-products';
  getCurrentSeasonURL =
    'scanning-category-and-region-mappings/by-scan-category-id/';
    _disabledSidebar: EventEmitter<any> = new EventEmitter(true);
    _closedPopup: EventEmitter<any> = new EventEmitter(true);
  utility: any;
  constructor(private http: HttpClient) {}

  setSeasonId(data: any) {
    this.seasonId = data;
  }

  getSeasonId() {
    return this.seasonId;
  }

  setScanningCategoryId(data: any) {
    this.scanningCategoryId = data;
  }

  getScanningCategoryId() {
    return this.scanningCategoryId;
  }

  getAllZonesByBU(data: any) {
    let URL = this.zonesByBUCodeURL + data.scannedCategory;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllRegionByBUAndZoneCodes(data: any) {
    let URL =
      this.regionByBUAndZoneCodes +
      data.scannedCategory +
      '?zoneIds=' +
      data.zoneIds +
      '&isDashboard=' +
      data.isDashboard;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllTerritoriesByRegionCode(data: any) {
    let URL =
      this.territoriesByRegionCodeURL +
      data.scanCategoryId +
      '?regionIds=' +
      data.refgionIds;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  removeSuffix(response : any){   
    // let decryptedRsponse = this.utility.decrypt(response); 
    // const index = decryptedRsponse.indexOf("}#");
    // if (index !== -1) { 
    //   decryptedRsponse = decryptedRsponse.substring(0, index+1);
    //   decryptedRsponse = JSON.parse(decryptedRsponse);
    // }   
     return JSON.parse(response);
  
  } 
  removeHashSuffix(response : any){  
    let decryptedRsponse = this.utility.decrypt(response); 
    const index = decryptedRsponse.indexOf("]#");
    if (index !== -1) { 
      decryptedRsponse = decryptedRsponse.substring(0, index+1);
      decryptedRsponse = JSON.parse(decryptedRsponse);
    }  
    return decryptedRsponse;
  }

  getAllSeasonByBU(data: any) {
    let URL = this.seasonByBU + data.scannedCategory;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllGeographicalCardData(data: any) {
    let URL =
      this.geographicPerformanceURL +
      '?scanCategoryId=' +
      data.scanCategoryId +
      '&scanningCategoryId=' +
      data.scanningCategoryId +
      '&zoneCodes=' +
      data.zoneCodes +
      '&regionCodes=' +
      data.regionCodes +
      '&territoryCodes=' +
      data.territoryCodes;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllQuantityTrendGraph(data: any) {
    let URL =
      this.getAllQuantityTrendURL +
      '?scanCategoryId=' +
      data.scanCategoryId +
      '&scanningCategoryId=' +
      data.scanningCategoryId +
      '&zoneCodes=' +
      data.zoneCodes +
      '&regionCodes=' +
      data.regionCodes +
      '&territoryCodes=' +
      data.territoryCodes;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllRewardTrendGraph(data: any) {
    let URL =
      this.getAllRewardTrendURL +
      '?scanCategoryId=' +
      data.scanCategoryId +
      '&scanningCategoryId=' +
      data.scanningCategoryId +
      '&zoneCodes=' +
      data.zoneCodes +
      '&regionCodes=' +
      data.regionCodes +
      '&territoryCodes=' +
      data.territoryCodes;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllUserTrendGraph(data: any) {
    let URL =
      this.getAllUserTrendURL +
      '?scanCategoryId=' +
      data.scanCategoryId +
      '&scanningCategoryId=' +
      data.scanningCategoryId +
      '&zoneCodes=' +
      data.zoneCodes +
      '&regionCodes=' +
      data.regionCodes +
      '&territoryCodes=' +
      data.territoryCodes;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllTopTenProductGraph(data: any) {
    let URL =
      this.getAllTopTenProductURL +
      '?scanCategoryId=' +
      data.scanCategoryId +
      '&scanningCategoryId=' +
      data.scanningCategoryId +
      '&zoneCodes=' +
      data.zoneCodes +
      '&regionCodes=' +
      data.regionCodes +
      '&territoryCodes=' +
      data.territoryCodes +
      '&filterOption=' +
      data.filterOption;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getCurrentSeasonAndDate(data: any) {
    let URL = this.getCurrentSeasonURL + data.scanCategoryId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getCountWthRoles(body: any) {
    const headers = this.authorizationKey();
    return this.http.post(this.baseUrl + `dashboard/countWthRoles`, body, {
      headers: headers,
      responseType: 'text',
    });
  }

  getPercentageVsDownloading(body: any) {
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseUrl + `/dashboard/scannedVsDownloading`,
      body,
      { headers: headers, responseType: 'text' }
    );
  }
  public uploadImageEcho1( src:string): Observable<any>{
    src= this.removeDoubleQuotes(src); 
    const headers = this.imageAuthorizationKey();
    return this.http.get(src,{ headers: headers,responseType : 'blob' });
  }

  removeDoubleQuotes(str: string): string {
    const result = str.replace(/^"(.*)"$/, '$1');
    return result;
  }
 imageAuthorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.header(currentToken);
    return headers;
  }

  private header(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}` 
    });
  }

  getCropWiseScannedQuantity(body: any) {
    const headers = this.authorizationKey();
    return this.http.post(this.baseUrl + `/dashboard/brandWiseQuantity`, body, {
      headers: headers,
      responseType: 'text',
    });
  }

  getCardCountdata(data:any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl + 'analytical-dashboard/cards-count'+'?scanCategoryId=' +
        data.scanCategoryId +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&zoneCodes=' +
        data.zoneCodes +
        '&regionCodes=' +
        data.regionCodes +
        '&territoryCodes=' +
        data.territoryCodes,
      { headers: headers, responseType: 'text' }
    );
  }

  getTopTenUsers(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        this.getAllTopTenUserURL +
        '?scanCategoryId=' +
        data.scanCategoryId +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&zoneCodes=' +
        data.zoneCodes +
        '&regionCodes=' +
        data.regionCodes +
        '&territoryCodes=' +
        data.territoryCodes,
      { headers: headers, responseType: 'text' }
    );
  }

  getTopTenRegions(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        this.getAllTopTenTerritoriesURL +
        `?scanCategoryId=${data.scanCategoryId}&scanningCategoryId=${data.scanningCategoryId}` +
        '&filterOption=' +
        data.filterOption,
      { headers: headers, responseType: 'text' }
    );
  }
  getTopTenTerritories(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        this.getAllTopTenTerritoriesURL +
        `?scanCategoryId=${data.scanCategoryId}&scanningCategoryId=${data.scanningCategoryId}` +
        '&filterOption=' +
        data.filterOption,
      { headers: headers, responseType: 'text' }
    );
  }

  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
    });
  }
}
