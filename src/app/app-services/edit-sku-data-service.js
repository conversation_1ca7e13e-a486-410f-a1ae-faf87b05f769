"use strict";
let __decorate =
  (this && this.__decorate) ||
  function (decorators, target, key, desc) {
    let c = arguments.length,
      r =
        c < 3
          ? target
          : desc === null
          ? (desc = Object.getOwnPropertyDescriptor(target, key))
          : desc,
      d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
      r = Reflect.decorate(decorators, target, key, desc);
    else
      for (let i = decorators.length - 1; i >= 0; i--)
        if ((d = decorators[i]))
          r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
  };
Object.defineProperty(exports, "__esModule", { value: true });
let BehaviorSubject_1 = require("rxjs/BehaviorSubject");
require("rxjs/Rx");
let core_1 = require("@angular/core");
let EditSKUData = /** @class */ (function () {
  function EditSKUData() {
    this.rowData = new BehaviorSubject_1.BehaviorSubject({});
  }
  EditSKUData = __decorate([core_1.Injectable()], EditSKUData);
  return EditSKUData;
})();
exports.EditSKUData = EditSKUData;
