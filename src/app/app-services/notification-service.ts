import { Injectable } from "@angular/core";
import { environment } from "../../environments/environment";
import { HttpClient, HttpErrorResponse, HttpHeaders } from "@angular/common/http";
import { Observable, throwError } from "rxjs";
import { catchError } from "rxjs/operators";
import { AuthenticationHelper } from "../helpers/authentication";

@Injectable({
  providedIn: "root",
})
export class NotificationService {
  private readonly notificationUrl: string =
    `${environment.baseUrl}` + "adminNotification/send";
  bearerToken: any = AuthenticationHelper.getToken();

  constructor(private http: HttpClient) {}

  addNotification(requestPayload: any): Observable<any> {
    return this.http
      .post<any>(`${this.notificationUrl}`, requestPayload, {
        headers: new HttpHeaders().set(
          "Authorization",
          `Bearer ${this.bearerToken}`
        ),
      })
      .pipe(catchError(this.handleError));
  }

  private handleError(errorRes: HttpErrorResponse) {
    return throwError(errorRes);
  }
}
