import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";
import { Utility } from "../shared/utility/utility";

@Injectable()
export class RegionsService {
  baseurl: any = environment.baseUrl;
  getAllRegionsURL: any = "regions/byprofitcenterzone/";
  getRegionsUrl: any = "regions/all";
  getExportRegionsUrl: any = "regions/export";
  regionsByZoneCodeURL: any = "regions/getRegionsByZone/";
  allRegionsURL: any = "regions/dropdown/byZone";
  amountDistribution :string = 'dashboard/amount-distribution-and-trend-of-week';
  randomNumber:any;
  getBrandsURL: any = "brands/dropdown/ByCrop/";
  regionByBUAndZoneCodes: any = 'regions/drop-down/by-scan-category/';



  constructor(private http: HttpClient,private utility:Utility) {
    const generatedNumber = Math.floor(1000 + Math.random() * 9000);
  this.randomNumber = this.utility.encrypt(generatedNumber.toString());
  }

  getBrands(id: any) {
    let URL = this.getBrandsURL + id;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers : headers,responseType : 'text'
    });
  }

  gettopRetailerByRegion(body: any) {  
    body=this.utility.encrypt(body);
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + `dashboard/topRetailerByRegion`,
      body,
      { headers: headers,responseType:"text" }
    );
  }
  getAllRegionsByZoneCode(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl +  "regions/dropdown/byZone" + "?zoneIds=" + data,
      { headers: headers,responseType : 'text'}
    ); 
  }

  getRegionDropdownDataCalibration(data: any) {
    let URL =
      this.regionByBUAndZoneCodes +
      data.scanCategoryID;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  updateRegionByZoneId(data: any) {
    const headers = this.authorizationKey();

    let URL: string = "";
    let zoneIds: any = [];
    for (let i = 0; i < data.length; i++) {
      zoneIds.push(`zoneIds=${data[i]}&`);
    }
    if (data.length == 0) {
      URL = this.allRegionsURL + "?zoneIds=";
    } else {
      let zoneParams = zoneIds.join();
      URL = (this.allRegionsURL + `?` + zoneParams).replace(/,/g, "");
    }
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }

  getAllRegions(data: any) {
    const headers = this.authorizationKey();
    let URL =
      this.getRegionsUrl +
      "?page=" +
      data.currentPage +
      "&size=" +
      data.pageLimit +
      "&searchedValue=" +
      data.searchedValue;
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text'});
  }

 getRegion() {
    const headers = this.authorizationKey();
    const URL =
      this.baseurl +
      'region/get-all'
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllRegionsExportData(data:any) {
   let URL =
   this.getRegionsUrl +
   "?page=0" +
   "&size=1" +
   "&searchedValue=" +
   data.searchedValue;;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,responseType : 'text'
    });
  }
 

  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
      "Content-Type":"application/json",
      'randNum' : `${this.randomNumber}`
    });
  }

  getAllFY() {
    const headers = this.authorizationKey();
    const URL =
      this.baseurl +
      'dashboard/get-all-financial-year'
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }
    getAllZonesByPC(data: any) {
    const headers = this.authorizationKey();
    const URL =
      this.baseurl +
      'zone/get-all/' + data.zoneId
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }

   getTopRegion(data: any) {
    const headers = this.authorizationKey();
    const URL = this.baseurl +
      'dashboard/get-top-region' +
      '?regionId=' + data.regionId +
       '&yearId=' + data.yearId
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }
  getTopZone(data: any) {
    const headers = this.authorizationKey();
    const URL = this.baseurl +
      'dashboard/get-top-zone' +
      '?regionId=' + data.regionId +
       '&yearId=' + data.yearId
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }
  getTopLeader(data: any) {
    const headers = this.authorizationKey();
    const URL = this.baseurl +
      'dashboard/get-top-leader' +
      '?regionId=' + data.regionId +
       '&yearId=' + data.yearId
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }
}

