import { HttpClient, HttpHeaders } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Utility } from '../shared/utility/utility';

@Injectable({
  providedIn: 'root',
})
export class SeasonService {
  _slabDialog: EventEmitter<any> = new EventEmitter(true);
  slabDialogProcessed: boolean = false;
  baseurl: any = environment.baseUrl;
  getAllCalibrationSeasonUrl: any = 'scanning-categories/by-scan-category';
  getAllBusinessUnitUrl: any = 'scanning-categories/all';
  getAllCreditNotesUrl: any = 'credit-note-mappings/by-scan-category';
  addSeasonUrl: any = 'scanning-categories';
  addSCropsUrl: any = 'credit-note-mappings';
  updateScanningtoggle: any =
    'scanning-category-and-region-mappings/toggle-scanning/';
  updateRedemptiontoggle: any =
    'scanning-category-and-region-mappings/toggle-redemption/';
  getAllSeasonUrl: any = 'seasons';
  getAllCropsUrl: any = 'crops';
  private eventSubject = new ReplaySubject<any>(1);
  redirectCrop: any;

  constructor(private http: HttpClient, private utility: Utility) {}

  // Method to emit an event
  emitEvent(data: any) {
    this.eventSubject.next(data);
  }

  // Method to subscribe to the event
  getEvent() {
    return this.eventSubject.asObservable();
  }

  getRedirection() {
    return this.redirectCrop;
  }

  setRedirection(data: any) {
    this.redirectCrop = data;
  }
  

  returnAllSeasonData() {
    let URL = this.getAllSeasonUrl;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnAllCropsData() {
    let URL = this.getAllCropsUrl;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnAllCalibrationSeasonData(data: any) {
    let URL = this.getAllCalibrationSeasonUrl + '/' + data.scanCategoryId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnAllBusinessUnitData(data: any) {
    let URL =
      this.getAllBusinessUnitUrl + '?scanCategoryId=' + data.scanCategoryId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnAllCreditNotes(data: any) {
    let URL =
      this.getAllCreditNotesUrl + '/' + data.scanCategoryId + "?transferType=" + data.transactionType+"&unPaged="+data.unPaged+"&page="+data.currentPage+"&size="+data.pageLimit;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnSubmitRequest(data: any) {
    let URL = this.addSeasonUrl;
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + URL, data, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnSubmitCropData(data: any) {
    let URL = this.addSCropsUrl;
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + URL, data, {
      headers: headers,
      responseType: 'text',
    });
  }

  returnUpdateRequest(data: any) {
    let URL = this.updateScanningtoggle;
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl +
        URL +
        data.scanningCategoryAndRegionMappingID +
        '?isScanningActive=' +
        data.isScanningActive,
      {
        headers: headers,
        responseType: 'text',
      }
    );
  }

  returnRedemptionUpdateRequest(data: any) {
    let URL = this.updateRedemptiontoggle;
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl +
        URL +
        data.scanningCategoryAndRegionMappingID +
        '?isRedemptionActive=' +
        data.isRedemptionActive,
      {
        headers: headers,
        responseType: 'text',
      }
    );
  }

  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
    });
  }
}

