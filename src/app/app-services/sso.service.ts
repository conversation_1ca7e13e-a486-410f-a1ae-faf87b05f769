import { Injectable } from '@angular/core';
import { BaThemeSpinner } from '../theme/services/baThemeSpinner/baThemeSpinner.service';
import { AdminService } from './admin.service';
import { Utility } from '../shared/utility/utility';

@Injectable({
  providedIn: 'root'
})
export class SsoService {

  router: any;
  randomNumber!: number;
  constructor(private adminService:AdminService,private utility:Utility ) { 
    this.randomNumber = Math.floor(1000 + Math.random() * 9000);
  }

  ssoLogin(): void {
    this.adminService.fetchSsoInfo().subscribe({
      next: (res: any) => {
        const encryptedBody = res?.encryptedBody;
        const decrypted = this.utility.decrypt(encryptedBody);
  
        try {
          const parsed = JSON.parse(decrypted);
          this.successLogin(parsed);
        } catch (e) {
          this.errorHandler('Decryption failed');
        }
      },
      error: (error: any) => {
        this.errorHandler(error);
      }
    });
  }
  
  
  

  errorHandler(error: any): void {}
  logout(authToken?: any): void {
    this.adminService.userLogout().subscribe({
     next : ((data : any) => {
        this.logOutSuccess(data, authToken);
      }),
      error : (error: any) => {
        this.errorHandler(error);
      }
    });
  }

  logOutSuccess(data: any, authToken?: any): void { 
    localStorage.clear(); 
    this.adminService.fetchSsoInfo().subscribe({
     next : (response: any) => this.successLogout(response),
     error : (error : any) => this.errorHandler(error)
    });
  }
  successLogin(item: any): void {  
    const loginUrl: string = item.loginUrl;
    const logOutUrl: string = item.logoutUrl;
    if (loginUrl) {
      localStorage.setItem("loginUrl", loginUrl);
      window.location.href = loginUrl;
    }
    if (logOutUrl) {
      localStorage.setItem("logOutUrl", logOutUrl);
    }
  }
  successLogout(item: any): void {
    const logOutUrl: string = item.logoutUrl;
    if (logOutUrl) {
      this.router.navigate(['']);
      localStorage.clear();
      // this.spinner.hide();
    }
  }
}
