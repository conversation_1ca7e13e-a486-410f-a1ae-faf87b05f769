import { EventEmitter, Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Utility } from '../shared/utility/utility';

@Injectable({
  providedIn: 'root',
})
export class SupportService {
  baseUrl = environment.baseUrl;
  getAllSupportDataUrl = 'support-tickets';
  PostAllSupportDataUrl= 'update/support-tickets'
  getAllCategoryDataUrl = 'support-ticket-types';
  getAllStatusDataUrl = 'support-ticket-status';
  _toggleButton: EventEmitter<any> = new EventEmitter(true);
  _supportTIcketCount: EventEmitter<any> = new EventEmitter(true);
  activeButton: string | undefined;
  buttonState: string = 'new';

  constructor(private http: HttpClient, private utility: Utility) {}

  getButtonState() {
    return this.buttonState;
  }

  setButtonState(data: any) {
    this.buttonState = data;
  }

  activeButtonSetter(data: string) {
    this.activeButton = data;
  }

  activeButtonGetter() {
    return this.activeButton;
  }

  downloadTemplate() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + 'customers/download-template', {
      responseType: 'blob',
      headers: headers,
    });
  }

  getProcessingTickets(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        this.getAllSupportDataUrl +
        '?isProcessing=' +
        data.isProcessing +
        '&page=' +
        data.currentPage +
        '&size=' +
        data.pageSize +
        '&startDate=' +
        data.startDate +
        '&endDate=' +
        data.endDate +
        '&searchValue=' +
        data.searchedValue +
        '&category=' +
        data.category,
      { headers: headers, responseType: 'text' }
    );
  }  

  downloadFile(file: any) {
    const headers = this.authorizationKey();
    const url = file;
    return this.http.get(url, { responseType: 'blob', headers: headers });
  }

  getResolvedTickets(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        this.getAllSupportDataUrl +
        '?isProcessing=' +
        data.isProcessing +
        '&page=' +
        data.currentPage +
        '&size=' +
        data.pageSize +
        '&startDate=' +
        data.startDate +
        '&endDate=' +
        data.endDate +
        '&searchValue=' +
        data.searchedValue +
        '&category=' +
        data.category,
      { headers: headers, responseType: 'text' }
    );
  }

  getAllCategoryData() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + this.getAllCategoryDataUrl, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllStatusData() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + this.getAllStatusDataUrl, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllSupportExportData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        this.getAllSupportDataUrl +
        `?isProcessing=` +
        data.status +
        '&page=' +
        data.currentPage +
        '&size=' +
        data.pageSize +
        '&startDate=' +
        data.startDate +
        '&endDate=' +
        data.endDate +
        '&searchValue=' +
        data.searchedValue +
        '&category=' +
        data.category,
      { headers: headers, responseType: 'text' }
    );
  }

  getUpdateTicket(body: any) {
    const headers = this.authorizationKey();
    // Encrypt the body before sending
    const encryptedBody = this.utility.encryptString(body);
    return this.http.post(this.baseUrl + this.PostAllSupportDataUrl, encryptedBody, {
      headers: headers,
      responseType: 'text',
    });
  }

  // Add a general method for POST requests with encrypted payload
  postWithEncryption(url: string, body: any) {
    const headers = this.authorizationKey();
    // Encrypt the body before sending
    const encryptedBody = this.utility.encrypt(JSON.stringify(body));
    return this.http.post(this.baseUrl + url, encryptedBody, {
      headers: headers,
      responseType: 'text',
    });
  }

  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
    });
  }

  ngOnDestroy() {
    localStorage.removeItem('brand_id');
  }
}
