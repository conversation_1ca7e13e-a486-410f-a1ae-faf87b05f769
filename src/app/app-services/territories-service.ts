import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "../../environments/environment";
import { Observable } from "rxjs";
import { Utility } from "../shared/utility/utility";

@Injectable()
export class TerritoriesService {
  baseurl: any = environment.baseUrl;
  getAllCustomersAdminURL: any = "customerByTerritory/";
  allTerritoriesURL: any = "territories/all";
  allTerritoriesExportURL: any = "territories/export";
  getAllCustomersURL: any = "customerByTerritory/byuser/";
  territoryProfitCentertURL: any = "profitcenter/territory/byuserid/";
  custDetailsByIDURL: any = "customerByTerritory/customer/";
  territoriesByProfitCenterURL: any = "territories/";
  addCustomerDetailsURL: any = "territories/";
  territoriesByZoneCodeURL: any = "territories/byZone";
  territoriesByRegionCodeURL: any = "territories/byRegion";
  topRetailerByTerritory: any = "dashboard/overallSalesScanned";
  retailerByTerritory: any = "retailer/byTerritory/dropdown";

  randomNumber: number | string;

constructor(private http: HttpClient, private utility: Utility) {
  const generatedNumber = Math.floor(1000 + Math.random() * 9000);
  this.randomNumber = this.utility.encrypt(generatedNumber.toString());
}

  getBrandList(body: any) {
    body=this.utility.encrypt(body)
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + `dashboard/listBrands`, body, {
      headers: headers,responseType:"text"
    });
  }

  top10TableListByTerritory(body: any) { 
    body=this.utility.encrypt(body);
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + `dashboard/topRetailerByTerritory`,
      body,
      { headers: headers,responseType : 'text' }
    );
  }

  top10TableListByMonth(body: any) {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + `dashboard/topRetailerByMonth`, {
      headers: headers, responseType  :'text'
    });
  }

  getTop10RetailerDistrict(body: any) { 
    body=this.utility.encrypt(body)
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + `dashboard/topRetailerByDistrict`,
      body,
      { headers: headers,responseType:"text" }
    );
  }

  getSlabCountTableList(body: any,brandCode : any) {
    body = this.utility.encrypt(body);
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + `dashboard/slabCount/${brandCode}`, body, {
      headers: headers,responseType:"text"
    });
  }

  getRetailerForAdminZone() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + `retailer/dropdown`, {
      headers: headers,responseType:"text"
    });
  }

  getRetailerDataByTerritory( ) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl + `retailers/dropdown`,
      { headers: headers,responseType:"text"  }
    );
  }

  getRetailerByRegion(regionCode: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl + `retailer/dropdown?regionCode=${regionCode}`,
      { headers: headers,responseType:"text" }
    );
  }

  getDataByRetailer(body: any) {
    body=this.utility.encrypt(body);
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + `dashboard/trendInWeek`, body, {
      headers: headers,responseType:"text"
    });
  }

  getDataoverallPoints(body: any) {
    body= this.utility.encrypt(body);
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + `dashboard/overallPoints`, body, {
      headers: headers,responseType:"text"
    });
  }

  getAllTerritoriesAdmin(data: any): Observable<any> {
    let URL =
      this.allTerritoriesURL +
      "?page=" +
      data.currentPage +
      "&size=" +
      data.pageLimit +
      "&searchedValue=" +
      data.searchedValue 
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }

  getAllTerritoriesExportData(data:any) {
    let URL =
    this.allTerritoriesURL +
    "?page=0" +
    "&size=1" +
    "&searchedValue=" +
    data.searchedValue 
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }

  getTerrProfitCenter(res: any) {
    let URL = this.territoryProfitCentertURL + res.userID;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }

  territoriesDetailByUserID(data: any) {
    let URL = this.custDetailsByIDURL + data.custID;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers ,responseType : 'text'});
  }

  getTerritoriesByProfitCenter(data: any) {
    let URL = this.territoriesByProfitCenterURL + data;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }

  addCustDetails(body: any) {
    let URL = this.addCustomerDetailsURL;
    const headers = this.authorizationKey();

    return this.http.post(this.baseurl + URL + body.territoriesId, body, {
      headers: headers,responseType : 'text'
    });
  }
  addCustDetailss(body: any) {
    let URL = this.addCustomerDetailsURL;
    const headers = this.authorizationKey();

    return this.http.post(this.baseurl + URL, body, { headers: headers ,responseType : 'text'});
  }

  getAllTerritoriesByZoneCode(data: any) {
    let URL = this.territoriesByZoneCodeURL + "?zoneCode=" + data;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }

  getAllTerritoriesByRegionCode(data: any) {
    let URL = this.territoriesByRegionCodeURL + "?regionCode=" + data;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text'  });
  }

  getAllTerritoryData(data : any){
     let URL = this.territoriesByRegionCodeURL + "?regionCode=" + data;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }
  getAllTerritories() {
    let URL = this.territoriesByRegionCodeURL + "?regionCode=";
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }
  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;  
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
      'Content-Type':"application/json",
      'randNum' : `${this.randomNumber}`
    });
  }
}
