import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { firstValueFrom } from 'rxjs';
import { Utility } from '../shared/utility/utility';

@Injectable({
  providedIn: 'root'
})
export class AppTranslateService {
  constructor(private translate: TranslateService, private utility: Utility,) {}

  async loadLanguage(): Promise<void> {
    const lang = this.utility.decryptLSDAta(localStorage.getItem('currentLanguage') || 'en');
    // const lang = localStorage.getItem('currentLanguage') || 'en';
    this.translate.setDefaultLang(lang);
    await firstValueFrom(this.translate.use(lang));
  }

  changeLanguage(lang: string) {
    localStorage.setItem('currentLanguage', lang);
    this.translate.use(lang).subscribe({
      next: () => console.log('Language successfully changed to:', lang),
      error: (err) => console.error('Error changing language:', err)
    });
  }  

  getCurrentLanguage(): string {
    return this.translate.currentLang || 'en';
  }
}
