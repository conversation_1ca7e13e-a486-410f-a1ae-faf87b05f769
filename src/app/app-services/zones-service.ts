import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";
import { Utility } from "../shared/utility/utility";

@Injectable()
export class ZonesService {
  zonesURL: any = "zones/dropdown";
  zonesBySBUCodeURL: any = "zone/bySbu";
  getZoneURL: any = "zones/all";
  getZoneExportURL: any = "zones/export";
  baseurl: any = environment.baseUrl;

  constructor(private http: HttpClient,  private utility: Utility) {}

  getAllZones(data: any) {
    let URL =
      this.getZoneURL +
      "?page=" +
      data.currentPage +
      "&size=" +
      data.pageLimit +
      "&searchedValue=" +
      data.searchedValue 
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }

  getAllZonesExportData(data:any) {
    let URL =
    this.getZoneURL + "?page=0" +
    "&size=1" +
    "&searchedValue=" +
    data.searchedValue;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }

  getAllZonesByPC() {
    let URL = "zones/dropdown";
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }

  getAllZonesBySBU(data: any) {
    let URL = this.zonesBySBUCodeURL + "?sbuCode=" + data;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }

  getLoggedInUserZone(data: any) {
    let URL = this.getZoneURL + data.params + "/" + data.id;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers,responseType : 'text' });
  }
  addUpdateZone(body: any) {
    let URL = this.getZoneURL + "/";
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + URL, body, { headers: headers,responseType : 'text' });
  }
  addZoneForm(body: any) {
    let URL = this.getZoneURL + "/";
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + URL, body, { headers: headers,responseType : 'text' });
  }

  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;  
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }
  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
      'Content-Type': 'application/json'
    });
  }
}
