// @import "./theme/sass/conf/conf";
// @import "./theme/components/baSidebar/baSidebar";

// ::ng-deep {
//   @mixin layout-collapsed() {
//     .al-main {
//       margin-left: 50px;
//       background-color: #fff;
//     }

//     .al-main-TM-50 {
//       margin-left: 50px;
//     }

//     .al-footer {
//       padding-left: 83px;
//     }
//   }

//   @mixin sidebar-collapsed() {
//     .al-sidebar {
//       width: 52px;

//       .fa-angle-down,
//       .fa-angle-up {
//         opacity: 0;
//       }

//       .al-sidebar-sublist {
//         position: absolute;
//         top: -1px;
//         left: 52px;
//         @include bg-translucent-dark(0.8);
//         width: 0;
//         display: block;
//         overflow: hidden;
//         transition: width 0.5s ease;

//         &.slide-right {
//           width: 135px;
//         }

//         &::before {
//           display: none;
//         }

//         li {
//           &::before {
//             display: none;
//           }

//           a {
//             padding-left: 18px;
//             padding-right: 18px;
//             min-width: 130px;
//             white-space: nowrap;
//           }
//         }
//       }

//       .sidebar-hover-elem,
//       .sidebar-select-elem {
//         left: 48px;
//       }
//     }
//   }

//   @mixin sidebar-overlap() {
//     .al-sidebar {
//       width: 210px;
//       @include bg-translucent-dark(0.75);
//       transition: width 0.5s ease;

//       .fa-angle-down,
//       .fa-angle-up {
//         opacity: 1;
//       }

//       .al-sidebar-sublist {
//         @include default-sublist();
//         top: auto;
//         left: auto;
//         background: none;
//         width: auto;
//         overflow: visible;
//         transition: none;
//       }

//       .sidebar-hover-elem,
//       .sidebar-select-elem {
//         left: 210px - 4;
//         transition: left 0.5s ease;
//       }
//     }
//   }

//   @mixin sidebar-hidden() {
//     .al-sidebar {
//       width: 0;
//     }

//     .sidebar-hover-elem,
//     .sidebar-select-elem {
//       display: none;
//     }
//   }

//   @media (min-width: 1200px) {
//     .menu-collapsed {
//       @include layout-collapsed();

//       .al-content {
//         width: calc(100% - 50px);
//       }
//     }
//   }

//   @media (max-width: 1200px) and (min-width: $resXS) {
//     @include layout-collapsed();

//     .al-content {
//       width: calc(100% - 50px);
//     }
//   }

//   @media (min-width: $resXS) {
//     .menu-collapsed {
//       @include sidebar-collapsed();
//     }
//   }

//   @media (max-width: 1200px) {
//     @include sidebar-overlap();
//   }

//   @media (max-width: $resXS) {
//     .menu-collapsed {
//       @include sidebar-hidden();

//       .al-content {
//         width: calc(100%);
//       }
//     }

//     .al-main {
//       margin-left: 0;
//     }

//     .al-main-TM-50 {
//       margin-left: 0;
//     }

//     .al-footer {
//       padding-left: 0;
//     }
//   }

//   @media (max-width: $resXS) {
//     .al-content {
//       width: calc(100%);
//     }
//   }
// }
