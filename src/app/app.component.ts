import {
  Component,
  ChangeDetectorRef,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  AfterViewChecked,
} from "@angular/core";
import { GlobalState } from "./helpers/global.state";
import { ToastrService } from "ngx-toastr";
import { NgClass } from "@angular/common";
import { RouterOutlet } from "@angular/router";
import { AngularMultiSelectModule } from "angular2-multiselect-dropdown";
import { UserService } from "./app-services/user-service";
import { filter, Subscription } from "rxjs";

@Component({
  selector: "app",
  styleUrls: ["./app.component.scss"],
  template: `
    <main [ngClass]="{ 'menu-collapsed': isMenuCollapsed }">
      <div class="additional-bg"></div>
      <router-outlet></router-outlet>
    </main>
  `,
  standalone: true,
  imports: [NgClass, RouterOutlet, AngularMultiSelectModule],
})
export class App implements OnInit, AfterViewChecked, <PERSON><PERSON><PERSON><PERSON> {
  isMenuCollapsed: boolean = false;
  private roleSubscription!: Subscription;

  constructor(
    private cdRef: ChangeDetectorRef,
    private state: GlobalState,
    public toastr: ToastrService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.roleSubscription = this.userService.userRole$
      .pipe(filter((role) => !!role))
      .subscribe((role: string) => {
      });
  
    // Fetch role on initial load
    this.userService.fetchUserRole();
  
    // Subscribe to menu collapse state
    this.state.subscribe("menu.isCollapsed", (isCollapsed: boolean) => {
      this.isMenuCollapsed = isCollapsed;
    });
  }

  ngAfterViewChecked() {
    this.cdRef.detectChanges();
  }

  ngOnDestroy(): void {
    this.roleSubscription?.unsubscribe();
  }
}
