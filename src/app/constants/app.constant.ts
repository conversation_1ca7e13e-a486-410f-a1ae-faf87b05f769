export class AppConstant {
  static SUPPORTDA<PERSON>_FETCH_ERROR(SUPPORT<PERSON><PERSON>_FETCH_ERROR: any) {
    throw new Error('Method not implemented.');
  }
  static LOYALTY_IMAGE = "assets/img/Logo_ADV_Pantone.png";
  static USER_DEFAULT_IMAGE = "assets/img/default-user.png";

  static USER_LOGIN_SUCCESS = "Signed In Successfully";
  static USER_LOGIN_ERROR = "Error while Signin";
  static USER_DELETE_SUCCESS = "User deleted successfully";
  static USER_DELETE_ERROR = "Error while deleting user";
  static USER_FETCH_ERROR = "Error while fetching user details";
  static DISTRIBUTOR_FETCH_ERROR = "Error while fetching distributors";
  static RETAILER_FETCH_ERROR = "Error while fetching retailers";
  static FARMER_FETCH_ERROR = "Error while fetching farmers";
  static EXPORT_ERROR = "Error while exporting data";
  static USER_CREATE_SUCCESS = "User created successfully";
  static USER_UPDATE_SUCCESS = "User updated successfully";
  static PASS_UPDATE_SUCCESS = "Password updated successfully";
  static USER_UPDATE_ERROR = "Error while updating user";
  static PASS_UPDATE_ERROR = "Error while updating password";
  static USER_CREATE_ERROR = "Error while creating user";
  static USER_ACTIVE_STATUS_SUCCESS = "User activated successfully";
  static USER_INACTIVE_STATUS_SUCCESS = "User deactivated successfully";
  static USER_UPDATE_STATUS_SUCCESS = "User status updated successfully";
  static USER_UPDATE_STATUS_ERROR = "Error while updating user status";

  static SBU_FETCH_ERROR = "Error while fetching sbu details";

  static ZONE_CREATE_SUCCESS = "Zone created successfully";
  static ZONE_UPDATE_SUCCESS = "Zone updated successfully";
  static ZONE_UPDATE_ERROR = "Error while updating zone";
  static ZONE_CREATE_ERROR = "Error while creating zone";
  static ZONE_FETCH_ERROR = "Error while fetching zone details";

  static REGION_CREATE_SUCCESS = "Region created successfully";
  static REGION_UPDATE_SUCCESS = "Region updated successfully";
  static REGION_UPDATE_ERROR = "Error while updating region";
  static REGION_CREATE_ERROR = "Error while creating region";
  static REGION_FETCH_ERROR = "Error while fetching region details";

  static PERCENTAGE_GROWTH_CREATE_SUCCESS = "Data added successfully";
  static PERCENTAGE_GROWTH_UPDATE_SUCCESS = "Information updated successfully";
  static PERCENTAGE_GROWTH_UPDATE_ERROR = "Error while updating data";
  static PERCENTAGE_GROWTH_CREATE_ERROR = "Error while adding information";
  static PERCENTAGE_GROWTH_FETCH_ERROR = "Error while fetching details";

  static SKU_ADD_ERROR = "Error while adding the SKU details";
  static SKU_ADD_SUCCESS = "SKU details added successfully";
  static SKU_UPDATED_SUCCESS = "SKU details updated successfully";
  static SKU_UPDATE_ERROR = "Error while updating SKU details";

  static FINANCIAL_YEAR_FETCHING_ERROR =
    "Error while fetching financial year details";

  static PREV_YEAR_SALES_FETCHING_ERROR =
    "Error while fetching previous year sales";
  static SALES_FETCHING_ERROR = "Error while fetching the sales data";

  static TERRITORY_FETCHING_ERROR = "Error while fetching territory details";

  static DISTRICT_FETCHING_ERROR = "Error while fetching district details";

  static BRANDS_FETCHING_ERROR = "Error while fetching brands details";

  static CUSTOMER_FETCHING_ERROR = "Error while fetching the territories";
  static BRAND_SKU_ERROR = "Error while fetching the sku details";
  static BRAND_SKU_UPDATE_ERROR = "Error while updating the details";

  static IMAGE_UPLOAD_MSG = 'Image uploaded successfully';


  static CUST_FETCHING_ERR = "Error while fetching the customer details";
  static CUST_ADDITION_ERROR = "Error while adding the customer details";
  static CUST_ADDITION_SUCCESS = "Customer added successfully";
  static CUST_CREATION_SUCCESS = "Customer udpated successfully";
  static CUST_CREATION_ERROR = "Error while updating the customer details";
  static CUST_TYPE_FETCHING_ERROR = "Error while fetching customer types";

  static BRANDS_ADDED_MSG = "Brand added successfully";
  static BRAND_UPDATE_MSG = "Brand updated successfully";
  static BRAND_UPDATE_ERR = "Error while updating the brand details";
  static BRAND_FETCHING_ERROR = "Error while fetching brand details";
  static BRAND_ADD_ERR = "Error while adding brand details";

  static ERR_FETCHING_BRANDS = "Error while fetching the brands";

  static NETWORK_ERROR =
    "Please check your Internet connection or try again after sometime";

  static PER_PAGE_ITEMS: number = 20;

  static PC_AF_CODE = "DO1301";
  static PC_SWAL_CODE = "DO1401";

  static ADD_SCHEME_SUCCESS = "Scheme added successfully";
  static ADD_SCHEME_ERR = "Error while adding scheme";
  static UPDATE_SCHEME_SUCCESS = "Scheme updated successfully";
  static DELETE_SCHEME_SUCCESS = "Scheme deleted successfully";
  static UPDATE_SCHEME_ERR = "Error while updating scheme";
  static GET_SCHEME_ERR = "Error while getting schemes";
  static FETCH_SCHEME_ERR = "Error while getting scheme details";
  static DELETE_SCHEME_ERR = "Scheme deleted successfully";
  static HISTORY_FETCH_ERR = "Error while fetching reward history";

  static OVERALLSCANNED_CHART =
    "Error while fetching overall scanned chart data";
  static TERRITORY_DATA = "Error while fetching territory data";

  static STATE_FETCH_ERR = "Error while fetching state names";
  static Traceability_FETCH_ERROR = "Error while fetching traceability details";
  static NOTIFICATION_SUCCESS = "Notification Sent Successfully";
  static NOTIFICATION_ERROR = "Error while sending notification";
}
