import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { AuthenticationHelper } from "../helpers/authentication";
@Injectable()
export class LoginGuard  {
  constructor(private router: Router) {}
  canActivate() {
    if (AuthenticationHelper.isLoggedIn()) {
      return true;
    } else {
      this.router.navigate([""]);
      return false;
    }
  }
}
@Injectable()
export class AuthGuard  {
  constructor(private router: Router) {}
  canActivate() {
    if (!AuthenticationHelper.isLoggedIn()) {
      return true;
    } else {
      let roleID = parseInt(AuthenticationHelper.getRoleID());
      switch (roleID) {
        case 1:
          this.router.navigate(["dashboard"]);
          break;
        case 2:
          this.router.navigate(["dashboard"]);
          break;
        case 3:
          this.router.navigate(["dashboard"]);
          break;
        case 4:
          this.router.navigate(["dashboard"]);
          break;
        case 5:
          this.router.navigate(["zones"]);
          break;
        case 6:
          this.router.navigate(["sbu"]);
          break;
        default:
          this.router.navigate(["sign-in"]);
          break;
      }
      return false;
    }
  }
}

@Injectable()
export class AdminGuard  {
  constructor(private router: Router) {}

  canActivate() {
    let roleID = parseInt(AuthenticationHelper.getRoleID());

    if (AuthenticationHelper.isLoggedIn()) {
      return true;
    } else {
      switch (roleID) {
        case 1:
          this.router.navigate(["dashboard"]);
          break;
        case 2:
          this.router.navigate(["dashboard"]);
          break;
        case 3:
          this.router.navigate(["dashboard"]);
          break;
        case 4:
          this.router.navigate(["dashboard"]);
          break;
        case 5:
          this.router.navigate(["zones"]);
          break;
        case 6:
          this.router.navigate(["sbu"]);
          break;
        default:
          this.router.navigate(["sign-in"]);
          break;
      }
      return false;
    }
  }
}
