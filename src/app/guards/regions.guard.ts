import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { AuthenticationHelper } from "../helpers/authentication";
@Injectable()
export class RegionsGuard  {
  constructor(private router: Router) {}
  canActivate() {
    let roleID = parseInt(AuthenticationHelper.getRoleID());
    let pc = AuthenticationHelper.getProfitCenterName();
    if (
      (AuthenticationHelper.isLoggedIn() && roleID === 1) ||
      roleID === 3 ||
      roleID === 4
    ) {
      return true;
    } else {
      this.router.navigate([""]);
      return false;
    }
  }
}
