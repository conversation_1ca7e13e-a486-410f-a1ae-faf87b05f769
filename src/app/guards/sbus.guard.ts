import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { AuthenticationHelper } from "../helpers/authentication";
@Injectable()
export class SbusGuard  {
  constructor(private router: Router) {}
  canActivate() {
    let roleID = parseInt(AuthenticationHelper.getRoleID());
    if ((AuthenticationHelper.isLoggedIn() && roleID === 1) || roleID === 5) {
      return true;
    } else {
      this.router.navigate([""]);
      return false;
    }
  }
}
