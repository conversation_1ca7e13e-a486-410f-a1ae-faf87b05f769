import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { AuthenticationHelper } from "../helpers/authentication";
@Injectable()
export class ZonesGuard  {
  constructor(private router: Router) {}
  canActivate() {
    let roleID = parseInt(AuthenticationHelper.getRoleID());
    if (
      (AuthenticationHelper.isLoggedIn() && roleID === 1) ||
      roleID === 4 ||
      roleID === 5
    ) {
      return true;
    } else {
      this.router.navigate([""]);
      return false;
    }
  }
}
