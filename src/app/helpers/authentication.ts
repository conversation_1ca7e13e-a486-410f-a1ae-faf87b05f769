import * as CryptoJS from 'crypto-js';
import { environment } from 'src/environments/environment';
import { Utility } from '../shared/utility/utility';

export class AuthenticationHelper {
private constructor() {}
private static utility: Utility;
// Initialize utility instance
private static getUtility(): Utility {
if (!this.utility) {
this.utility = new Utility();
}
return this.utility;
}

/**
* To set user token in localStorage
* @param token
*/
static setToken(token: any) {
const utility = this.getUtility();
const encryptedToken = utility.encrypt(typeof token === 'string' ? token : JSON.stringify(token));
localStorage.setItem('token', encryptedToken);
}

/**
* To return token from localStorage
* @returns {string|null}
*/
static getToken() {
const utility = this.getUtility();
const encryptedToken = localStorage.getItem('token');
if (!encryptedToken) return null;
try {
return utility.decrypt(encryptedToken);
} catch (error) {
console.error('Failed to decrypt token:', error);
return null;
}
}

static setUserEditId(id: any) {
  localStorage.setItem('editUserId', id);
}

static getUserEditId() {
  return localStorage.getItem('editUserId');
}

static setUsername(firstName: any) {
const utility = this.getUtility();
const encryptedFirstName = utility.encrypt(typeof firstName === 'string' ? firstName : JSON.stringify(firstName));
localStorage.setItem('firstName', encryptedFirstName);
}

static getUsername() {
const utility = this.getUtility();
const encryptedFirstName = localStorage.getItem('firstName');
if (!encryptedFirstName) return null;
try {
return utility.decrypt(encryptedFirstName);
} catch (error) {
console.error('Failed to decrypt firstName:', error);
return null;
}
}

static setEmail(email: any) {
const utility = this.getUtility();
const encryptedEmail = utility.encrypt(typeof email === 'string' ? email : JSON.stringify(email));
localStorage.setItem('email', encryptedEmail);
}

static getEmail() {
const utility = this.getUtility();
const encryptedEmail = localStorage.getItem('email');
if (!encryptedEmail) return null;
try {
return utility.decrypt(encryptedEmail);
} catch (error) {
console.error('Failed to decrypt email:', error);
return null;
}
}

// static setRoleID(id: any) {
// const utility = this.getUtility();
// const encryptedId = utility.encrypt(typeof id === 'string' ? id : JSON.stringify(id));
// localStorage.setItem('roleID', encryptedId);
// }

  static getRoleID(): string {
    return localStorage.getItem("roleID")!;
  }

  static setRoleID(id: any) {
    localStorage.setItem("roleID", id);
  }
  static setUserID(id: any) {
    localStorage.setItem("userID", id);
  }

  static getUserID() {
    return localStorage.getItem("userID");
  }

static setProfitCenter(data: any) {
const utility = this.getUtility();
const encryptedData = utility.encrypt(typeof data === 'string' ? data : JSON.stringify(data));
localStorage.setItem("profitCenter", encryptedData);
}

static getProfitCenter() {
const utility = this.getUtility();
const encryptedData = localStorage.getItem("profitCenter");
if (!encryptedData) return null;
try {
return utility.decrypt(encryptedData);
} catch (error) {
console.error('Failed to decrypt profitCenter:', error);
return null;
}
}

static setProfitCenterName(data: any) {
const utility = this.getUtility();
const encryptedData = utility.encrypt(typeof data === 'string' ? data : JSON.stringify(data));
localStorage.setItem("profitCenterName", encryptedData);
}

static getProfitCenterName() {
const utility = this.getUtility();
const encryptedData = localStorage.getItem("profitCenterName");
if (!encryptedData) return null;
try {
return utility.decrypt(encryptedData);
} catch (error) {
console.error('Failed to decrypt profitCenterName:', error);
return null;
}
}

static setZoneCode(code: any) {
const utility = this.getUtility();
const encryptedCode = utility.encrypt(typeof code === 'string' ? code : JSON.stringify(code));
localStorage.setItem("zoneCode", encryptedCode);
}

static getZoneCode() {
const utility = this.getUtility();
const encryptedCode = localStorage.getItem("zoneCode");
if (!encryptedCode) return null;
try {
return utility.decrypt(encryptedCode);
} catch (error) {
console.error('Failed to decrypt zoneCode:', error);
return null;
}
}

static setRegionCode(code: any) {
const utility = this.getUtility();
const encryptedCode = utility.encrypt(typeof code === 'string' ? code : JSON.stringify(code));
localStorage.setItem("regionCode", encryptedCode);
}

static getRegionCode() {
const utility = this.getUtility();
const encryptedCode = localStorage.getItem("regionCode");
if (!encryptedCode) return null;
try {
return utility.decrypt(encryptedCode);
} catch (error) {
console.error('Failed to decrypt regionCode:', error);
return null;
}
}

static setTerritoryCode(code: any) {
const utility = this.getUtility();
const encryptedCode = utility.encrypt(typeof code === 'string' ? code : JSON.stringify(code));
localStorage.setItem("territoryCode", encryptedCode);
}

static getTerritoryCode() {
const utility = this.getUtility();
const encryptedCode = localStorage.getItem("territoryCode");
if (!encryptedCode) return null;
try {
return utility.decrypt(encryptedCode);
} catch (error) {
console.error('Failed to decrypt territoryCode:', error);
return null;
}
}


static setRole(role: string): void {
const utility = this.getUtility();
const encryptedRole = utility.encrypt(role);
localStorage.setItem('userRole', encryptedRole);
}

static getRole(): string | null {
const utility = this.getUtility();
const encryptedRole = localStorage.getItem('userRole');
if (!encryptedRole) return null;
try {
return utility.decrypt(encryptedRole);
} catch (error) {
console.error('Failed to decrypt userRole:', error);
return null;
}
}

/**
* To check the token and return if user is logged in or not
* @returns {boolean}
*/
static isLoggedIn() {
let token = this.getToken();
if (token && token.length > 0) {
return true;
}
return false;
}

static clear() {
sessionStorage.clear();
localStorage.clear();
}
}
