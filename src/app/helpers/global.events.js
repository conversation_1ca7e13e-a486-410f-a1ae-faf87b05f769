"use strict";
let __decorate =
  (this && this.__decorate) ||
  function (decorators, target, key, desc) {
    let c = arguments.length,
      r =
        c < 3
          ? target
          : desc === null
          ? (desc = Object.getOwnPropertyDescriptor(target, key))
          : desc,
      d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
      r = Reflect.decorate(decorators, target, key, desc);
    else
      for (let i = decorators.length - 1; i >= 0; i--)
        if ((d = decorators[i]))
          r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
  };
Object.defineProperty(exports, "__esModule", { value: true });
let core_1 = require("@angular/core");
let GlobalEvents = /** @class */ (function () {
  function GlobalEvents() {
    this.changeContentTopText = new core_1.EventEmitter(true);
  }
  /**
   * to set the content top title
   * @param value
   */
  GlobalEvents.prototype.setChangedContentTopText = function (value) {
    this.changeContentTopText.emit(value);
  };
  /**
   * return the event for value changes of content top title
   * @returns {EventEmitter<any>}
   */
  GlobalEvents.prototype.getChangedContentTopText = function () {
    return this.changeContentTopText;
  };
  __decorate(
    [core_1.Output()],
    GlobalEvents.prototype,
    "changeContentTopText",
    void 0
  );
  GlobalEvents = __decorate([core_1.Injectable()], GlobalEvents);
  return GlobalEvents;
})();
exports.GlobalEvents = GlobalEvents;
