import { Injectable, EventEmitter, Output } from "@angular/core";

@Injectable()
export class GlobalEvents {
  @Output() changeContentTopText: EventEmitter<any> = new EventEmitter(true);
  @Output() onPCSelect: EventEmitter<any> = new EventEmitter(true);
  @Output() showBack: EventEmitter<any> = new EventEmitter(true);
  @Output() clearPCSelect: EventEmitter<any> = new EventEmitter(true);
  @Output() onUserGridTabChange: EventEmitter<any> = new EventEmitter(true);
  @Output() onUserStatusChange: EventEmitter<any> = new EventEmitter(true);
  @Output() changeTabTextOnStatusChange: EventEmitter<any> = new EventEmitter(
    true
  );

  constructor() {}

  /**
   * to set the content top title
   * @param value
   */
  setChangedContentTopText(value: any) {
    this.changeContentTopText.emit(value);
  }

  setChangedStatusText(value: any) {
    this.changeTabTextOnStatusChange.emit(value);
  }
  

  /**
   * return the event for value changes of content top title
   * @returns {EventEmitter<any>}
   */
  getChangedContentTopText(): EventEmitter<any> {
    return this.changeContentTopText;
  }
  getChangedStatusText(): EventEmitter<any> {
    return this.changeTabTextOnStatusChange;
  }
}
