import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment.prod";

@Injectable()
export class UtilityHelper {
  private constructor() {}

  /**
   * To format and return the date
   * @param date
   * @returns {string}
   */

  /**
   * To convert the string in Title case
   * @param str
   * @returns {LoDashExplicitWrapper<string>|string|any|LodashReplace1x3|void}
   */
  static toTitleCase(str: any) {
    return str.replace(/\w\S*/g, (txt: any) => {
      return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
  }

  /**
   * To trim the values of the form controls
   * @param form
   * @param value
   * @param control
   * @returns {string|any|LoDashExplicitWrapper<string>}
   */
  static trimContent(form: any, value: any, control: any) {
    if (value) {
      form.controls[control].setValue(value.trim());
    }
    return value.trim();
  }

  /**
   * To trim the values of the form controls
   * @param value
   * @returns {string|any|LoDashExplicitWrapper<string>}
   */
  static trimValue(value: any) {
    return value && value.trim() ? value.trim() : null;
  }

  /**
   * To validate and allow only numbers
   * @param event
   * @returns {boolean}
   */
  static validateNumber(event: any) {
    let key = window.event ? event.keyCode : event.which;
    if (event.keyCode === 8 || event.keyCode === 46 || event.keyCode === 9) {
      return true;
    } else if (key < 48 || key > 57) {
      return false;
    } else {
      return true;
    }
  }

  /**
   * To return the array of Geo Types data
   * @returns array of Geo Types data
   */
  static getGeoTypes() {
    return [
      { name: "District", id: "3", parent_geo_id: "2", code: "GEO004" },
      { name: "Tehsil", id: "4", parent_geo_id: "3", code: "GEO005" },
      { name: "Village", id: "5", parent_geo_id: "4", code: "GEO006" },
    ];
  }

  static moneyFormatting(val: any) {
    let value = val.toString();
    if (value.includes(".")) {
      let parts = value.split(".");
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      let money = parts.join(".");
      return money;
    } else {
      let money = value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      return money;
    }
  }

  formatString(inputString: string): string { 
    if (!inputString) {
      return '';
    } else {
      return inputString
        .toLowerCase()
        .split(' ')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
  }

  encrypt(text: string) {
    let encryptedBase64Key = environment.base64;
    let parsedBase64Key = CryptoJS.enc.Base64.parse(encryptedBase64Key);
    let encryptedData = CryptoJS.AES.encrypt(text, parsedBase64Key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encryptedData.toString();
  }

  decrypt(text: string) {
    let parsedBase64Key = CryptoJS.enc.Base64.parse(environment.base64);
    let decryptedData = CryptoJS.AES.decrypt(text, parsedBase64Key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    let decryptedText = decryptedData.toString(CryptoJS.enc.Utf8);
    return decryptedText;
  }

  async hashPassword(password: any) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray
      .map((byte) => byte.toString(16).padStart(2, '0'))
      .join('');
    return hashHex;
  }
  
  formatDateToDDMMYYYY(date: string | Date): string {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const year = d.getFullYear();
    return `${day}-${month}-${year}`;
  }
}
