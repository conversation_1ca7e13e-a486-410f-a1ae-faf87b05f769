@import "../../theme/sass/auth";
@import "../../../styles";

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .modal-content {
    background-color: $white-bg;
    border-radius: 8px;
    width: 502px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 7px 0px;
    display: flex;
    gap: 20px;
    text-align: center;
    align-items: center;
    zoom: 0.9;

    .image-container {
      img {
        height: 185px;
      }
    }

    .main-message {
      max-width: 273px;

      .text-fields {
        font-size: 18px;
        line-height: 100%;
        width: 500px;
        color: #000000;
        font-weight: 500;
      }
    }

    .sub-message {
      display: flex;
      gap: 5px;
      border-radius: 8px;
      background: #FFFAF880;
      border: 1px solid #FF6F1F;
      padding: 10px 8px;

      .text-fields {
        font-size: 12px;
        line-height: 100%;
        color: #000000;
        font-weight: 500;
      }
    }
  }

  .action-button-section {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 15px 0;

    .action-btn {
      font-size: 18px;
      font-weight: 500;
      background: #FF8033;
      border: 1px solid #FF8033;
      border-radius: 5px;
      color: #fff;
      padding: 12px 0px;
      width: 110px;
      outline: none;
    }

    .cancel-btn {
      color: #FF8033;
      background: white;
      border: 1px solid #FF8033;
    }
  }
}