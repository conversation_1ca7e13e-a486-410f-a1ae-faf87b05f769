import { Component, Input, Output, EventEmitter, OnInit, SimpleChanges, OnChanges, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl, ValidationErrors, ValidatorFn, FormArray } from '@angular/forms';
import { NgIf, CommonModule } from "@angular/common";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatIconModule } from "@angular/material/icon";
import { ToastrService } from 'ngx-toastr';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { RewardPointsService } from 'src/app/app-services/reward-points-service';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { UserService } from 'src/app/app-services/user-service';
import { SupportService } from 'src/app/app-services/support.service';
import { GlobalEvents } from 'src/app/helpers/global.events';
import { BaThemeSpinner } from 'src/app/theme/services/baThemeSpinner';
import { Utility } from 'src/app/shared/utility/utility';
import { ViewChildren, QueryList, ElementRef } from '@angular/core';


interface ChildRfc {
  id: string;
  taxProof?: File;
}
@Component({
  selector: 'app-target-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, MatDatepickerModule, MatIconModule, NgIf, MatFormFieldModule, MatSelectModule, AngularMultiSelectModule,],
  templateUrl: './target-modal.component.html',
  styleUrls: ['./target-modal.component.scss'],
   encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.Default
})

export class TargetModalComponent implements OnInit, OnChanges {
  @ViewChildren('pdfInput') pdfInputs!: QueryList<ElementRef>;
  @Input() modalType: 'add' | 'edit' = 'edit';
  @Input() isOpen = false;
  @Input() defaultValues: any = {};
  @Input() addTargetType: boolean = false;

  @Output() close = new EventEmitter<void>();
  @Output() submit = new EventEmitter<any>();
  @Output() refreshUserList = new EventEmitter<void>();
  @Output() getAllTargetData = new EventEmitter<void>();


  targetForm!: FormGroup;
  growerChecked = false;
  uploadedFiles: { type: string; name: string; file: File }[] = [];
  childRfcArray: FormArray = this.fb.array([]);
  blurredChildIndices: Set<number> = new Set();


  startDate: Date = new Date(new Date().getFullYear(), 3, 1);
  endDate: Date = this.calculateFinancialYearEnd(this.startDate);
  minDate: Date | null = null; // Allow all dates, including previous ones

  leaderList: any[] = [];

  leaderDropdownSettings = {
    singleSelection: true,
    text: "Select Leader",
    enableSearchFilter: true,
    classes: "leader-dropdown"
  };

  selectedLeader: any[] = [];
  selectedLeaderRFC: any;
  selectedRedeemedMethod: string = '';
  statusDataList: any[] = [];
  selectedStatusLeader: any = [];
  statusLeaderName: any[] = [];
  statusDropdownLeaderName = {
    text: 'Select Leader Name',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'itemName',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  constructor(
    private fb: FormBuilder,
    public toastr: ToastrService,
    private rewardPointsService: RewardPointsService,
    private userService: UserService,
    private supportService: SupportService,
    private events: GlobalEvents,
    private spinner: BaThemeSpinner,
    private utility: Utility
  ) {
  }

  calculateFinancialYearEnd(startDate: Date): Date {
    const year = startDate.getMonth() < 3 ? startDate.getFullYear() : startDate.getFullYear() + 1;
    return new Date(year, 2, 31);
  }

  deleteLevel() {
  }

  dateRangeValidator(): ValidatorFn {
    return (group: AbstractControl): ValidationErrors | null => {
      const startDate = group.get('startDate')?.value;
      const endDate = group.get('endDate')?.value;

      if (!startDate || !endDate) {
        return null;
      }

      const errors: any = {};

      if (startDate.getTime() === endDate.getTime()) {
        errors.datesAreSame = true;
      }

      if (endDate < startDate) {
        errors.endDateBeforeStartDate = true;
      }

      return Object.keys(errors).length ? errors : null;
    };
  }

  ngOnInit(): void {

    this.targetForm = this.fb.group({
      startDate: [this.startDate],
      endDate: [this.endDate],
      childRfcs: this.fb.array([]),
      rootRfcId: this.fb.control('', {
        validators: [
          Validators.required,
          Validators.pattern(/^[A-ZÑ&]{3,4}[0-9]{6}[A-Z0-9]{3}$/)  // Ensure it accepts ABCD0732035A1
        ],
        updateOn: 'blur'  // ✅ Validate only on blur
      }),

      leaderId: ['', Validators.required],
      leaderName: ['', Validators.required],
      totalTarget: [
        '',
        [
          Validators.required,
          Validators.pattern(/^\d+(\.\d+)?$/)
        ]
      ],
      id: [null],
      cpPercent: [''],
      cpAmount: [],
      nppPercent: [{ value: '', disabled: true }],
      nppAmount: [''],
      grower: [false],
      fromApi: [false]
    });

    // if (this.modalType === 'add') {
    //   this.addChildRfc();
    // }


    this.getAllLeaders();
    this.growerChecked = this.targetForm.get('grower')?.value;
    this.updateFieldValidators(this.targetForm.get('grower')?.value);

    this.targetForm.get('grower')?.valueChanges.subscribe((value: boolean) => {
      this.growerChecked = value;
      this.updateFieldValidators(value);
    });

    this.targetForm.get('totalTarget')?.valueChanges.subscribe(() => this.calculateValues());
    this.targetForm.get('cpPercent')?.valueChanges.subscribe(() => this.calculateValues());
  }

addChildRfc(): void {
  const childRfcGroup = this.fb.group({
    id: ['', [
      Validators.required,
      Validators.pattern(/^[A-ZÑ&]{3,4}[0-9]{6}[A-Z0-9]{3}$/)
    ]],
    companyname: ['', Validators.required],
    taxProof: [null, Validators.required] // Add required validator here
  });
  
  // Mark all fields as untouched initially
  Object.keys(childRfcGroup.controls).forEach(key => {
    childRfcGroup.get(key)?.markAsUntouched();
  });
  
  this.childRfcArray.push(childRfcGroup);
}

areAllChildRfcFieldsFilled(): boolean {
  if (this.childRfcArray.length === 0) {
    return true;
  }
  return !this.hasEmptyChildRfcFields();
}


isAddChildRfcDisabled(): boolean {
  return this.hasEmptyChildRfcFields();
}


// Download method for child RFC attachments
downloadChildRfc(childData: any): void {
  console.log('Download childData:', childData); // Debug log

  // Check for both possible property names
  const fileUrl = childData?.taxProofURL || childData?.taxProofUrl || childData?.documentUrl;

  if (!childData || !fileUrl) {
    this.toastr.warning('No attachment available for download');
    return;
  }

  this.spinner.show();

  // Extract filename from URL or use default PDF name
  const fileName = this.extractFileNameFromUrl(fileUrl) || 'tax-proof.pdf';

  this.userService.downloadDocumentFiles(fileUrl).subscribe({
    next: (response: Blob) => {
      // Create PDF blob only
      const blob = new Blob([response], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = fileName.endsWith('.pdf') ? fileName : fileName + '.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.toastr.success('PDF downloaded successfully');
    },
    error: (error) => {
      console.error('Error downloading PDF:', error);
      this.toastr.error('Failed to download PDF attachment');
    },
    complete: () => {
      this.spinner.hide();
    }
  });
}

// Helper method to extract filename from URL
private extractFileNameFromUrl(url: string): string {
  if (!url) return '';
  const parts = url.split('/');
  return parts[parts.length - 1] || 'attachment';
}



// Simple delete method:
deleteChildRfc(index: number): void {
  this.childRfcArray.removeAt(index);
}

  funRestName(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
      // this.toastr.error('Double space is not allowed');
    }

    var k;
    k = event.charCode;
    if ((k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32) {
    } else {
      // this.toastr.error('Number and special characters' + ' ' + 'not allowed');
    }

    return (k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32;
  }

hasEmptyChildRfcFields(): boolean {
  return this.childRfcArray.controls.some(child => {
    const idValid = child.get('id')?.valid;
    const companyValid = child.get('companyname')?.valid;
    const taxProofValid = child.get('taxProof')?.valid;
    
    return !idValid || !companyValid || !taxProofValid;
  });
}


// Simple file selection handler:
onChildFileSelected(event: any, index: number): void {
  const file = event.target.files?.[0];
  if (!file) return;

  // Validate file type
  if (file.type !== 'application/pdf' && !file.name.toLowerCase().endsWith('.pdf')) {
    this.toastr.error('Please upload a valid PDF file');
    event.target.value = ''; // Clear the invalid file
    return;
  }

  // Validate file size (10MB limit)
  if (file.size > 10 * 1024 * 1024) {
    this.toastr.error('File size should not exceed 10MB');
    event.target.value = ''; // Clear the invalid file
    return;
  }

  // Update the form control and mark as touched
  const childGroup = this.childRfcArray.at(index);
  childGroup.patchValue({
    taxProof: file
  });
  childGroup.get('taxProof')?.markAsTouched();
  
  // Workaround for blur event not firing reliably
  this.blurredChildIndices.add(index);
}
onFileInputBlur(index: number): void {
  this.blurredChildIndices.add(index);
}
  private updateFieldValidators(isGrower: boolean): void {
    const conditionalFields = ['cpPercent', 'cpAmount', 'nppPercent', 'nppAmount'];

    conditionalFields.forEach(field => {
      const control = this.targetForm.get(field);
      if (!control) return;

      if (isGrower) {
        control.clearValidators();
        control.disable();
        control.setValue(null);
      } else {
        if (field === 'cpPercent' || field === 'nppPercent') {
          control.setValidators([
            Validators.required,
            Validators.min(0),
            Validators.max(100)
          ]);
        } else {
          control.setValidators(Validators.required);
        }
        control.enable();
      }
      control.updateValueAndValidity();
    });
  }

  getAllLeaders() {
    this.rewardPointsService.getAllLeader({}).subscribe(
      (response: any) => {
        try {
          // First, check if response is a string and try to parse it
          let parsedResponse = response;
          if (typeof response === 'string') {
            try {
              parsedResponse = JSON.parse(response);
            } catch (e) {
              // If parsing fails, try to decrypt first
              const decrypted = this.utility.decrypt(response);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          // Check if response has encryptedBody property
          let leaderData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            leaderData = JSON.parse(decrypted);
          } else {
            leaderData = parsedResponse;
          }
          
          if (Array.isArray(leaderData)) {
            this.leaderList = leaderData.map((item: any) => ({
              id: item.id,
              itemName: item.firstName + (item.lastName ? ' ' + item.lastName : ''),
              // rfc: item.rfc
            }));
            // Set default leader from defaultValues after list is ready
            if (this.defaultValues && this.defaultValues.leaderName) {
              const selected = this.leaderList.find(l => l.itemName === this.defaultValues.leaderName);
              if (selected) {
                this.selectedLeader = [selected];
                this.targetForm.patchValue({
                  leaderName: selected.itemName,
                  leaderId: selected.id
                });
              }
            }

            // Listen to leaderName changes and update rootRfcId
            this.targetForm.get('leaderName')?.valueChanges.subscribe(selectedName => {
              const selected = this.leaderList.find(l => l.itemName === selectedName);
              // const rfc = selected?.rfc || '';
              const id = selected?.id || '';
              // this.selectedLeaderRFC = rfc;
              this.targetForm.patchValue({ leaderId: id });

              this.selectedLeader = selected
                ? [{ id: selected.id, itemName: selected.itemName }]
                : [];
            });
          }
        } catch (error) {
          console.error('Error processing leader data:', error);
        }
      },
      (error) => {
        console.error('Failed to fetch leader data', error);
      }
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen'] && this.isOpen) {
      if (this.modalType === 'add') {
        this.resetForm();
      }
      this.getAllLeaders();
    }
  
    if (changes['defaultValues'] && this.targetForm) {
      this.isOpen = true;
      if (this.defaultValues?.startDate) {
        this.startDate = new Date(this.defaultValues.startDate);
      }
      if (this.defaultValues?.endDate) {
        this.endDate = new Date(this.defaultValues.endDate);
      }
      const rfcControl = this.targetForm.get('rootRfcId');
      rfcControl?.setValidators([
        Validators.required,
        Validators.pattern(/^[A-ZÑ&]{3,4}[0-9]{6}[A-Z0-9]{3}$/)
      ]);
      rfcControl?.updateValueAndValidity({ onlySelf: true, emitEvent: false });
      this.targetForm.patchValue({
        ...this.defaultValues,
        startDate: this.startDate,
        endDate: this.endDate
      });
      if (this.modalType === 'edit') {
        this.targetForm.markAllAsTouched();
  
        setTimeout(() => {
          this.formatTotalTarget();
          this.formatAmountValues();
        }, 0);
        while (this.childRfcArray.length) {
          this.childRfcArray.removeAt(0);
        }

        if (this.defaultValues.childRfcs && Array.isArray(this.defaultValues.childRfcs)) {
          this.defaultValues.childRfcs.forEach((child: any) => {
            const taxProofValue = child.taxProof
            ? { ...child.taxProof }
            : child.taxProofDocName
              ? { name: child.taxProofDocName }
              : child.childRfcs && child.childRfcs.length > 0
                ? { name: child.childRfcs[0].taxProofDocName, url: child.childRfcs[0].taxProofUrl }
                : null;

  
            const childGroup = this.fb.group({
              editRfcId: child.childDataId,
              id: [child.childRfcId || '', [
                Validators.required,
                Validators.pattern(/^[A-ZÑ&]{3,4}[0-9]{6}[A-Z0-9]{3}$/)
              ]],
              companyname: [child.companyName || '', Validators.required],
              taxProof: [taxProofValue, Validators.required],
              taxProofURL: child.taxProofUrl,
              fromApi: child.fromApi || false
            });
  
            this.childRfcArray.push(childGroup);
          });
        }
      }
      this.targetForm.updateValueAndValidity();
      this.calculateValues();
  
      this.growerChecked = this.targetForm.get('grower')?.value;
      this.setDropdownDisabledState();
  
      const selected = this.leaderList.find(l => l.itemName === this.defaultValues.leaderName);
      if (selected) {
        this.selectedLeader = [selected];
      }
  
      this.statusDropdownLeaderName.disabled = true;
    }
  }

  formatUSD(value: number): string {
    if (value === null || value === undefined) return '';
    
    // Format with commas and 2 decimal places
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  }

  setDropdownDisabledState() {
    this.statusDropdownLeaderName = {
      ...this.statusDropdownLeaderName,
      disabled: this.modalType === 'edit',
    };
  }

  transformToUppercase(event: Event, fieldName: string, index?: number) {
    const input = event.target as HTMLInputElement;
    const start = input.selectionStart;
    const end = input.selectionEnd;
    input.value = input.value.toUpperCase();
    input.setSelectionRange(start, end);
    if (fieldName === 'rootRfcId') {
      this.targetForm.get('rootRfcId')?.setValue(input.value, { emitEvent: true });
    } else if (fieldName === 'childRfcId' && index !== undefined) {
      const childGroup = this.childRfcArray.at(index);
      childGroup?.get('id')?.setValue(input.value, { emitEvent: true });
    }
  }
  


  calculateValues(): void {
  const total = +this.targetForm.get('totalTarget')?.value || 0;
  const cpPercent = +this.targetForm.get('cpPercent')?.value || null;
  let cpAmount = 0;
  let nppPercent = 0;
  let nppAmount = 0;

  if (cpPercent !== null) {
    cpAmount = (total * cpPercent) / 100;
    nppPercent = 100 - cpPercent;
    nppAmount = total - cpAmount;
  }

  this.targetForm.patchValue({
    cpAmount: cpAmount,
    nppPercent: nppPercent,
    nppAmount: nppAmount
  }, { emitEvent: false });
  }

  handleSubmit(): void {
  if (!this.targetForm.valid || !this.areAllChildRfcFieldsFilled()) {
    return;
  }
  this.spinner.show();
  const formattedChildRfcs = this.childRfcArray.value.map((child: any) => {
    return {
      editRfcId: child.editRfcId,
      rfc: child.id,
      companyName: child.companyname,
      taxProof: child.taxProof,
      taxProofURL:child.taxProofURL
    };
  });
  const formData = {
    ...this.targetForm.value,
    childRfcs: formattedChildRfcs
  };
  
  if (formData.grower) {
    delete formData.cpPercent;
    delete formData.cpAmount;
    delete formData.nppPercent;
    delete formData.nppAmount;
  }

  if (this.modalType === 'edit' && this.defaultValues.id && !formData.id) {
    formData.id = this.defaultValues.id;
  }

  if (formData.startDate) {
    const startDate = new Date(formData.startDate);
    formData.startDate = this.formatLocalDate(startDate);
  }

  if (formData.endDate) {
    const endDate = new Date(formData.endDate);
    formData.endDate = this.formatLocalDate(endDate);
  }

  formData.createdAt = new Date().toISOString().split('T')[0];

  const serviceCall = this.modalType === 'edit'
    ? this.rewardPointsService.editTarget(formData)
    : this.rewardPointsService.addTarget(formData);

  serviceCall.subscribe({
    next: () => {
      this.spinner.hide();
      this.toastr.success(
        this.modalType === 'edit' ? "Target edited successfully" : "Target added successfully"
      );
      this.close.emit();
      this.submit.emit(formData);
      this.events.onUserStatusChange.emit(true);
      this.refreshUserList.emit();
      this.spinner.hide();
      if (this.modalType !== 'edit') {
        this.getAllTargetData.emit();
      }
    },
    error: (error: any) => {
      this.spinner.hide();
      try {
        if (error.error && typeof error.error === 'string') {
          try {
            const decrypted = this.utility.decryptString(error.error);
            const errorObj = JSON.parse(decrypted);
            this.toastr.error(errorObj.message || "Something went wrong");
          } catch (decryptError) {
            console.warn('Decryption failed:', decryptError);
            try {
              const errorObj = JSON.parse(error.error);
              this.toastr.error(errorObj.message || "Something went wrong");
            } catch (parseError) {
              this.toastr.error("Something went wrong");
            }
          }
        } else if (error.message) {
          this.toastr.error(error.message);
        } else {
          this.toastr.error("Something went wrong");
        }
      } catch (e) {
        console.error('Unexpected error block failure:', e);
        this.toastr.error("Something went wrong");
      }
    }
  });
}

  formatLocalDate(date: Date): string {
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const day = ('0' + date.getDate()).slice(-2);
    return `${year}-${month}-${day}`;
  }

  handleClose(): void {
    this.targetForm.reset({
      leaderId: "",
      rootRfcId: '',
      leaderName: '',
      totalTarget: '',
      cpPercent: '',
      cpAmount: 0,
      nppPercent: 0,
      nppAmount: 0,
      grower: false,
      startDate: this.startDate,
      endDate: this.endDate
    });

    this.growerChecked = false;

    this.close.emit();
  }
  onLeaderSelect(event: any) {
    this.targetForm.patchValue({
      leaderName: event.itemName,
      leaderId: event.id
    });
    this.selectedStatusLeader = event;
  }
  onLeaderDeselect(event: any) {
    this.selectedStatusLeader = [];
    this.targetForm.patchValue({
      leaderName: '',
      leaderId: ''
    });
  }
  onLeaderDeselectAll(event: any) {
    this.targetForm.patchValue({
      leaderName: '',
      leaderId: ''
    }); this.selectedStatusLeader = [];
  }
  // private activateUserStatus(userId: string): void {
  //   if (!userId) {
  //     console.warn('Cannot activate user: No user ID provided');
  //     this.spinner.hide();
  //     return;
  //   }

  //   const data = {
  //     active: true,
  //     id: userId
  //   };

  //   this.userService.updateAppUsersStatus(data).subscribe({
  //     next: (response: any) => {
  //       response = this.utility.decryptString(response)
  //       this.events.onUserStatusChange.emit(true);
  //       this.refreshUserList.emit();
  //       try {
  //         // Check if response needs decryption
  //         if (typeof response === 'string' && response) {
  //           try {
  //             const decrypted = this.utility.decrypt(response);
  //             response = JSON.parse(decrypted);
  //           } catch (e) {
  //             // If decryption fails, use original response
  //           }
  //         }
          
  //         this.events.onUserStatusChange.emit(true);
  //         this.spinner.hide();
  //       } catch (error) {
  //         console.error('Error processing response:', error);
  //         this.spinner.hide();
  //       }
  //     },
  //     error: (error: any) => {
  //       console.error('Failed to activate user status:', error);
  //       this.spinner.hide();
  //     }
  //   });
  // }

  onStartDateChange(date: Date): void {
    this.startDate = date;
    const newEndDate = this.calculateFinancialYearEnd(date);
    // this.endDate = newEndDate;

    if (this.targetForm) {
      // this.endDate = newEndDate;
      this.targetForm.patchValue({
        startDate: date
      });
    }
  }

  onEndDateChange(date: Date): void {
    if (this.targetForm) {
      this.targetForm.patchValue({
        endDate: date
      });
    }
  }

  // private refreshUserData(userId: string): void {
  //   this.userService.getAppUserDetailsById(userId).subscribe({
  //     next: (userData: any) => {
  //       this.refreshUserList.emit();
  //       this.spinner.hide();
  //     },
  //     error: (error: any) => {
  //       console.error('Failed to refresh user data:', error);
  //       this.spinner.hide();
  //     }
  //   });
  // }

  rootForm = this.fb.group({
    rootRfcId: [
      '',
      [
        Validators.required,
        Validators.pattern(/^[A-ZÑ&]{3,4}[0-9]{6}[A-Z0-9]{3}$/)  // Updated to match ABCD0732035A1
      ]
    ],
  });

  preventMinus(event: KeyboardEvent) {
  if (event.key === '-' || event.key === 'Minus') {
    event.preventDefault();
  }
}

  preventSpecialCharacters(event: KeyboardEvent): void {
    this.utility.preventSpecialCharacters(event);
  }

  getFilesByType(type: string): { type: string, name: string }[] {
    return this.uploadedFiles.filter(f => f.type === type);
  }

    onFileSelected(event: any, fileType: string) {
    const file = event.target.files[0];
    if (!file) return;

    // File size validation (5MB limit)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      this.toastr.error(`File size should not exceed 5MB`);
      event.target.value = ''; // Clear the invalid file
      return;
    }

    // File type validation
    const isValidType =
      (fileType === 'pdf' && (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf'))) ||
      (fileType === 'xml' && (file.type === 'text/xml' || file.name.toLowerCase().endsWith('.xml')));

    if (!isValidType) {
      this.toastr.error(`Please upload a valid ${fileType.toUpperCase()} file`);
      event.target.value = ''; // Clear the invalid file
      return;
    }

    // Remove existing file of same type
    this.uploadedFiles = this.uploadedFiles.filter(f => f.type !== fileType);

    // Add new file
    this.uploadedFiles.push({
      type: fileType,
      name: file.name,
      file: file
    });
    // Reset input to allow re-selecting same file
    event.target.value = '';
  }

  onTotalTargetInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/[^\d.]/g, '');
    
    const parts = value.split('.');
    if (parts.length > 2) {
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    const numericValue = value ? parseFloat(value) : null;
    this.targetForm.get('totalTarget')?.setValue(numericValue, { emitEvent: true });
  }

  formatTotalTarget(): void {
    const totalTargetControl = this.targetForm.get('totalTarget');
    if (totalTargetControl && totalTargetControl.value) {
      const numericValue = parseFloat(totalTargetControl.value);
      totalTargetControl.setValue(numericValue, { emitEvent: false });
      const inputElement = document.querySelector('input[formControlName="totalTarget"]') as HTMLInputElement;
      if (inputElement) {
        inputElement.value = this.formatUSD(numericValue);
      }
    }
  }

  formatAmountValues(): void {
    const cpAmountInput = document.querySelector('input[formControlName="cpAmount"]') as HTMLInputElement;
    const nppAmountInput = document.querySelector('input[formControlName="nppAmount"]') as HTMLInputElement;
    
    if (cpAmountInput && this.targetForm.get('cpAmount')?.value) {
      const cpAmount = +this.targetForm.get('cpAmount')?.value;
      if (!isNaN(cpAmount)) {
        cpAmountInput.value = this.formatUSD(cpAmount);
      }
    }
    if (nppAmountInput && this.targetForm.get('nppAmount')?.value) {
      const nppAmount = +this.targetForm.get('nppAmount')?.value;
      if (!isNaN(nppAmount)) {
        nppAmountInput.value = this.formatUSD(nppAmount);
      }
    }
  }

  private resetForm(): void {
    this.startDate = new Date(new Date().getFullYear(), 3, 1);
    this.endDate = this.calculateFinancialYearEnd(this.startDate);
    this.targetForm.reset({
      leaderId: "",
      rootRfcId: '',
      leaderName: '',
      totalTarget: '',
      cpPercent: '',
      cpAmount: 0,
      nppPercent: 0,
      nppAmount: 0,
      grower: false,
      fromApi: false,
      startDate: this.startDate,
      endDate: this.endDate
    });
      while (this.childRfcArray.length) {
        this.childRfcArray.removeAt(0);
      }

      // // Add initial child RFC if in add mode
      // if (this.modalType === 'add') {
      //   this.addChildRfc();
      // }

    this.growerChecked = false;
  }
}
