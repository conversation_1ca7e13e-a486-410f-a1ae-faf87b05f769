<div class="modal-overlay">
    <div class="modal-content">
        <div class="heading">View Documents</div>
        <div class="documents-container">
            <div *ngFor="let doc of documents" class="document-card">
                <img src="../../../assets/img/pdf-icon.svg" alt="PDF Icon" class="pdf-icon" />
                <div class="document-name" [title]="doc.name">{{ doc.name }}</div>
                <div class="actions">
                    <button 
                      class="document-button" 
                      [title]="doc.button"
                      (click)="onViewDocument(doc.type)">
                      {{ doc.button }}
                    </button>
                    <button class="download-button" (click)="onDownload(doc.url, doc.name)">
                        <img class="icon" src="../../../assets/img/download-icon.svg" alt="download-icon">
                    </button>
                </div>
            </div>
        </div>
        <div class="button-section">
            <button class="button-style close-button" (click)="onClose()">Close</button>
        </div>
    </div>
</div>
