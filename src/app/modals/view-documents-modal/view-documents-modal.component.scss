@import "../../theme/sass/auth";
@import "../../../styles";

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;

    .modal-content {
        background-color: $white-bg;
        border-radius: 8px;
        width: 760px;
        height: 360px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 40px;
        text-align: center;
        zoom: 0.9;

        .heading {
            text-align: left;
            font-weight: 700;
            font-size: 24px;
            line-height: 100%;
            color: #222222;
        }

        .documents-container {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 35px;
            flex-wrap: wrap; /* Allow wrapping for multiple documents */

            .document-card {
                background-color: #FFE9E9;
                border-radius: 4px;
                border: 0.49px dashed #EF5350;
                padding: 20px;
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 200px; /* Fixed width for consistency */
                height: 180px; /* Fixed height for consistency */
                justify-content: space-between;

                .pdf-icon {
                    height: 43px;
                    width: 43px;
                    margin-bottom: 10px;
                    margin-right: 20px;
                }

                .document-name {
                    font-family: Roboto;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 14px;
                    color: #000000;
                    text-align: center;
                    width: 100%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin: 10px 0;
                    margin-right: 20px;
                    height: 14px; /* Fixed height for text */
                }

                .actions {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 6px;
                    width: 100%;
                    margin-top: auto; /* Push to bottom */

                    .document-button {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 120px; /* Limit width */
                        display: inline-block;
                        height: 32px;
                        line-height: 32px;
                        padding: 0 12px;
                        background-color: #FF8033;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: background-color 0.2s;
                    }

                    .document-button:hover {
                        background-color: #e67730;
                    }

                    .download-button {
                        border: none;
                        background: none;
                        border-radius: 2px;
                        padding: 0;
                        height: 32px;
                        width: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        
                        img {
                            height: 30px;
                            width: 30px;
                        }
                    }
                }
            }
        }

        .button-section {
            margin-top: 40px;
            text-align: center;
        }
    }
}

.button-style {
    width: fit-content;
    background-color: $button-color;
    color: white;
    border: none;
    font-size: 16px;
    font-weight: 500;
    border-radius: 3px;
    padding: 2px 30px;
    cursor: pointer;
}

.document-button {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
  height: 32px;
  line-height: 32px;
  padding: 0 12px;
  background-color: $button-color;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  margin: 5px 0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.document-button:hover {
  background-color: #e67730;
}
