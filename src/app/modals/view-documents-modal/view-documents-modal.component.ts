import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-view-documents-modal',
  templateUrl: './view-documents-modal.component.html',
  styleUrls: ['./view-documents-modal.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class ViewDocumentsModalComponent {
  @Input() documents: { name: string, url: string, type: string, button: string }[] = [];
  @Output() close = new EventEmitter<void>();
  @Output() viewDocument = new EventEmitter<string>();

  onClose() {
    this.close.emit();
  }

  onDownload(url: string, filename: string) {
    fetch(url)
      .then(res => res.blob())
      .then(blob => {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        URL.revokeObjectURL(link.href);
      });
  }

  onViewDocument(type: string): void {
    this.viewDocument.emit(type);
  }
}
