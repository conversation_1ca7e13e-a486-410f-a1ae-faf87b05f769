import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "../../environments/environment";
import * as _ from "lodash";
import { Utility } from "../shared/utility/utility";

@Injectable({
  providedIn: "root",
})
export class ApiServiceService {
  url: string = " ";
  getCustomersURL: any = "customerByTerritory/";
  getBrandsURL: any = "brands/dropdown/ByCrop/";
  baseurl: any = environment.baseUrl;
  randomNumber :any;

  constructor(private http: HttpClient, private utility : Utility) {
    const generatedNumber = Math.floor(1000 + Math.random() * 9000);
    this.randomNumber = this.utility.encrypt(generatedNumber.toString());
  }

  getApiMethod() {
    return this.http.get(this.getCustomersURL);
  }
  getUsers() {
    return this.http.get(this.url);
  }
  getDropDownText(id: any, object: any) {
    const selObj = _.filter(object, function (o) {
      return _.includes(id, o.id);
    });
    return selObj;
  }

  getBrands(id: any) {
    let URL = this.getBrandsURL + id;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers,responseType : 'text'
    });
  }
  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`, 
      'Content-Type' : 'application/json',
      'randNum' : `${this.randomNumber}` , 
    });
  }
}
