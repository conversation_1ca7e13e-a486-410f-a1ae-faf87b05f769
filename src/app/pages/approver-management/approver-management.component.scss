:host {
  // Dev server specific toast fixes
  ::ng-deep {
    .toast-container {
      position: fixed !important;
      z-index: 999999 !important; 
      pointer-events: auto !important;
      top: 12px !important;
      right: 12px !important;
      width: auto !important;
      max-width: 350px !important;
      display: block !important;
      visibility: visible !important;
      
      .ngx-toastr {
        opacity: 1 !important;
        box-shadow: 0 0 12px rgba(0, 0, 0, 0.25) !important;
        display: block !important;
        visibility: visible !important;
        pointer-events: auto !important;
      }
      
      .toast-success {
        background-color: #51A351 !important;
      }
      
      .toast-error {
        background-color: #BD362F !important;
      }
      
      .toast-info {
        background-color: #2F96B4 !important;
      }
      
      .toast-warning {
        background-color: #F89406 !important;
      }
    }
  }
}

@import "../../theme/sass/_auth";
@import "../../../styles.scss";
@import "../../theme/sass/mixins";
@import "../../theme/sass/conf/variables";

.app-container {
    width: 100%;
    padding-left: 15px;
    overflow-y: hidden;
    .app-grid-container {
      width: 98%;
      float: left;
      border-radius: 3px;
      margin-top: 50px;
      position: relative;
      .app-grid-data-container {
        width: 100%;
        overflow-y: auto;
        background: #fff;
        border-radius: 2px;
        padding: 10px 15px 25px;
        float: left;
        margin: 10px 0;
        .view-switch {
          width: 12%;
          float: right;
          @media screen and (max-width: 768px) {
            width: 100%;
          }
          .map-view {
            width: 30%;
            float: right;
            background: #a4a4a4;
            font-size: 22px;
            text-align: center;
            border-radius: 3px;
            margin: 0 0 0 11px;
            color: #ffffff;
            cursor: pointer;
          }
          .active {
            background: #102d69;
          }
          .disable {
            opacity: 0.9;
            cursor: not-allowed;
          }
        }
  
        .app-table {
          font-size: 15px;
          width: 100%;
          overflow-y: hidden;
          float: left;
          @media screen and (max-width: 900px) {
            width: 100%;
          }
        }
  
        .app-map {
          width: 50%;
          height: 120vh;
          float: left;
          padding: 15px 0 15px 10px;
          .name {
            line-height: 17px;
            font-size: 14px;
            font-weight: 500;
            color: #333;
          }
          @media screen and (max-width: 900px) {
            width: 100%;
          }
        }
        .no-result {
          width: 100%;
          float: left;
          text-align: center;
          line-height: 43px;
          background-color: rgba(0, 0, 0, 0.1);
          margin-top: 10px;
          font-size: 15px;
          color: #666679;
        }
  
        .full-width {
          width: 100%;
        }
        .hide {
          display: none;
        }
  
        .app-filter-container {
          display: flex;
          // margin-bottom: 5px;
          .left-column {
            width: 30%;
            .main-campaign {
              .panel {
                .table-heading {
                  color: #FF8033;
                }
                .panel-body {
                  .wizard {
                    width: 60%;
                    float: left;
                    background: #fff;
                    margin: 0;
                    margin-bottom: 20px;
                    // margin-top: -22px;
                    font-size: 14px;
                    @media screen and (max-width: 500px) {
                      font-size: 8px;
                    }
                    @media screen and (max-width: 768px) {
                      font-size: 12px;
                    }
                    .profile-tab {
                      float: left;
                      text-decoration: none;
                      text-align: center;
                      border-bottom: 3px solid #c6c6c6;
                      height: 50px;
                      line-height: 50px;
                      cursor: pointer;
                      font-size: 14px;
                      color: #c6c6c6;
                      width: 33%;
                      &:last-child {
                        border-right: none;
                      }
  
                      i {
                        margin-left: 5px;
                      }
                    }
                    .active {
                      border-bottom: 3px solid $button-color;
                    }
                  }
                  .wizard .active {
                    border-bottom: 3px solid #ff8033;
                    color: #ff8033;
                  }
                  .current-tab {
                    font-size: 14px;
                    border-bottom: 3px solid $button-color;
                    color: #195c94;
                    @media screen and (max-width: 500px) {
                      font-size: 8px !important;
                    }
                    @media screen and (max-width: 768px) {
                      font-size: 12px !important;
                    }
                  }
  
                  .search-input {
                    display: flex;
                    align-items: center;
                    width: 50%;
                  }
  
                  .app-filter {
                    .radio-container {
                      height: 40px;
                      padding-left: 30px;
                      display: flex;
                      gap: 10px;
                      vertical-align: middle;
                      align-items: flex-start;
                      position: relative;
                      bottom: 6px;
                      .radio-margin {
                        label {
                          position: relative;
                          top: 2px;
                          left: 3px;
                          font-size: 16px;
                        }
                      }
                    }
                  }
                  .tab-button {
                    min-width: 60%;
                    height: 40px;
                    float: left;
                    background: #fff;
                    margin: 0;
                    margin-top: -15px;
                    font-size: 14px;
                    border-radius: 6px;
                    border: 1px solid rgba(0, 0, 0, 0.15);
                    @media screen and (max-width: 500px) {
                      font-size: 8px;
                    }
                    @media screen and (max-width: 768px) {
                      font-size: 12px;
                    }
                    .profile-tab {
                      float: left;
                      text-align: center;
                      height: 38px;
                      line-height: 50px;
                      cursor: pointer;
                      font-size: 14px;
                      color: #c6c6c6;
                      width: 50%;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      i {
                        margin-left: 5px;
                      }
                    }
                    .active {
                      border-bottom: 3px solid $button-color;
                    }
                  }
                  .tab-button .active {
                    border: 1px solid #ff8033;
                    color: #ff8033;
                    background-color: #e5efff;
                    font-weight: 500;
                    border-radius: 5px 0px 0px 5px;
                  }
                  .tab-button .activeCrop {
                    border: 1px solid #ff8033;
                    color: #ff8033;
                    width: 100% !important;
                    background-color: #e5efff;
                    font-weight: 500;
                    border-radius: 5px 5px 5px 5px !important;
                  }
                }
              }
  
              .app-filter {
                min-width: 42%;
                height: 40px;
              }
            }
          }
          .second-left-column {
            width: 60%;
          }
          .right-column {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
          }
  
          .input-group {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: nowrap;
            justify-content: flex-end;
          }
  
          .search-input {
            display: flex;
            align-items: center;
            width: 300px;
          }
  
          .input-group-add {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 8px 12px;
            background-color: #fff;
            border: 1px solid #ff8033;
            border-radius: 5px;
          }
  
          .input-group-add i {
            color: #ff8033;
          }
  
          .input-group-add input {
            border: none;
            width: 100%;
            outline: none;
            font-size: 14px;
            margin-left: 5px;
          }
  
          .input-group-add input:focus {
            border: none;
          }
  
          .input-group-add img {
            height: 18px;
            cursor: pointer;
          }
  
          .export-button,
          .filter-button {
            margin-left: 5px;
          }
  
          .export-button button,
          .filter-button button {
            width: 40px;
            height: 40px;
            background-color: #ff8033;
            color: white;
            border: none;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
  
          .export-button button:hover,
          .filter-button button:hover {
            background-color: #e06c2b;
          }
  
          .export-button i,
          .filter-button i {
            color: white;
            font-size: 16px;
          }
  
          .addSchemeButton {
            height: 37px;
            font-weight: 600;
            background-color: #ff8033;
            border-radius: 0.25rem;
            border: 1px solid #ff8033;
            color: #fff;
            padding: 0px 14px 0 14px;
            width: 160px;
          }
        }
      }
    }
  }

  .right-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    .search-container {
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: flex-end;

      .export-button button {
        width: 38px;
        height: 37px;
        // background-color: #fff;
        border-radius: 0.25rem;
        border: 1px solid #FF8033;
        background: #FF8033;
        color: #fff;
      }
      .add-button button {
        width: 38px;
        height: 37px;
        background-color: #FF8033;
        border-radius: 0.25rem;
        border: 1px solid #FF8033;
        color: #fff;
      }
      .input-group {
        display: flex;
        justify-content: flex-end;

        .search-input {
          display: flex;
          align-items: center;
          width: 50%;

          .input-group-add {
            padding: 9px 10px;
            margin-bottom: 0;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1;
            color: #464a4c;
            text-align: center;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 0.25rem;
            width: 85%;
            margin-left: 11%;
            border-color: #FF8033;
            display: flex;
            img {
              height: 18px;
              cursor: pointer;
            }
            i {
              float: left;
            }

            input {
              border: none;
              width: 85%;
              outline: none;
              font-size: 14px;
            }
            input:focus {
              border: none;
            }
          }

          input {
            margin-right: 10px;
            margin-left: 10px;
          }
        }

        .export-button {
          margin-right: 10px;
        }
      }
    }

    // Media query for tablets (if needed)
    @media screen and (max-width: 768px) {
      .search-container {
        justify-content: center;
      }
    }
  }

  // .popup-overlay {
  //   position: fixed;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  //   background-color: rgba(0, 0, 0, 0.4);
  //   z-index: 1000;
  // }

  .level-field-1 {
    width: 100% !important;
    // padding-right: 10px !important;
  }
  .level-field-2 {
    width: 95% !important;
    // padding-right: 10px !important;
  }
  
    .form-group {
      margin-bottom: 1.2rem;
      display: flex;
      flex-direction: column;
  
      label {
        margin-bottom: 0.3rem;
        font-weight: 500;
        color: #464a4c;
      }
  
      .required {
        color: red;
      }
  
      angular2-multiselect {
        height: 36px;
        // padding: 0 0.5rem;
        // border: 1px solid #ccc;
        // border-radius: 4px;
      }
    }
  
    .with-icon {
      .dropdown-icon-level{
        display: flex;
        align-items: center;
        width: 100%;
        angular2-multiselect {
          flex: 1;
          margin-right: 5px;
          gap: 5px;
        }
      }
      .dropdown-icon {
        display: flex;
        align-items: center;
  
        angular2-multiselect {
          flex: 1;
        }
  
        .delete-btn {
          background: none;
          border: 1px solid;
          border-radius: 4px;
          color: #FF8033;
          cursor: pointer;
          margin-left: 8px;
          font-size: 1.2rem;
        }
      }
    }
  
    .button-group {
      display: flex;
      justify-content: center;
      margin-top: 2rem;
      gap: 10px;
  
      .cancel-btn,
      .create-btn {
        width: 40%;
        padding: 0.5rem;
        border-radius: 4px;
        font-weight: 500;
        border: 1px solid #FF8033;
      }
  
      .cancel-btn {
        background-color: white;
        color: #FF8033;
      }
  
      .create-btn {
        background-color: #FF8033;
        color: white;
      }
    }
  
  .popup-container {
    overflow: auto;
    background: #fff;
    width: 35%;
    padding: 20px 40px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    max-width: 700px;
    max-height: 80%;
  }
  
  .popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3); /* No blur, just a dark overlay */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }


  //for invoce 
  mat-table {
    width: 100%;
    border-collapse: collapse;
  
    mat-header-cell, mat-cell {
      padding: 8px;
      text-align: left;
      border: 1px solid #ddd;
      color: #080808; // All table body font color
    }
  
    mat-header-cell {
      background-color: #FBE3D4; // Table header background color
      font-weight: bold;
    }
  
    mat-cell {
      background-color: #ffffff;
      background-color: #ffffff;
      border-bottom: none;
      border-top: none; 
    }
  
    // Alternating row colors
    mat-row:nth-child(even) mat-cell {
      background-color: #F8F8F8; // Second row color
    }
  
    .header-container {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  
    .cell-container {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  
    .search-field {
      width: 100px;
      mat-select {
        font-size: 14px;
      }
    }
  
    .custom-multiselect {
      width: 100px;
      .c-btn {
        padding: 5px;
        border: 1px solid #ddd;
      }
    }
  
    // Checkbox styling
    mat-checkbox .mdc-checkbox__background {
      background-color: #000000; // Checkbox fill color
    }
    mat-checkbox .mdc-checkbox__checkmark {
      color: #ffffff; // Right tick color
    }
    .button-group {
      display: flex;
      justify-content: center;
      margin-top: 2rem;
      gap: 10px;
  
      .cancel-btn,
      .create-btn {
        width: 40%;
        padding: 0.5rem;
        border-radius: 4px;
        font-weight: 500;
        border: 1px solid #FF8033;
      }
  
      .cancel-btn {
        background-color: white;
        color: #FF8033;
      }
  
      .create-btn {
        background-color: #FF8033;
        color: white;
      }
    }
  }

::ng-deep .mdc-checkbox__background{
  border-color: #1C1B1F !important;
}

::ng-deep .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background{
  background-color: #000000 !important;
  fill: #000000 !important;
}

.button-group-inline {
  display: flex;
  gap: 8px;
}

.add-btn-container {
  display: flex;
  justify-content: flex-end; /* Align to the right */
  height: 40px;
  margin: 0;
  
  .create-level-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    height: 37px;
    background-color: #ff8033;
    border-radius: 0.25rem;
    border: 1px solid #ff8033;
    color: #fff;
    padding: 0 14px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: darken(#ff8033, 5%);
    }
    
    img {
      display: inline-block;
      vertical-align: middle;
    }
    
    span {
      display: inline-block;
      vertical-align: middle;
      white-space: nowrap;
    }
  }
}

.history-filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  // margin-bottom: 15px;
  
  .left-column {
    float: left;
    width: auto;
  }
  
  .second-left-column {
    width: 60%;
  }
  
  .right-column {
    display: flex;
    justify-content: flex-end;
    width: auto;
    margin-left: auto; /* Push to the right */
    
    .input-group {
      display: flex;
      justify-content: flex-end;
      width: 100%;
    }
  }
}

/* Ensure the button stays right-aligned on smaller screens */
@media screen and (max-width: 768px) {
  .history-filter-container {
    flex-direction: column;
    
    .left-column, .second-left-column {
      width: 100%;
      margin-bottom: 10px;
    }
    
    .right-column {
      width: 100%;
      justify-content: flex-end;
      
      .input-group {
        justify-content: flex-end;
      }
    }
  }
}

::ng-deep .myclass .c-list .item-label,
::ng-deep .myclass .c-btn {
  color: black !important;
}

::ng-deep .form-group label {
  color: #000000 !important;
}

::ng-deep .selected-list .c-list .c-token {
  background-color: #e0f7fa !important; /* Replace with your desired color */
}
.disable-submit {
  opacity: 0.7;
  cursor: not-allowed;
  &:hover {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.custom-delete-buttons {
  margin-top: 10px;
  
  .delete-btn {
    background: none;
    border: 1px solid #ff8033;
    border-radius: 4px;
    color: #ff8033;
    cursor: pointer;
    margin: 5px;
    padding: 5px 10px;
    
    &:hover {
      background-color: #fff0e8;
    }
    
    .fa-trash {
      margin-right: 5px;
    }
  }
}

// Fix for angular2-multiselect borders in production
::ng-deep .popup-container {
  .form-group {
    .dropdown-icon-level, .dropdown-icon {
      angular2-multiselect {
        // Force borders to be visible with high specificity
        ::ng-deep .c-btn {
          border: 1px solid #FF8033 !important;
          border-radius: 4px !important;
          min-height: 36px !important;
          display: flex !important;
          align-items: center !important;
          background-color: #ffffff !important;
          
          &:focus, &:active {
            border-color: #FF8033 !important;
            box-shadow: 0 0 0 1px rgba(255, 128, 51, 0.25) !important;
          }
        }
        
        // Fix dropdown list styling
        ::ng-deep .dropdown-list {
          border: 1px solid #ccc !important;
          border-radius: 4px !important;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) !important;
        }
      }
    }
  }
}

// Ensure ViewEncapsulation doesn't prevent styles from applying
:host {
  display: block;
  
  ::ng-deep {
    // Target multiselect components with very high specificity
    body .popup-container .form-group .dropdown-icon-level angular2-multiselect .c-btn,
    body .popup-container .form-group .dropdown-icon angular2-multiselect .c-btn {
      border: 1px solid #FF8033 !important;
      border-radius: 4px !important;
      min-height: 36px !important;
    }
    
    // Fix for buttons in the popup
    .add-btn, .delete-btn {
      border: 1px solid #FF8033 !important;
      border-radius: 4px !important;
    }
  }
}

// Additional fixes for production build issues
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.popup-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  .popup-header {
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
    }
  }
  
  .form-group {
    margin-bottom: 15px;
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #333;
      
      .required {
        color: red;
        margin-left: 3px;
      }
    }
  }
  
  .with-icon {
    .dropdown-icon-level, .dropdown-icon {
      display: flex;
      align-items: center;
      gap: 10px;
      
      angular2-multiselect {
        flex: 1;
      }
    }
  }
  
  .button-group-inline {
    display: flex;
    gap: 8px;
  }
  
  .add-btn, .delete-btn {
    background: none;
    border: 1px solid #FF8033;
    border-radius: 4px;
    color: #FF8033;
    cursor: pointer;
    height: 36px;
    width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      background-color: rgba(255, 128, 51, 0.1);
    }
  }
  
  .button-group {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    
    .cancel-btn, .create-btn {
      padding: 8px 20px;
      border-radius: 4px;
      font-weight: 500;
      cursor: pointer;
      min-width: 100px;
    }
    
    .cancel-btn {
      background-color: white;
      color: #FF8033;
      border: 1px solid #FF8033;
    }
    
    .create-btn {
      background-color: #FF8033;
      color: white;
      border: 1px solid #FF8033;
    }
    
    .disable-submit {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
}
