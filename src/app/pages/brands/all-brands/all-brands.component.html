<div (window:resize)="onWindowResizeBrands()"></div>
<div class="brands-container">
  <div class="brands-grid-container">
    <div class="area-filter-container">
      <div class="left-column">
        <div class="main-campaign">
          <div class="panel">
            <div class="panel-body">
              <div class="wizard">
                <a style="text-decoration: none; font-weight: 500;" class="profile-tab area-tab-width"
                  [ngClass]="{ active: isAllProduct }" (click)="ticketTab('ALLPRODUCT')">
                  <span>All Product<span *ngIf="isAllProduct"> ({{ allProductCount }})</span></span>
                </a>
            
                <a style="text-decoration: none; font-weight: 500;" class="profile-tab area-tab-width"
                  [ngClass]="{ active: isActiveProduct }" (click)="ticketTab('ACTIVE')">
                  <span>Active Product<span *ngIf="isActiveProduct"> ({{ allProductCount }})</span></span>
                </a>
            
                <a style="width: 200px; text-decoration: none; font-weight: 500;" class="profile-tab area-tab-width"
                  [ngClass]="{ active: isInactiveProduct }" (click)="ticketTab('INACTIVE')">
                  <span>Inactive Product<span *ngIf="isInactiveProduct"> ({{ allProductCount }})</span></span>
                </a>
              </div>
            </div>
            </div>
        </div>
      </div>
      <div class="right-column">
        <div class="search-container">
          <div class="input-group">
            <div class="search-input">
              <span class="input-group-add">
                <i class="fa fa-search" aria-hidden="true"></i>
                <input #searchBox class="input-fields" placeholder="Type to search" target [(ngModel)]="model"
                  (ngModelChange)="onSearch($event)" (keypress)="funRestSearchPrevent($event)" />
                <span (click)="clearSearch()" *ngIf="model !== ''">
                  <img title="Clear" src="../../../../assets/img/icons8-cancel-50.png" alt="Example Image" />
                </span>
              </span>
            </div>
            <div class="export-button">
              <button class="add" (click)="onExport()" title="Export">
                <i class="fa fa-share-square-o export-icon"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="brands-grid-data-parent-container">
      <div id="foo" class="brands-grid-data-container">
        <div class="brands-table">
          <dynamic-table 
          [tableHeads]="tableHead"
          [tableData]="brandData"
          [tableConfiguration]="configurationSettings"
          [tableColName]="tableColName" 
          (pageChange)="getBrandsPageData($event)" 
          (onRowEdit)="brandDetails($event)"
          (brandDetails)="brandsData($event)" 
          (onStatusChange)="ChangeStatus($event)"
          [showIndex]="showIndex">
          </dynamic-table>
      
        </div>
      </div>
    </div>
  </div>
</div>
