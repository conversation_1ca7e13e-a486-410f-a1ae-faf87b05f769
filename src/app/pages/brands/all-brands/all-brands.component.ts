import { Component, ViewEncapsulation, ElementRef } from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { GlobalEvents } from "../../../helpers/global.events";
import { UserService } from "../../../app-services/user-service";
import { AppConstant } from "../../../constants/app.constant";
import { FormBuilder } from "@angular/forms";
import { AuthenticationHelper } from "../../../helpers/authentication";
import { ngxCsv } from "ngx-csv";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { DashboardService } from "src/app/app-services/dashboard.service";
import { CommonModule } from "@angular/common";
import { Utility } from "src/app/shared/utility/utility";



import {  OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { PageEvent } from '@angular/material/paginator';
import moment from 'moment';

import { Observable, Subject, debounceTime } from 'rxjs';
import { RewardPointsService } from 'src/app/app-services/reward-points-service';
import { SidebarServiceService } from 'src/app/app-services/sidebar-service.service';
// import { SupportService } from 'src/app/app-services/support.service';

// import * as saveAs from 'file-saver';
// import { AllSupportRoutingModule } from './all-support-routing.module';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
// import { AllSupportComponent } from './all-support.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule } from '@angular/material/paginator';
import { BrandFormComponent } from "../brand-form";
import { DynamicTableComponent } from "src/app/shared/data-table/data-table.component";
import { NumberFormatService } from "src/app/shared/shared/number-format.service";

interface BrandData {
  isActiveProduct: boolean;
  id: number;
  code: string;
  name: string;
  bonusPercentage: number;
  active: boolean;
  pc: string;
  description: string;
  noOfSkus: number;
}
interface ConfigurationSettings {
  showPagination: boolean;
  perPage: number;
  totalRecordCount: number;
  currentPage: number;
  showActionsColumn: boolean;
  actionsColumnName: string;
  productIcon: boolean;
  noDataMessage: string;
  isActiveProduct: boolean; 
  changeStatus: boolean;
  showStatus: boolean;
}
interface ShowOtherFilters {
  showRadioFilters: boolean;
  showSearch: boolean;
  addZone: boolean;
  addUser: boolean;
  addRegion: boolean;
  addTerritory: boolean;
  addNew: boolean;
  add: boolean;
  showdropdown1Filters: boolean;
  showdropdown1Title: string;
  showdropdown2Filters: boolean;
  showSearchiconFilters: boolean;
  showReport: boolean;
  export: boolean;
}
interface BrandEvent {
  id: number;
}
interface BrandInfo {
  brandId: number;
  brandCode: string;
  brandName: string;
  profitCenterName: string;
  brandDescription: string;
  noOfSkus: number;
}
interface ExportInfo {
  materialCode: string;
  productName: string;
  unit: string;
  group: string;
  groupDescription: string;
  grossWeight: string;
  // distribution: string;
  portfolio: string;
  bonusPercentage: number;
}

@Component({
    selector: "nga-all-brands",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["./../brands.component.scss"],
    templateUrl: "all-brands.component.html",
    imports: [DynamicTableComponent, CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatButtonToggleModule,
      MatDatepickerModule,
      MatNativeDateModule,
      MatButtonModule,
      MatSelectModule,
      MatInputModule,
      MatIconModule,
      MatPaginatorModule,
      MatDialogModule,
      AngularMultiSelectModule,
      DynamicTableComponent,
      ],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService,]
})
export class AllBrandsComponent {
  brandData: BrandData[] = [];
  exportData: ExportInfo[] = [];

  tableHead: string[] = [];
  tableColName: string[] = [];
  innerTableHeader: string[] = [];
  innerTableKey:string[] = [];
  showIndex = { index: null };

  isAdmin: boolean = false;
  searchedValue: string = "";
  isSearch: boolean = false;
  PCCode!: string;

  allProductCount: number = 0;
  supportTab: string = 'PROCESSING';
  selectedStatus: string | undefined;
  activeButton: string | undefined;
  selectedDate: string | undefined;
  selectedValue: string = '1';
  isActiveProduct: boolean = false;
  isAllProduct: boolean = true;
  isInactiveProduct: boolean = false;
  model: string = '';
  startDate: any;
  endDate: any;
  category: any = [];
  selectedCategory: string = '';



  configurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: true,
    actionsColumnName: 'Actions',
    productIcon: true,
    noDataMessage: "No data found",
    changeStatus: true,
    isActiveProduct: this.isActiveProduct,
    showStatus: true,
  };
  

  showOtherFilters: ShowOtherFilters = {
    showRadioFilters: false,
    showSearch: true,
    addZone: false,
    addUser: false,
    addRegion: false,
    addTerritory: false,
    addNew: false,
    add: true,
    showdropdown1Filters: true,
    showdropdown1Title: "",
    showdropdown2Filters: false,
    showSearchiconFilters: false,
    showReport: false,
    export: true,
  };

  userButton = [
    {
      path: "/product-catalog/add-crop",
      title: " Add Brands",
    },
  ];
  ismobileViewAllBrands: boolean = false;

  constructor(
    private routes: ActivatedRoute,
    private eRef: ElementRef,
    private dashboardService: DashboardService,
    private router: Router,
    private userService: UserService,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private events: GlobalEvents,
    public fb: FormBuilder,
    private dialog: MatDialog,
    private utility: Utility,
    private numberFormatService: NumberFormatService
  ) {
    this.isAdmin = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;
    this.ismobileViewAllBrands = window.innerWidth <= 1023;
    this.showOtherFilters.showdropdown1Title = "brands";
  }
  ngOnInit() {
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    if (localRole) {
      this.setRoleBasedConfig(localRole);
      this.spinner.hide();
    }
    
    this.userService.userRole$.subscribe(apiRole => {
      const apiRoleFormatted = apiRole?.trim().toUpperCase();
      if (apiRoleFormatted && apiRoleFormatted !== localRole) {
        this.setRoleBasedConfig(apiRoleFormatted);
        this.spinner.hide();
      }
    });    
    
    this.setTableHeader();
    window.scrollTo(0, 0);
  }

  private setRoleBasedConfig(role: string) {
    // Create a completely new configuration object
    const newConfig: ConfigurationSettings = {
      showPagination: this.configurationSettings.showPagination,
      perPage: this.configurationSettings.perPage,
      totalRecordCount: this.configurationSettings.totalRecordCount,
      currentPage: this.configurationSettings.currentPage,
      productIcon: this.configurationSettings.productIcon,
      noDataMessage: this.configurationSettings.noDataMessage,
      isActiveProduct: this.configurationSettings.isActiveProduct,
      changeStatus: this.configurationSettings.changeStatus,
      showStatus: this.configurationSettings.showStatus,
      showActionsColumn: role === 'FINANCE',
      actionsColumnName: role === 'FINANCE' ? 'Actions' : ''
    };
    
    const newFilters: ShowOtherFilters = {
      ...this.showOtherFilters,
      add: role === 'FINANCE'
    };
    
    this.configurationSettings = newConfig;
    this.showOtherFilters = newFilters;
    
    setTimeout(() => {
      this.allBrandsData();
    }, 0);
  }
  
  ngAfterViewInit() {
    this.events.onPCSelect.subscribe((item) => {
      this.configurationSettings.currentPage = 0;
      if (item && item.tab == "brands") {
        if (item && item.pcCode != "all") {
          this.PCCode = item.pcCode;
          this.allBrandsData();
        } else {
          this.PCCode = "";
          this.allBrandsData();
        }
      }
    });
  }

  setTableHeader(): void {
    this.tableHead = [
      "Material Code", 
      "Product Name", 
      "Unit", 
      "Group", 
      "Group Description", 
      "Gross Weight (Kg)",
      // "Profit Center",
      "Portfolio",
      "Bonus%",
      "Segment"
    ];
    
    this.tableColName = [
      "materialCode", 
      "productName", 
      "unit", 
      "group", 
      "groupDescription", 
      "grossWeight",
      // "distribution",
      "portfolio",
      "bonusPercentage",
      "category"
    ];
  
    const pc = String(localStorage.getItem("profitCenter"));
    if (this.isAdmin) {
      this.PCCode = "";
    } else {
      if (pc) {
        this.PCCode = pc;
      }
    }
    this.allBrandsData();
  }
  
handleToggleEdit(brandData: any) {
  this.spinner.show();

  // Determine if we're activating or deactivating
  const isActivating = !brandData.is_active;

  if (!isActivating) {
    // For active to inactive, send raw id and active=false
    this.userService.updateProductToggleActive(brandData.id, false).subscribe({
      next: (response: any) => {
        this.toastr.success('Status updated successfully');
        this.allBrandsData(); // Refresh data
        this.spinner.hide();
      },
      error: (error: any) => {
        this.handleError(error);
      }
    });
  } else {
    // For inactive to active, send raw params
    const bonus = Number(brandData.bonusPercentage) || 0;
    const startDate = brandData.startDate ? new Date(brandData.startDate) : undefined;
    const endDate = brandData.endDate ? new Date(brandData.endDate) : undefined;
    this.userService.updateProductToggle(
      brandData.id,
      true,
      bonus,
      startDate,
      endDate
    ).subscribe({
      next: (response: any) => {
        this.toastr.success('Status updated successfully');
        this.allBrandsData(); // Refresh data
        this.spinner.hide();
      },
      error: (error: any) => {
        this.handleError(error);
      }
    });
  }
}
private handleError(error: any) {
  if (error?.status === 401 || error?.status === 404) {
    localStorage.clear();
    this.router.navigate(['']);
    this.toastr.success('Signed Out Successfully');
  } else {
    let errorMessage = 'Failed to update status';
    try {
      if (error?.error && typeof error.error === 'string') {
        const decrypted = this.utility.decrypt(error.error);
        const parsed = JSON.parse(decrypted);
        errorMessage = parsed.message || errorMessage;
      }
    } catch (decryptError) {
      console.error('Error decrypting error response:', decryptError);
      // Fallback generic message if decrypt or parse fails
    }
    this.toastr.error(errorMessage);
  }
  this.spinner.hide();
}


   
  brandDetails(event: BrandEvent): void {
    this.router.navigate(["product-catalog/edit-crop"], {
      queryParams: { id: event.id },
    });
  }

  clearSearch() {
    this.searchedValue = '';
    this.model = '';
    this.isSearch = false; // Reset search state
    this.getBrandsPageData(1); // Refresh data with empty search
  }

  funRestSearchPrevent(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
      // Allow uppercase letters, lowercase letters, backspace, space, and numbers
    } else {
      event.preventDefault();
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }


    handleAddClick(){
      this.dialog.open(BrandFormComponent).afterClosed().subscribe((result: any)=>{})
    }
    ticketTab(tab: string) {
      this.spinner.show();
      this.allProductCount = 0;
      this.supportTab = tab;
      this.isActiveProduct = tab === 'ACTIVE';
      this.isAllProduct = tab === 'ALLPRODUCT';
      this.isInactiveProduct = tab === 'INACTIVE';
      this.model = '';
      this.searchedValue = '';
      this.category = '';
      this.selectedCategory = '';
      this.configurationSettings.currentPage = 1;
      this.allBrandsData();
  }  
      
  /**
   * API call for getting all brands details
   * @param page number
   */
  allBrandsData(page?: number): void {
    this.spinner.show();
    let data = {
      pageLimit: AppConstant.PER_PAGE_ITEMS,
      currentPage: page ? page - 1 : 0,
      profitCenter: AuthenticationHelper.getProfitCenter(),
      profitCenterCode: this.PCCode || "",
      searchedValue: this.searchedValue ? this.searchedValue.trim() : ""
    };

    // Add isActive parameter based on the selected tab
    if (this.isActiveProduct) {
      (data as any)['isActive'] = true;
    } else if (this.isInactiveProduct) {
      (data as any)['isActive'] = false;
    }
    // For "All Products" tab (this.isAllProduct), we don't send isActive parameter

    this.userService.getAllProduct(data).subscribe({
      next: (response: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = response;
          if (typeof response === 'string') {
            try {
              parsedResponse = JSON.parse(response);
            } catch (e) {
              // If parsing fails, try to decrypt first
              try {
                const decrypted = this.utility.decrypt(response);
                parsedResponse = JSON.parse(decrypted);
              } catch (decryptError) {
                console.warn('Failed to decrypt response:', decryptError);
              }
            }
          }
          
          // Check if response has encryptedBody property
          let brands;
          if (parsedResponse && parsedResponse.encryptedBody) {
            try {
              // Decrypt the encryptedBody
              const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
              brands = JSON.parse(decrypted);
            } catch (decryptError) {
              console.warn('Failed to decrypt encryptedBody:', decryptError);
              brands = parsedResponse;
            }
          } else {
            brands = parsedResponse;
          }
          
          // Only call removeSuffix if brands is a string
          if (typeof brands === 'string') {
            brands = this.dashboardService.removeSuffix(brands);
          } else if (brands && typeof brands === 'object') {
            // If it's already an object, use it directly
            // No need to call removeSuffix
          } else {
            console.warn('Unexpected response format:', brands);
            this.brandData = [];
            this.configurationSettings.totalRecordCount = 0;
            this.spinner.hide();
            return;
          }
          
          if (!brands?.content || !Array.isArray(brands.content)) {
            this.brandData = [];
            this.configurationSettings.totalRecordCount = 0;
            this.spinner.hide();
            return;
          }
          
          this.brandData = brands.content?.map((brandInfo: any) => ({
            materialCode: brandInfo.materialCode || 'NA',
            productName: brandInfo.productName || 'NA',
            unit: brandInfo.unit ? brandInfo.unit.toUpperCase() : 'NA',
            group: brandInfo.groupCode || 'NA',
            groupDescription: brandInfo.groupDescription || 'NA',
            grossWeight: brandInfo.grossWeight || 'NA',
            distribution: brandInfo.distributorName || 'NA',
            portfolio: brandInfo.portfolio || 'NA',
            id: brandInfo.id,
            active: brandInfo.active,
            is_active: brandInfo.active || false,
            amount: brandInfo.amount,
            bonification: brandInfo.bonification,
            bonusPercentage: this.numberFormatService.formatPercentage(brandInfo.bonusPercentage) || 0,
            category: brandInfo.category ? brandInfo.category.toUpperCase() : 'NA',
            skuCode: brandInfo.skuCode,
            stateId: brandInfo.stateId,
            stateName: brandInfo.stateName,
            unitCost: brandInfo.unitCost,
            zoneId: brandInfo.zoneId,
            zoneName: brandInfo.zoneName,
            quantity: brandInfo.quantity,
          }));

          // Set total count from API response
          this.allProductCount = brands.totalElements || 0;
          this.configurationSettings.totalRecordCount = brands.totalElements || 0;
          
          this.events.setChangedContentTopText(`Product Catalog`);
          window.scrollTo(0, 0);
          this.spinner.hide();
        } catch (error) {
          console.error('Error processing brands data:', error);
          this.brandData = [];
          this.configurationSettings.totalRecordCount = 0;
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          if (errorResponse?.error && typeof errorResponse.error === 'string') {
            try {
              const decrypted = this.utility.decrypt(errorResponse.error);
              const errorObj = JSON.parse(decrypted);
              if (errorObj.status === 401 || errorObj.status === 404) {
                localStorage.clear();
                this.router.navigate([""]);
                this.toastr.success("Signed Out Successfully");
              } else {
                this.toastr.error(errorObj.message || AppConstant.ERR_FETCHING_BRANDS);
              }
            } catch (decryptError) {
              console.warn('Failed to decrypt error response:', decryptError);
              let errorMsg = errorResponse.status;
              if (+errorMsg === 401 || +errorMsg === 404) {
                localStorage.clear();
                this.router.navigate([""]);
                this.toastr.success("Signed Out Successfully");
              } else {
                this.toastr.error(AppConstant.ERR_FETCHING_BRANDS);
              }
            }
          } else {
            let errorMsg = errorResponse.status;
            if (+errorMsg === 401 || +errorMsg === 404) {
              localStorage.clear();
              this.router.navigate([""]);
              this.toastr.success("Signed Out Successfully");
            } else {
              this.toastr.error(AppConstant.ERR_FETCHING_BRANDS);
            }
          }
        } catch (e) {
          console.error('Error handling error response:', e);
          this.toastr.error(AppConstant.ERR_FETCHING_BRANDS);
        }
        this.spinner.hide();
      },
    });
  }
  
  ChangeStatus(event: any) {
    if (event?.id) {
      this.handleToggleEdit(event);
    }
  }
    
  /**
   * To handle the page change event
   * @param page
   */
  brandsData(event: BrandEvent): void {
    this.router.navigate(["brands/sku"], { queryParams: { id: event.id } });
  }
  onWindowResizeBrands(): void {
    this.ismobileViewAllBrands = window.innerWidth <= 1023 ? true : false;
  }
  /**
   *  Displays the brands data according to the searched query
   *  @param searched query
   */
  getBrandsPageData(page: number): any {
    this.configurationSettings.currentPage = page;
    this.allBrandsData(page); // Always passing scanCategoryId as 1
  }
  
  onSearch(event: string): void {
    const trimmedValue = event.trim();
    
    // Update search state only if there's an actual change
    if (this.searchedValue !== trimmedValue) {
      this.isSearch = Boolean(trimmedValue);
      this.searchedValue = trimmedValue;
      this.getBrandsPageData(1);
    }
  }
  
  
  /**
   *  Method to export the data in CSV format
   */
  onExport(): void {
    this.getBrandsExportData();
  }

  // onExport(event: any) {
  //   if (event) {
  //     this.getSupportExportData();
  //   }
  // }
  getBrandsExportData(): void {
    window.scrollTo(0, 0);
    this.spinner.show();
    let data = {
      profitCenter: AuthenticationHelper.getProfitCenter(),
      profitCenterCode: this.PCCode ? this.PCCode : "",
      searchedValue: this.searchedValue ? encodeURIComponent(this.searchedValue) : '',
      unPaged: true
    };

    // Add isActive parameter based on the current tab
    if (this.isActiveProduct) {
      (data as any)['isActive'] = true;
    } else if (this.isInactiveProduct) {
      (data as any)['isActive'] = false;
    }
    // For "All Products" tab, we don't send isActive parameter

    this.userService.getAllProductExport(data).subscribe({
      next: (response: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = response;
          if (typeof response === 'string') {
            try {
              parsedResponse = JSON.parse(response);
            } catch (e) {
              // If parsing fails, try to decrypt first
              try {
                const decrypted = this.utility.decrypt(response);
                parsedResponse = JSON.parse(decrypted);
              } catch (decryptError) {
                console.warn('Failed to decrypt response:', decryptError);
              }
            }
          }
          
          // Check if response has encryptedBody property
          let exportsData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            try {
              // Decrypt the encryptedBody
              const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
              exportsData = JSON.parse(decrypted);
            } catch (decryptError) {
              console.warn('Failed to decrypt encryptedBody:', decryptError);
              exportsData = parsedResponse;
            }
          } else {
            // Use the response directly
            exportsData = parsedResponse;
          }
          
          // Only call removeSuffix if exportsData is a string
          if (typeof exportsData === 'string') {
            exportsData = this.dashboardService.removeSuffix(exportsData);
          } else if (exportsData && typeof exportsData === 'object') {
            // If it's already an object, use it directly
            // No need to call removeSuffix
          } else {
            console.warn('Unexpected response format:', exportsData);
            this.toastr.warning("No data available to export!");
            this.spinner.hide();
            return;
          }
          
          this.exportData = [];
          if ((exportsData) && exportsData.content && exportsData.content.length) {
            exportsData.content.forEach((exportInfo: any) => {
              let exportObj = {
                materialCode: exportInfo.materialCode || 'NA',
                productName: exportInfo.productName || 'NA',
                unit: exportInfo.unit || 'NA',
                group: exportInfo.groupCode || 'NA',
                groupDescription: exportInfo.groupDescription || 'NA',
                grossWeight: exportInfo.grossWeight || 'NA',
                // distribution: exportInfo.distributorName || 'NA',
                portfolio: exportInfo.portfolio || 'NA',
                bonusPercentage: exportInfo.bonusPercentage || 0,
                category: exportInfo.category ? exportInfo.category.toUpperCase() : 'NA',
                active: exportInfo.active ? 'Active' : 'Inactive',
              };
              this.exportData.push(exportObj);
            });
            let options = {
              fieldSeparator: ",",
              quoteStrings: '"',
              decimalseparator: ".",
              showLabels: true,
              headers: [
                "Material Code", 
                "Product Name", 
                "Unit", 
                "Group", 
                "Group Description", 
                "Gross Weight (Kg)",
                "Portfolio",
                "Bonus%",
                "Segment",
                "Status",
              ],
            };
            new ngxCsv(this.exportData, "Product Catalog", options);
          } else {
            this.toastr.warning("No data available to export!");
          }
          this.spinner.hide();
        } catch (error) {
          console.error('Error parsing export data:', error);
          this.toastr.error(AppConstant.EXPORT_ERROR);
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          if (errorResponse?.error && typeof errorResponse.error === 'string') {
            try {
              const decrypted = this.utility.decrypt(errorResponse.error);
              const errorObj = JSON.parse(decrypted);
              this.toastr.error(errorObj.message || AppConstant.EXPORT_ERROR);
            } catch (decryptError) {
              console.warn('Failed to decrypt error response:', decryptError);
              this.toastr.error(AppConstant.EXPORT_ERROR);
            }
          } else {
            this.toastr.error(AppConstant.EXPORT_ERROR);
          }
        } catch (e) {
          console.error('Error handling error response:', e);
          this.toastr.error(AppConstant.EXPORT_ERROR);
        }
        this.spinner.hide();
      },
    });
  }
}
