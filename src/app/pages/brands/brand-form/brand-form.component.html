<div class="brand-form-container">
  <div class="brand-form-content">
    <form [formGroup]="brandForm">
      <div class="col-md-12 row brand-form">
        <div class="width-100 float-left">
          <h3 class="brand-form-info-title">
            {{ brandId ? "Update Product" : "Add Product" }}
          </h3>
          <div class="brand-form-information-section">
            <div class="col-md-4 bottom-padding">
              <div>
                <label class="label-style"
                  >Brand Name<i class="required">&nbsp;*</i></label
                >
                <span *ngIf="brandId">
                  <input
                    formControlName="brandName"
                    type="text"
                    minlength="3"
                    readonly
                    maxlength="50"
                    class="form-control"
                    (keypress)="funRestSearchPrevent($event)"
                    [disabled]="isBrandIdNotNull()"
                  />

                  
                </span>
                <span *ngIf="!brandId">
                  <input
                    formControlName="brandName"
                    type="text"
                    minlength="3"
                    maxlength="50"
                    class="form-control"
                    (keypress)="funRestSearchPrevent($event)"
                    [disabled]="isBrandIdNotNull()"
                  />
                </span>
              </div>
              <span
                class="error-message"
                *ngIf="
                  brandForm.get('brandName')?.hasError('required') &&
                  brandForm.get('brandName')?.touched
                "
              >
                Brand name is required
              </span>
              <span
                class="error-message"
                *ngIf="
                  brandForm.get('brandName')?.hasError('minlength') &&
                  brandForm.get('brandName')?.touched
                "
              >
                Brand name should be at least 3 characters
              </span>
            </div>

            <div class="col-md-4 bottom-padding">
              <div>
                <label class="label-style"
                  >Select Crop Type<i class="required">&nbsp;*</i></label
                >
                <angular2-multiselect
                  [data]="cropDetails"
                  [(ngModel)]="crops"
                  [settings]="cropDropdownSettings"
                  (onSelect)="selectedCrop($event)"
                  (onDeSelect)="deSelectedCrop($event)"
                  (onSelectAll)="selectedAllCrop($event)"
                  (onDeSelectAll)="deSelectedAllCrop($event)"
                  [ngModelOptions]="{ standalone: true }"
                >
                </angular2-multiselect>
              </div>
              <div class="error-message">
                <span
                  *ngIf="showCropTypeError"
                  class="help-block sub-little-error confpass"
                >
                  Crop type is required
                </span>
              </div>
            </div>
            <div class="col-md-4 bottom-padding">
              <div class="packshot-ui">
                <label class="label-style"
                  >Add Packshot<i class="required">&nbsp;*</i></label
                >
                <span class="input-file" (click)="showErrorMessage()">
                  <input
                    #fileInput
                    type="file"
                    style="display: none"
                    accept=".jpg,.jpeg,.png"
                    (change)="onFileSelected($event)"
                  />
                  <input
                    readonly
                    type="text"
                    [value]="imageName"
                    [src]="imageName | AuthImagePipe | async"
                    (click)="fileInput.click()"
                  />
                  <i
                    (click)="fileInput.click()"
                    class="fa fa-upload upload-icon"
                    aria-hidden="true"
                  ></i>
                </span>
              </div>
              <span
                class="error-message"
                *ngIf="showPackshotErrorMessage && !fileUploaded && !imageName"
              >
                Packshot is required
              </span>
            </div>
            <div class="col-md-8 bottom-padding" style="margin-top: 10px">
              <div>
                <label class="label-style"
                  >Brand Description<i class="required">&nbsp;*</i></label
                >
                <textarea
                  formControlName="description"
                  class="form-control"
                  minlength="25"
                  maxlength="300"
                  #description
                  (keypress)="descriptionValidation($event)"
                ></textarea>
                <div id="hint">
                  <mat-hint> {{ description.value.length }}/300</mat-hint>
                </div>
                <span
                  class="error-message"
                  *ngIf="
                    brandForm.get('description')?.hasError('required') &&
                    brandForm.get('description')?.touched
                  "
                >
                  Brand description is required
                </span>
                <span
                  class="error-message"
                  *ngIf="
                    brandForm.get('description')?.hasError('minlength') &&
                    brandForm.get('description')?.touched
                  "
                >
                  Brand description should be at least 25 characters
                </span>
              </div>
            </div>
            <div class="col-md-4 bottom-padding" style="margin-top: 10px">
              <div *ngIf="!brandId">
                <label class="label-style">Business Unit</label>
                <angular2-multiselect
                  [data]="bussinessUnitDetails"
                  [(ngModel)]="bussinessUnit"
                  [settings]="bussinessUnitDropdownSettings"
                  (onSelect)="selectedBU($event)"
                  (onDeSelect)="deSelectedBU($event)"
                  (onSelectAll)="selectedAllBU($event)"
                  (onDeSelectAll)="deSelectedAllBU($event)"
                  [ngModelOptions]="{ standalone: true }"
                >
                </angular2-multiselect>
              </div>
              <div *ngIf="brandId">
                <label class="label-style">Business Unit</label>
                <angular2-multiselect
                  [data]="bussinessUnitDetails"
                  [(ngModel)]="bussinessUnit"
                  [settings]="bussinessUnitDropdownSettingsDisabled"
                  (onSelect)="selectedBU($event)"
                  (onDeSelect)="deSelectedBU($event)"
                  (onSelectAll)="selectedAllBU($event)"
                  (onDeSelectAll)="deSelectedAllBU($event)"
                  [ngModelOptions]="{ standalone: true }"
                >
                </angular2-multiselect>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="horizontalLine"></div>
  <div class="add-button">
    <form [formGroup]="skuForm">
      <span class="number-of-skus"
        >{{ "Total Created SKU’s:" }}
        <span id="sku-number">
          {{ numberOfCreatedSKUs }}
        </span>
      </span>
      <button
        type="button"
        class="add"
        title="Add"
        (click)="addRowSlabs()"
        [disabled]="!skuForm.valid"
        [ngClass]="{ 'disabled-button': !skuForm.valid }"
      >
        <i class="fa fa-plus export-icon"></i>&nbsp;&nbsp;
        {{ "Add SKU" }}
      </button>
    </form>
  </div>
  <div class="brand-form-content sku-details-form">
    <form [formGroup]="skuForm" class="form-container">
      <div class="col-md-12 row brand-form" formArrayName="sectionFormArray">
        <div
          class="width-100 float-left add-btn-container"
          *ngFor="let item of sectionFormArray.controls; let i = index"
          [formGroupName]="i"
        >
          <div class="brand-form-information-section slab-container">
            <div class="parent-bottom-container col-md-12">
              <div class="bottom-padding form-row2">
                <button
                  *ngIf="i > 0"
                  class="btn float-end mb-3 deleteBtn"
                  (click)="deleteRowSlabs(i)"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </div>
              <div *ngIf="i == 0" style="margin-left: 16px; font-weight: 600">
                {{ "SKU :" }} {{ i + 1 }}
              </div>
              <div
                *ngIf="i > 0"
                style="
                  margin-left: 16px;
                  font-weight: 600;
                  position: relative;
                  top: 15px;
                  width: 80%;
                "
              >
                {{ "SKU :" }} {{ i + 1 }}
              </div>
            </div>
            <div class="col-md-4 bottom-padding">
              <div>
                <label class="label-style"
                  >SKU Name<i class="required">&nbsp;*</i></label
                >
                <input
                  type="text"
                  class="form-control"
                  formControlName="skuName"
                  minlength="3"
                  maxlength="50"
                  (keypress)="funRestSearchPrevent($event)"
                  (input)="trimInput($event)"
                />
                <span
                  class="error-message"
                  *ngIf="
                    skuForm
                      .get('sectionFormArray.' + i + '.skuName')
                      ?.hasError('required') &&
                    skuForm.get('sectionFormArray.' + i + '.skuName')?.touched
                  "
                >
                  SKU Name is required
                </span>
                <span
                  class="error-message"
                  *ngIf="
                    skuForm
                      .get('sectionFormArray.' + i + '.skuName')
                      ?.hasError('minlength') &&
                    skuForm.get('sectionFormArray.' + i + '.skuName')?.touched
                  "
                >
                  SKU Name should be at least 3 characters
                </span>
              </div>
            </div>

            <div class="col-md-4 bottom-padding">
              <div>
                <label class="label-style"
                  >SKU Code<i class="required">&nbsp;*</i></label
                >
                <input
                  type="text"
                  class="form-control"
                  formControlName="skuCode"
                  maxlength="12"
                  minlength="8"
                  (keypress)="funRestAllowOnlyNumberPrevent($event)"
                />
                <span
                  class="error-message"
                  *ngIf="
                    skuForm
                      .get('sectionFormArray.' + i + '.skuCode')
                      ?.hasError('required') &&
                    skuForm.get('sectionFormArray.' + i + '.skuCode')?.touched
                  "
                >
                  SKU Code is required
                </span>
                <span
                  class="error-message"
                  *ngIf="
                    skuForm
                      .get('sectionFormArray.' + i + '.skuCode')
                      ?.hasError('minlength') &&
                    skuForm.get('sectionFormArray.' + i + '.skuCode')?.touched
                  "
                >
                  SKU Code should be at least 8 characters
                </span>
              </div>
            </div>
            <div class="col-md-4 bottom-padding">
              <div class="packshot-ui">
                <label class="label-style"
                  >Pack Quantity (Kg)<i class="required">&nbsp;*</i></label
                >
                <input
                  type="text"
                  class="form-control input-padding"
                  formControlName="quantity"
                  maxlength="6"
                  minlength="1"
                  (input)="onInputChange($event)"
                  (keypress)="numberValidation($event)"
                  (paste)="onPaste($event)"
                />
                <span
                  class="error-message"
                  *ngIf="
                    skuForm
                      .get('sectionFormArray.' + i + '.quantity')
                      ?.hasError('required') &&
                    skuForm.get('sectionFormArray.' + i + '.quantity')?.touched
                  "
                >
                  Quantity is required
                </span>
                <span
                  class="error-message"
                  *ngIf="
                    skuForm
                      .get('sectionFormArray.' + i + '.quantity')
                      ?.hasError('pattern') &&
                    skuForm.get('sectionFormArray.' + i + '.quantity')?.touched
                  "
                >
                  Please enter a valid weight (up to 3 decimal places)
                </span>
              </div>
            </div>
            <div
              class="col-md-12 bottom-padding"
              style="margin: 10px 0px 10px 0px"
            >
              <div>
                <label class="label-style"
                  >SKU Description<i class="required">&nbsp;*</i></label
                >
                <textarea
                  style="height: 60px"
                  formControlName="description"
                  class="form-control"
                  minlength="25"
                  maxlength="300"
                  #productDescription
                  formControlName="description"
                  (keypress)="descriptionValidation($event)"
                ></textarea>
                <mat-hint> {{ productDescription.value.length }}/300</mat-hint>
                <span
                  class="error-message"
                  *ngIf="
                    skuForm
                      .get('sectionFormArray.' + i + '.description')
                      ?.hasError('required') &&
                    skuForm.get('sectionFormArray.' + i + '.description')
                      ?.touched
                  "
                >
                  SKU Description is required
                </span>
                <span
                  class="error-message"
                  *ngIf="
                    skuForm
                      .get('sectionFormArray.' + i + '.description')
                      ?.hasError('minlength') &&
                    skuForm.get('sectionFormArray.' + i + '.description')
                      ?.touched
                  "
                >
                  SKU Description should be at least 25 characters
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="button-container row">
    <button type="button" class="btn-cancel" (click)="backScreen()"
    mat-dialog-close>
      {{ "Cancel" }}
    </button>
    <button
      type="button"
      class="btn-submit"
      type="submit"
      (click)="onSubmit(brandForm.value, skuForm.value)"
      [disabled]="isFormInvalid()"
      [ngClass]="{ 'disabled-button': isFormInvalid() }"
      mat-button
      [mat-dialog-close]="true"
    >
      {{ "Submit" }}
    </button>
  </div>
</div>
