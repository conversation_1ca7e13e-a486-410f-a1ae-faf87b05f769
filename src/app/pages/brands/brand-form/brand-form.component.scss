@import "../../../theme/sass/auth";
@import "../../users/users";
@import "../../../../styles";
.form-control[readonly] {
  color: #464a4c;
  cursor: pointer;
  border: 1px solid #1a3661;
}
.disabled-button {
  opacity: 0.6;
  cursor: not-allowed;
}

.bottom-padding {
  padding-right: 15px;
  padding-left: 15px;
  textarea {
    resize: none;
  }
  .brand-form-label {
    margin: 5px 26px 0 5px;
    cursor: pointer;
  }
  .brand-form-status {
    .btn:disabled {
      cursor: not-allowed;
      opacity: 0.9;
    }
  }

  #hint {
    float: right;
  }
}

.sku-description {
  padding-bottom: 20px;
  padding-top: 22px;
  textarea {
    resize: none;
    height: 90px;
    border-radius: 3px;
  }
}

.width-100 {
  width: 100%;
}

.brand-form-container {
  background: white;
  min-width: 95%;
  border-radius: $border-radius;
  display: grid;
  height: 100%;

  .input-button {
    text-align: center;
    margin: 1.9% 0 2% 0;
  }
  .padding-left-right {
    padding-left: 30px;
    padding-right: 30px;
  }
  .form-control {
    border-radius: $border-radius;
    border: 1px solid #1a3661;
    height: 35px;
  }
  .input-padding {
    padding-right: 30px;
  }
  .form-control[disabled] {
    color: grey;
    border-color: lightgrey;
  }
  .disable-submit {
    opacity: 0.8;
    cursor: not-allowed;
    &:hover {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }
  .add-button {
    margin: 18px 10px 0px 10px;
    float: right;
    button {
      width: 125px;
      height: 35px;
      background-color: #fff;
      border-radius: 0.25rem;
      border: 1px solid #1a3661;
      color: #1a3661;
      font-weight: 500;
      float: right;
      margin-right: 24px;
    }

    #sku-number {
      font-size: 16px;
      font-weight: 500;
    }
  }
  .brand-form-content {
    margin-top: 2%;
    margin-right: 0;
    max-height: 210px;
    overflow-y: scroll;
    .brand-form {
      margin-left: 0;
      .form-row2 {
        float: right;
        margin-right: 30px;

        button {
          margin: 10px;
          width: 50px;
          background-color: #fff;
          border-radius: 0.25rem;
          border: 1px solid #1a3661;
          color: #1a3661;
          font-weight: 500;
        }
      }
      .brand-form-information-section {
        width: 100%;
        float: left;
        padding: 10px 0 0 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        .bottom-padding {
          .packshot-ui {
            input {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              border: none;
            }
            .form-control {
              border-radius: $border-radius;
              border: 1px solid #1a3661;
              height: 35px;
            }
            .input-file {
              display: flex;
              align-items: center;
              border: 1px solid #1a3661;
              border-radius: $border-radius;
              height: 34px;
              cursor: pointer;
              input {
                width: 90%;
                margin-right: 2px;
                margin-left: 2px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                outline: none;
                cursor: pointer;
              }
            }
          }
        }
        .PC-dropdown {
          width: 100%;
          float: left;
          height: 35px;
          &:disabled {
            cursor: not-allowed;
            opacity: 0.8;
          }
        }
      }
      .slab-container {
        box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;
        margin-top: 2px;
      }
    }
    .brand-form-info-title {
      font-weight: 700;
      color: #374767;
      float: left;
      width: 100%;
      margin: 0;
      padding: 0;
      opacity: 0.9;
    }
  }
  .btn-style {
    float: right;
    width: auto;
    font-size: 12px;
    min-width: 150px;
    height: auto;
    @media screen and (max-width: 767px) {
      width: 80%;
    }
  }
  .disable-submit {
    opacity: 0.8;
    cursor: not-allowed;
    &:hover {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }
  .help-block {
    color: red;
  }

  .button-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 5%;
    button {
      height: 40px;
      width: 20%;
      border: none;
      border-radius: 5px;
    }
    .btn-cancel {
      background-color: #fff;
      color: #1a3661;
      font-family: sans-serif;
      font-weight: 600;
      border: 1px solid #1a3661;
    }
    .btn-submit {
      background-color: #1a3661;
      color: #fff;
      font-family: $sans-font-family;
      font-weight: 600;
    }
  }
}
.error-message {
  font-size: 12px;
}

@media only screen and (min-device-width: 310px) and (max-device-width: 320px) {
  .brand-form {
    padding-right: 0 !important;
  }
}

@media only screen and (min-width: 320px) and (max-width: 767px) {
  .brand-form-container {
    .input-button {
      display: flex;
      .btn-cancel-style {
        min-width: 120px;
        margin-right: 10px;
      }
    }
  }
}

:host ::ng-deep .brands-multi-select .cuppa-dropdown .selected-list .c-btn {
  border-radius: 3px !important;
  padding: 8px;
  height: 35px !important;
}

:host ::ng-deep .selected-list .c-list .c-token {
  font-size: 13px !important;
}

:host ::ng-deep .selected-list .c-list .c-token {
  width: 75%;
  list-style: none;
  border-radius: 2px;
  margin-right: 4px;
  float: left;
  position: relative;
  padding: 4px 22px 4px 8px;
}
.number-of-skus {
  margin-left: 15px;
  font-weight: 600;
  position: relative;
  top: 4px;
}
.horizontalLine {
  width: 93%;
  background: #d7d7d7;
  height: 1px;
  position: relative;
  left: 27px;
  margin-top: 10px;
}
