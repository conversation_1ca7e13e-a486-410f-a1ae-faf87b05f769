import { Component } from "@angular/core";
import { Location, NgIf, NgClass, AsyncPipe } from "@angular/common";
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormsModule,
  FormArray,
  ReactiveFormsModule,
} from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { Router, ActivatedRoute } from "@angular/router";
import { GlobalEvents } from "../../../helpers/global.events";
import { UserService } from "../../../app-services/user-service";
import { AppConstant } from "../../../constants/app.constant";
// import { Observable } from "rxjs";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { DashboardService } from "src/app/app-services/dashboard.service";
import { MatIconModule } from "@angular/material/icon";
import { AuthImagePipe } from "src/app/app-services/auth-image.pipe";
import { UtilityHelper } from "src/app/helpers/utility";
import { AdminService } from "src/app/app-services/admin.service";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatDialogModule, MatDialogRef } from "@angular/material/dialog";
import { MatButtonModule } from "@angular/material/button";
import { AngularMultiSelectModule } from "angular2-multiselect-dropdown";

interface BrandInfo {
  id?: string;
  code?: string;
  name?: string;
  description?: string;
  profitCenterName?: string;
}
interface BrandFormValues {
  name: string;
  description: string;
  pc: string;
  profitCenterCode: string;
  imageURL:string,
}

interface BrandDataget {
  name?: string;
  desc?: string;
  pc?: string;
  image:string,
}
@Component({
    selector: "nga-add-brand",
    styleUrls: ["brand-form.component.scss"],
    templateUrl: "brand-form.component.html",
    imports: [FormsModule, ReactiveFormsModule, NgIf, NgClass, MatIconModule, AuthImagePipe, MatFormFieldModule, AsyncPipe,MatDialogModule, MatButtonModule, AngularMultiSelectModule],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class BrandFormComponent {
  brandForm!: FormGroup;
  brandId: number;
  brandData!: {};
  skuForm!: FormGroup;
  packshot: string = '';
  fileUploaded: boolean = false;
  numberOfCreatedSKUs: number = 1;
  formData: FormData = new FormData();
  cropDetails: any = [];
  crops: any = [];
  selectedCropType: number = 0;
  showCropTypeError: boolean = false;
  bussinessUnitDetails: any = [];
  bussinessUnit: any = [];
  selectedBussinessUnit: number = 0;
  showPackshotErrorMessage = false;
  dataArray: any = [];
  dataArry: any;
  cropTab: any;
  cropDropdownSettings = {
    text: 'Select Crop',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  bussinessUnitDropdownSettings = {
    text: 'Select Business Unit',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  bussinessUnitDropdownSettingsDisabled = {
    text: 'Select Business Unit',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: true,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  imageName: string = '';

  constructor(
    private fb: FormBuilder,
    public location: Location,
    private router: Router,
    public events: GlobalEvents,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private userService: UserService,
    private utility: UtilityHelper,
    public dialogRef: MatDialogRef<BrandFormComponent>,
    private adminService: AdminService
  ) {
    this.brandId = adminService.getEditBrandID();
  }

  ngOnInit() {
    window.scrollTo(0, 0);
    this.getcropType();
    this.getBussinessUnit();
    this.setUserForm();
    this.initializeSKUForms();
    this.cropTab = this.adminService.getCropType();
    if (this.brandId) {
      this.brandDetails();
    }
  }

  ngOnDestroy() {
    localStorage.removeItem('brand_id');
  }

  selectedCrop(event: any) {
    this.selectedCropType = event.id;
    this.showCropTypeError = false;
  }

  deSelectedCrop(event: any) {
    if (this.crops.length === 0) {
      this.showCropTypeError = true;
      this.selectedCropType = 0;
    }
  }

  isBrandIdNotNull(): boolean {
    return this.brandId != 0;
  }

  selectedAllCrop(event: any) {}

  deSelectedAllCrop(event: any) {
    if (this.crops.length === 0) {
      this.showCropTypeError = true;
      this.selectedCropType = 0;
    }
  }

  selectedBU(event: any) {
    this.selectedBussinessUnit = event.id;
  }

  deSelectedBU(event: any) {
    this.selectedBussinessUnit = 0;
  }

  selectedAllBU(event: any) {}

  deSelectedAllBU(event: any) {}

  brandDetails() {
    this.spinner.show();
    let data = {
      id: this.brandId,
    };
    const brandDetails = this.userService.getBrandDetailsByID(data);
    brandDetails.subscribe({
      next: (brandInfoRes: any) => {
        let brandInfo = JSON.parse(this.utility.decrypt(brandInfoRes));
        const parts = brandInfo?.packshot.split('/');
        this.imageName = parts[parts.length - 1];
        if (brandInfo) {
          this.brandData = {
            id: brandInfo.id ? brandInfo.id : '',
            code: brandInfo.code ? brandInfo.code : '',
            cropId: brandInfo.cropId ? brandInfo.cropId : '',
            name: brandInfo.name ? brandInfo.name : '',
            description: brandInfo.description ? brandInfo.description : '',
            packshot: brandInfo.packshot ? brandInfo.packshot : '',
            scanCategoryId: brandInfo.scanCategoryId
              ? brandInfo.scanCategoryId
              : '',
            skus: brandInfo.skus ? brandInfo.skus : '',
            numberOfSku: brandInfo.numberOfSku ? brandInfo.numberOfSku : '',
            active: brandInfo.active ? brandInfo.active : '',
            uCropId: brandInfo.uCropId ? brandInfo.uCropId : '',
            uId: brandInfo.uId ? brandInfo.uId : '',
            uScanCategoryId: brandInfo.uScanCategoryId
              ? brandInfo.uScanCategoryId
              : '',
          };
          this.cropDetails.forEach((item: any) => {
            if (item.id === brandInfo.cropId) {
              this.crops.push(item);
              this.selectedCropType = brandInfo.cropId;
            }
          });
          this.packshot = brandInfo.packshot;
          this.bussinessUnitDetails.forEach((item: any) => {
            if (item.id === brandInfo.scanCategoryId) {
              this.bussinessUnit.push(item);
            } else if (this.selectedBussinessUnit !== 0) {
              this.selectedBussinessUnit = brandInfo.scanCategoryId;
            }
          });
          this.setUserForm(this.brandData);
          this.skuResponse(this.brandData);
        }
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
        let error:any =this.utility.decrypt(errorResponse.error)
        error= JSON.parse(error);
        if('Full authentication is required to access this resource'==error.message){
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
          this.router.navigate(['brands']);
        }
      },
    });
  }

  extractFileNameFromURL(url: string) {
    const parts = url.split('/');
    const filenameWithExtension = parts[parts.length - 1];
    const filenameParts = filenameWithExtension.split('-');
    const filename = filenameParts.slice(1).join('-');
    return filename;
  }

  /**
   *  To set user form values
   *  @param user form values
   */
  setUserForm(values?: any) {
    this.brandForm = this.fb.group({
      brandName: [
        values ? values.name : '',
        Validators.compose([Validators.required]),
      ],
      description: [
        values ? values.description : '',
        Validators.compose([Validators.required]),
      ],
    });
    this.spinner.hide();
  }

  skuResponse(values: any) {
    const skusArray = values.skus;
    this.numberOfCreatedSKUs = skusArray.length;
    for (let i = 0; i < skusArray.length; i++) {
      if (i < this.sectionFormArray.length) {
        const skuFormGroup = this.sectionFormArray.at(i) as FormGroup;
        skuFormGroup.patchValue({
          skuName: skusArray[i].name,
          skuCode: skusArray[i].code,
          quantity: skusArray[i].quantity,
          description: skusArray[i].description,
          uId: skusArray[i].uId,
          id: skusArray[i].id,
        });
      } else {
        const skuFormGroup = this.createItem();
        skuFormGroup.patchValue({
          skuName: skusArray[i].name,
          skuCode: skusArray[i].code,
          quantity: skusArray[i].quantity,
          description: skusArray[i].description,
          uId: skusArray[i].uId,
          id: skusArray[i].id,
        });
        this.sectionFormArray.push(skuFormGroup);
      }
    }
  }

  get sectionFormArray(): any {
    return this.skuForm.get('sectionFormArray') as FormArray;
  }
  initializeSKUForms(): void {
    this.skuForm = this.fb.group({
      sectionFormArray: this.fb.array([this.createItem()]),
    });
  }

  createItem(): FormGroup {
    return this.fb.group({
      skuName: [null, Validators.compose([Validators.required])],
      skuCode: [null, Validators.compose([Validators.required])],
      quantity: [
        null,
        Validators.compose([
          Validators.required,
          Validators.pattern(/^\d+(\.\d{1,3})?$/),
        ]),
      ],
      description: [null, Validators.compose([Validators.required])],
      uId: [null],
      id: [null],
    });
  }

  addRowSlabs(): void {
    this.sectionFormArray.push(this.createItem());
    this.numberOfCreatedSKUs = this.sectionFormArray.controls.length;
  }

  deleteRowSlabs(i: any): void {
    this.sectionFormArray.removeAt(i);
    this.numberOfCreatedSKUs = i;
  }

  isFormInvalid(): boolean {
    return (
      this.brandForm.invalid ||
      this.sectionFormArray.invalid ||
      this.showCropTypeError ||
      !this.imageName
    );
  }

  getcropType() {
    this.userService.getDropdownCropData().subscribe({
      next: (cropDetails: any) => {
        cropDetails = JSON.parse(this.utility.decrypt(cropDetails));
        this.cropDetails = [];
        cropDetails.forEach((cropInfo: any) => {
          const cropInfoObj = {
            id: cropInfo.id,
            name: this.utility.formatString(cropInfo.name),
            description: cropInfo.description,
            active: cropInfo.active,
          };
          this.cropDetails.push(cropInfoObj);
        });
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
        let error:any =this.utility.decrypt(errorResponse.error)
        error= JSON.parse(error);
        if('Full authentication is required to access this resource'==error.message){
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  getBussinessUnit() {
    this.userService.getBussinessUnit().subscribe({
      next: (BUdetails: any) => {
        BUdetails = JSON.parse(this.utility.decrypt(BUdetails));
        this.bussinessUnitDetails = [];
        BUdetails.forEach((BUinfo: any) => {
          const BUinfoObj = {
            id: BUinfo.id,
            name: this.utility.formatString(BUinfo.name),
            description: BUinfo.description,
            active: BUinfo.active,
            uId: BUinfo.uId,
          };
          this.bussinessUnitDetails.push(BUinfoObj);
        });
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
        let error:any =this.utility.decrypt(errorResponse.error)
        error= JSON.parse(error);
        if('Full authentication is required to access this resource'==error.message){
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }
  

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    if (file) {
      this.showPackshotErrorMessage = false;
      this.imageName = file.name;
      this.formData = new FormData();
      this.formData.append('file', file, file.name);
      this.spinner.show();

      this.userService.uploadPackshot(this.formData).subscribe({
        next: (packshotDetails: any) => {
          this.fileUploaded = true;
          const packshot = JSON.parse(this.utility.decrypt(packshotDetails));
          if (packshot.error) {
            this.toastr.error(packshot.message);
          } else {
            this.packshot = packshot.message;
            this.toastr.success(AppConstant.IMAGE_UPLOAD_MSG);
          }
          this.spinner.hide();
        },
        error: (errorResponse: any) => {
          this.spinner.hide();
          this.imageName = '';
          this.fileUploaded = false;
          let error:any =this.utility.decrypt(errorResponse.error)
          error= JSON.parse(error);
          if('Full authentication is required to access this resource'==error.message){
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message);
          }
        },
      });
    }
  }

  showErrorMessage() {
    this.showPackshotErrorMessage = true;
  }

  backScreen() {
    this.brandForm.reset();
    this.sectionFormArray.reset();
    this.adminService.setEditBrandID(0);
    this.selectedCropType = 0;
    this.cropTab = '';
    this.packshot = '';
    this.numberOfCreatedSKUs = 1;
    this.adminService._closeAddBrandDialog.emit(true);
  }

  /**
   * submission of user form to add or update user.
   * @param user form values
   */
  onSubmit(brandValues: any, skuValues: any): void {
    this.spinner.show();
    if (this.brandId) {
      const brandFormValues = this.brandForm.value;
      const encryptedSkus = this.sectionFormArray.value.map((item: any) => {
        return {
          uId: item.id ? item.id.toString() : '',
          code: item.skuCode ? item.skuCode : '',
          name: item.skuName ? item.skuName : '',
          description: item.description
            ? item.description
            : '',
          active: true,
          uQuantity: item.quantity
            ? item.quantity.toString()
            : '',
          quantity: 0,
        };
      });
      const data = {
        uId: this.brandId ? this.brandId.toString() : '',
        name: brandFormValues.brandName
          ? brandFormValues.brandName
          : '',
        description: brandFormValues.description
          ? brandFormValues.description
          : '',
        uCropId: this.selectedCropType.toString(),
        uScanCategoryId: this.cropTab
          ? this.cropTab.toString()
          : '',
        active: true,
        packshot: this.packshot ? this.packshot : '',
        skus: encryptedSkus,
      };    
      const updateBrand = this.userService.updateBrand(data);
      updateBrand.subscribe({
        next: (res: any) => {
          res = JSON.parse(this.utility.decrypt(res));
          this.toastr.success(res.message);
          this.backScreen();
          this.spinner.hide();
        },
        error: (errMsg: any) => {
          errMsg = JSON.parse(this.utility.decrypt(errMsg.error));
          if (errMsg.error && errMsg.existsSkuCodes) {
            errMsg.existsSkuCodes.forEach((item: any) => {
              this.toastr.error(
                errMsg.existsSkuCodes.join(', ') + ' SKU codes already exists'
              );
            });
          } else if (errMsg.message) {
            this.toastr.error(errMsg.message);
          }
          let errorMsg = errMsg.status;
          if (+errorMsg === 401 || +errorMsg === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          this.spinner.hide();
        },
      });
    } else {
      this.spinner.show();
      const brandFormValues = this.brandForm.value;
      const skuFormValues = this.skuForm.value;

      const encryptedSkus = this.sectionFormArray.value.map((item: any) => {
        return {
          id: 0,
          code: item.skuCode,
          name: item.skuName,
          description: item.description,
          active: true,
          uQuantity: item.quantity,
          quantity: 0,
        };
      });
      const data = {
        name: brandFormValues.brandName.toString(),
        description: brandFormValues.description.toString(),
        uCropId: this.selectedCropType.toString(),
        uScanCategoryId: this.cropTab.toString(),
        active: true,
        packshot: this.packshot.toString(),
        skus: encryptedSkus,
      };

      const addBrand = this.userService.addBrand(data);
      addBrand.subscribe({
        next: (brandDetails: any) => {
          brandDetails = JSON.parse(this.utility.decrypt(brandDetails));
          this.toastr.success(brandDetails.message);
          this.spinner.hide();
          this.backScreen();
        },
        error: (errMsg: any) => {
          errMsg = JSON.parse(this.utility.decrypt(errMsg.error));
          if (errMsg.error && errMsg.existsSkuCodes) {
            errMsg.existsSkuCodes.forEach((item: any) => {
              this.toastr.error(
                errMsg.existsSkuCodes.join(', ') + ' SKU codes already exists'
              );
            });
          } else if (errMsg.message) {
            this.toastr.error(errMsg.message);
          }
          this.spinner.hide();
        },
      });
    }
  }

  sanitizeInput(input: string): string {
    let sanitizedValue = input.replace(/<[^>]*>/g, '');
    sanitizedValue = sanitizedValue.replace(/[&^*#$!@()%]/g, '');
    return sanitizedValue;
  }

  onInputChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const inputValue = inputElement.value;
    const sanitizedValue = this.sanitizeInput(inputValue);
    inputElement.value = sanitizedValue;
  }

  onPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedText = event.clipboardData!.getData('text/plain');
    const sanitizedValue = this.sanitizeInput(pastedText);
    document.execCommand('insertText', false, sanitizedValue);
  }

  funRestSearchPrevent(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
    } else {
      event.preventDefault();
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }

  funRestAllowOnlyNumberPrevent(event: any) {
    event = event || window.event;
    let k;

    if ('key' in event) {
      k = event.key.charCodeAt(0);
    } else {
      k = event.keyCode || event.which;
    }
    if (k >= 48 && k <= 57) {
      return true;
    } else if (k === 32) {
      const input = (event.target as HTMLInputElement).value + ' ';
      const words = input.split(/\s+/);
      const lastWord = words[words.length - 2];
      if (
        lastWord &&
        lastWord.length > 0 &&
        lastWord.match(/\s/g) &&
        lastWord.match(/\s/g)!.length >= 2
      ) {
        event.preventDefault();
        return false;
      } else {
        return true;
      }
    } else {
      event.preventDefault();
      return false;
    }
  }

  descriptionValidation(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }
    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57) ||
      '!@#$%&()-=+:;\'"{}[]<>.,'.indexOf(event.key) !== -1
    ) {
    } else {
      event.preventDefault();
    }

    return true;
  }

  numberValidation(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k = event.charCode || event.keyCode;
    if (k == 8 || k == 32 || k == 46 || (k >= 48 && k <= 57)) {
    } else {
      event.preventDefault();
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 46 ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }

  trimInput(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    const trimmedValue = inputElement.value.trim();
    this.skuForm.get('skuName')?.patchValue(trimmedValue);
  }

  diableSubmitButton(): boolean {
    return (
      !this.brandForm.invalid &&
      this.skuForm.invalid &&
      this.sectionFormArray.invalid &&
      !this.showCropTypeError &&
      !this.fileUploaded &&
      this.imageName !== ''
    );
  }

}
