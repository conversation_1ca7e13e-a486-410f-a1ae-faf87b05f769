@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

$font-size: 13px;

.width-100 {
  width: 100%;
}
.width-60 {
  width: 60%;
}
.width-40 {
  width: 40%;
}
.input-box-sku {
  border-radius: 3px;
}
.error-message {
  .help-block {
    color: red;
  }
}

.sku-input-box {
  padding-top: 10px;
  padding-bottom: 10px;
}

.sku-btn {
  border-radius: 3px;
  height: 35px;
  background-color: grey;
  color: #fff;
  font-weight: bold;
}

.sku-btn-container {
  padding-top: 20px;
}

.submit-color {
  background-color: #ff8033;
}

.brands-container {
  width: 96%;
  margin: 45px 2.5% 15px 2.5%;
  overflow-y: hidden;
  .brands-grid-container {
    width: 99%;
    float: left;
    border-radius: $border-radius;
    margin-top: 20px;
    position: relative;
    .area-filter-container {
      display: flex;
      .left-column {
        width: 60%;
        .main-campaign {
          padding-top: 20px;
          .panel {
            .panel-body {
              width: 100%;
              .wizard {
                min-width: 99%;
                width: 95%;
                float: left;
                background: #fff;
                margin: 0;
                margin-top: -22px;
                font-size: 14px;
                @media screen and (max-width: 500px) {
                  font-size: 8px;
                }
                @media screen and (max-width: 768px) {
                  font-size: 12px;
                }
                .profile-tab {
                  float: left;
                  text-align: center;
                  border-bottom: 3px solid #c6c6c6;
                  height: 50px;
                  line-height: 50px;
                  cursor: pointer;
                  font-size: 14px;
                  color: #c6c6c6;
                  width: 20%;
                  &:last-child {
                    border-right: none;
                  }

                  i {
                    margin-left: 5px;
                  }
                  img {
                    height: 20px;
                    margin-left: 5px;
                  }
                }
                .active {
                  border-bottom: 3px solid $button-color;
                  img {
                    height: 24px;
                    margin-left: 5px;
                  }
                }
              }
              .wizard .active {
                border-bottom: 3px solid #FF8033;
                color: #FF8033; /* Change 'blue' to your preferred color */
              }
              .current-tab {
                font-size: 14px;
                border-bottom: 3px solid $button-color;
                color: #195c94;
                @media screen and (max-width: 500px) {
                  font-size: 8px !important;
                }
                @media screen and (max-width: 768px) {
                  font-size: 12px !important;
                }
              }
            }
          }
        }
      }
      .right-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        width: 100%;
        .search-container {
          display: flex;
          align-items: center;
          width: 100%;
          justify-content: flex-end;

          .export-button button {
            width: 38px;
            height: 37px;
            // background-color: #fff;
            border-radius: 0.25rem;
            border: 1px solid #FF8033;
            background: #FF8033;
            color: #fff;
          }
          .add-button button {
            width: 38px;
            height: 37px;
            background-color: #FF8033;
            border-radius: 0.25rem;
            border: 1px solid #FF8033;
            color: #fff;
          }
          .input-group {
            display: flex;
            justify-content: flex-end;
            .search-input {
              display: flex;
              align-items: center;
              width: 60%;
              margin-top: 8px;

              .input-group-add {
                padding: 0.5rem 0.75rem;
                margin-bottom: 0;
                font-size: 0.9rem;
                font-weight: 400;
                line-height: 1;
                color: #464a4c;
                text-align: center;
                background-color: #fff;
                border: 1px solid rgba(0, 0, 0, 0.15);
                border-radius: 0.25rem;
                width: 85%;
                margin-left: 11%;
                border-color: #FF8033;
                display: flex;
                img {
                  height: 18px;
                  cursor: pointer;
                }
                i {
                  float: left;
                  margin-top: 2px;
                }

                input {
                  border: none;
                  width: 85%;
                  outline: none;
                }
                input:focus {
                  border: none;
                }
              }

              input {
                margin-right: 10px;
                margin-left: 10px;
              }
            }

            .category {
              width: 28%;
              margin-top: 8px;
            }
            .date-picker-container {
              width: 30%;
              height: 52px;
              mat-form-field {
                width: 92%;
                margin: 7px 0px 0px 12px;
              }
              .cancel-button {
                border: 2px solid #FF8033;
              }
              .submit-button {
                color: #fff;
                background-color: #FF8033;
              }
            }

            .export-button,
            .add-button {
              margin-right: 4px;
              margin-top: 7px;
            }
          }
        }

        // Media query for tablets (if needed)
        @media screen and (max-width: 768px) {
          .search-container {
            justify-content: center;
          }
          .cancel-button{
          border: 2px solid #FF8033;
        }
          .submit-button{
          color: #fff;
          background-color: #FF8033;
        }
      }
    }
    .support-table {
      font-size: 15px;
      min-width: 100%;
      overflow-y: hidden;
    }
  }

    .brands-grid-data-parent-container {
      min-height: 485px;
      background: #ffffff;
      overflow-y: auto;
    }
    .brands-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      // padding: 10px 15px 25px;

      .brands-table {
        font-size: 15px;
        min-width: 100%;
        overflow-y: hidden;
        border-radius: $table-border-radius;
      }
    }
  }
}

@media screen and (min-width: 200px) and (max-width: 575px) {
  .brands-container {
    .brands-grid-container {
      .brands-grid-action {
        .brands-grid-search-container {
          width: 100%;
          .brands-grid-search-input {
            width: 100%;
            .input-group-addon {
              padding: 0.2rem 0.5rem;
              margin-bottom: 0;
              font-size: 1rem;
            }
          }
        }
        .brands-grid-action-container {
          width: 100%;
          display: flex;
          flex-direction: column;
          padding: 10px;
          .brands-grid-action-add {
            width: 50%;
            padding: 0;
            margin-top: 5px;
          }
          .brands-grid-action-add {
            width: 60%;
          }
        }
      }
    }
  }
}

@media screen and (min-width: 576px) and (max-width: 767px) {
  .brands-container {
    .brands-grid-container {
      .brands-grid-action {
        .brands-grid-search-container {
          width: 45%;
          .brands-grid-search-input {
            width: 80%;
          }
        }
        .brands-grid-action-container {
          width: 55%;
          .brands-grid-action-add {
            width: 50%;
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 768px) {
  .brands-container {
    .brands-grid-container {
      .brands-grid-data-parent-container {
      }
      .brands-grid-action {
        .brands-grid-action-container {
          width: 100%;
        }
      }
    }
  }
}

.confirmUserActiveContainer {
  @include confirmDialogueActiveInActive();
}

.modal-backdrop.fade {
  opacity: 0.6;
}

.confirmUserActiveContainer .fade {
  opacity: 1 !important;
}

.disable-submit {
  opacity: 0.8;
  cursor: not-allowed;
  &:hover {
    opacity: 0.8;
    cursor: not-allowed;
  }
}
