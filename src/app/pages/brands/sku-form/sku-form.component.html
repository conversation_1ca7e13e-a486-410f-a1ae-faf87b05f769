<div (window:resize)="onWindowResizeSKUs()"></div>
<div class="brands-container">
  <div class="brands-grid-container">
    <div class="brands-grid-action">
      <div
        [ngClass]="{ 'padding-left-0 padding-right-5': ismobileViewSKUBrands }"
        class="brands-grid-action-container"
      >
        <div class="brands-grid-action-export">
          <button class="add" (click)="export('brand')">
            <i class="fa fa-share-square-o export-icon"></i>
          </button>
        </div>
        <div
          *ngIf="isAdmin"
          [ngClass]="{ 'width-100': ismobileViewSKUBrands }"
          class="brands-grid-action-add"
        >
          <button class="add" (click)="addSku(modalTemplate)">
            <i class="fa fa-plus"></i>&nbsp;&nbsp;Add SKU
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="brands-grid-data-parent-container">
    <div id="foo" class="brands-grid-data-container">
      <div class="brands-table">
        <dynamic-table
          [tableHeads]="tableHead"
          [tableData]="skuData"
          [tableConfiguration]="configurationSettings"
          [tableColName]="tableColName"
          [showIndex]="showIndex"
          (onRowEdit)="skuEdit($event)"
        >
        </dynamic-table>
      </div>
    </div>
  </div>
</div>

<div class="confirmUserActiveContainer machine-preview">
  <ng-template #modalTemplate>
    <div class="modal-header">
      <h4 class="modal-title">SKU Details</h4>
      <button
        type="button"
        class="close"
        aria-label="Close"
        (click)="modalRef.hide()"
      >
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <form
        [formGroup]="skuFormModal"
        (ngSubmit)="skuDetailsSubmit(skuFormModal.value)"
      >
        <div class="float-left width-100 sku-input-box">
          <div class="float-left width-40">
            <label class="label-style"
              >SKU Code<i class="required">&nbsp;*</i></label
            >
          </div>
          <div class="float-left width-60">
            <input
              type="text"
              class="form-control input-box-sku"
              formControlName="sku_code"
            />
            <div class="error-message">
              <span
                *ngIf="
                  !skuFormModal.get('sku_code')?.valid &&
                  skuFormModal.get('sku_code')?.dirty
                "
                class="help-block"
                >SKU code is required</span
              >
            </div>
          </div>
        </div>
        <div class="float-left width-100 sku-input-box">
          <div class="float-left width-40">
            <label class="label-style"
              >SKU Name<i class="required">&nbsp;*</i></label
            >
          </div>
          <div class="float-left width-60">
            <input
              type="text"
              class="form-control input-box-sku"
              formControlName="sku_name"
            />
            <div class="error-message">
              <span
                *ngIf="
                  !skuFormModal.get('sku_name')?.valid &&
                  skuFormModal.get('sku_name')?.dirty
                "
                class="help-block"
                >SKU name is required</span
              >
            </div>
          </div>
        </div>
        <div class="float-left width-100 sku-input-box">
          <div class="float-left width-40">
            <label class="label-style"
              >Unit Of Measurement<i class="required">&nbsp;*</i></label
            >
          </div>
          <div class="float-left width-60">
            <select
              class="form-control inbut-box-sku"
              formControlName="uom"
              [(ngModel)]="uom"
            >
              <option class="uom-options" value="">Select UOM</option>
              <option class="uom-options" value="KG">KG</option>
              <option class="uom-options" value="LTR">LTR</option>
              <option class="uom-options" value="ML">ML</option>
            </select>
            <div class="error-message">
              <span
                *ngIf="
                  !skuFormModal.get('uom')?.valid &&
                  skuFormModal.get('uom')?.dirty
                "
                class="help-block"
                >Unit of measurement is required</span
              >
            </div>
          </div>
        </div>
        <div class="float-left width-100 sku-input-box">
          <div class="float-left width-40">
            <label class="label-style">SKU Description</label>
          </div>
          <div class="float-left width-60">
            <textarea
              class="form-control input-box-sku"
              formControlName="sku_desc"
            ></textarea>
            <div class="error-message">
              <span
                *ngIf="
                  !skuFormModal.get('sku_desc')?.valid &&
                  skuFormModal.get('sku_desc')?.dirty
                "
                class="help-block"
                >SKU description is required</span
              >
            </div>
          </div>
        </div>
        <div class="float-left width-100 sku-btn-container">
          <div class="float-left width-20">
            <input type="button" class="btn sku-btn" value="CANCEL" />
          </div>
          <div class="float-right width-20">
            <input
              type="submit"
              class="btn sku-btn submit-color"
              [disabled]="!skuFormModal.valid"
              value="SUBMIT"
            />
          </div>
        </div>
      </form>
    </div>
  </ng-template>
</div>
