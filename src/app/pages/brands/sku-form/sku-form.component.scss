@import "../../../theme/sass/_auth";
@import "../../../../styles";
@import "../../../theme/sass/mixins";

$font-size: 13px;

.width-100 {
  width: 100%;
}
.width-60 {
  width: 60%;
}
.width-40 {
  width: 40%;
}
.input-box-sku {
  height: 36px;
  border-radius: 3px;
}
.error-message {
  .help-block {
    color: red;
  }
}

select.form-control {
  cursor: pointer;
}

.margin-bottom-0 {
  margin-bottom: 0;
}

.width-50 {
  width: 50%;
}

.margin-top-0 {
  margin-top: 0 !important;
}

.sku-input-box {
  padding-top: 10px;
  padding-bottom: 10px;
}

.sku-btn {
  border-radius: 3px;
  height: 35px;
  background-color: grey;
  color: #fff;
  font-weight: bold;
}

.sku-btn-container {
  padding-top: 20px;
}

.submit-color {
  background-color: #FF8033;
}

.brands-container {
  width: 96%;
  margin: 70px 2.5% 15px 2.5%;
  overflow-y: hidden;
  .padding-left-0 {
    padding-left: 0 !important;
  }
  .padding-right-5 {
    padding-right: 5px !important;
  }
  .padding-10-15 {
    padding: 10px 15px !important;
  }
  .width-100 {
    width: 100% !important;
  }
  .brands-grid-container {
    width: 99.7%;
    float: left;
    border-radius: $border-radius;
    margin-top: 30px;
    position: relative;
    .brands-grid-action {
      padding: 0 15px;
      width: 100%;
      position: relative;
      margin-bottom: 10px;
      overflow-y: auto;
      background: #fff;
      border-radius: $border-radius;
      box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.1);
      .brands-grid-search-container {
        width: 40%;
        float: left;
        padding: 1% 0;
        .brands-grid-search-input {
          width: 100%;
          @include gridSearchInput();
          .input-fields-brands {
            padding: 0.5rem 0.75rem;
            font-size: 14px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            line-height: 1.25;
          }
        }
        @include inputRightBorder($border-radius);
        .input-group {
          margin-bottom: 0;
          @include inputAddOnBorder($border-radius);
        }
        @include placeholderColor(#666, 0.7);
      }
      .brands-grid-action-container {
        width: 50%;
        float: right;
        padding: 1% 0;
        display: block;
        position: relative;
        .brands-grid-action-add {
          width: 30% !important;
          margin-right: 6px;
          @include addButtonContainer();
          .add {
            width: 100% !important;
          }
          .add-btn-width {
            width: 100%;
          }
        }
        .brands-grid-action-export {
          width: 8% !important;
          float: right;
          @include addButtonContainer();
          .add {
            width: 100%;
          }
          .add-btn-width {
            width: 100%;
          }
        }
      }
    }
    .brands-grid-data-parent-container {
      min-height: 485px;
      background: #ffffff;
      overflow-y: auto;
    }
    .brands-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;

      .brands-table {
        font-size: 15px;
        min-width: 100%;
        overflow-y: hidden;
      }
    }
  }
}

@media only screen and (max-width: 900px) {
  .brands-container {
    .brands-grid-container {
      .brands-grid-action {
        .brands-grid-action-container {
          width: 100%;
          .brands-grid-action-export {
            width: 100%;
          }
        }
      }
    }
  }
}
@media only screen and (max-width: 500px) {
  .brands-container {
    .brands-grid-container {
      .brands-grid-action {
        .brands-grid-action-container {
          .brands-grid-action-export {
            width: 100% !important;
            padding: 0 0 0 0;
          }
          .brands-grid-action-add {
            width: 100% !important;
          }
        }
      }
    }
  }
}
