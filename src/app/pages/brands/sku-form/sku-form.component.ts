import {
  Component,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
} from "@angular/core";
import { Location, NgClass, NgIf } from "@angular/common";
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { Router, ActivatedRoute } from "@angular/router";
import { GlobalEvents } from "../../../helpers/global.events";
import { UserService } from "../../../app-services/user-service";
import { AppConstant } from "../../../constants/app.constant";
import { EditSKUData } from "../../../app-services/edit-sku-data-service";
import * as _ from "lodash";
import { AuthenticationHelper } from "../../../helpers/authentication";
import { ngxCsv } from "ngx-csv";
import { BsModalService, BsModalRef } from "ngx-bootstrap/modal";
import { DynamicTableComponent } from "../../../shared/data-table/data-table.component";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";

interface SkuDetails {
  id: string;
  code: string;
  name: string;
  description: string;
  uom: string;
}
interface BrandSkuApiResponse {
  content: SkuDetails[];
}
interface SKUData {
  id: any;
  sku: string;
  name: string;
  description: string;
  uom: string;
}
interface SkuFormValues {
  uom: any;
  code: string;
  name: string;
  description: string;
}
interface BrandDetails {
  id: any;
  code: string;
  name: string;
  desc: string;
  pc: string;
}
@Component({
    selector: "nga-add-sku",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["sku-form.component.scss"],
    templateUrl: "sku-form.component.html",
    imports: [
        NgClass,
        NgIf,
        DynamicTableComponent,
        FormsModule,
        ReactiveFormsModule,
    ],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class SKUFormComponent {
  skuForm!: FormGroup;
  skuFormModal!: FormGroup;
  skuData: SKUData[] = [];
  tableHead: string[] = [];
  tableColName: string[] = [];
  isSKUDataEditable: boolean[] = [];
  brandDetailsObj: BrandDetails = {} as BrandDetails;
  oldSKUValues: any = {};
  updateSKUDetails = {};
  subsObj: any = {};
  brandId: number;
  ID: any = "";
  uom: string = "";
  ismobileViewSKUBrands: boolean = false;
  isAdmin: boolean = false;
  showIndex: any = { index: null };
  configurationSettings: any = {
    showPagination: true,
    perPage: 10,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    editBox: true,
    actionsColumnName: "Actions",
    noDataMessage: "No data found",
    showEdit: true,
  };
  exportData: any = [];
  @ViewChild("modalTemplate")
  modalTemplate!: TemplateRef<any>;
  modalRef!: BsModalRef;

  constructor(
    private modalService: BsModalService,
    private fb: FormBuilder,
    public location: Location,
    private router: Router,
    public events: GlobalEvents,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private activeRoute: ActivatedRoute,
    private userService: UserService,
    private editSKUDetailsService: EditSKUData
  ) {
    this.ismobileViewSKUBrands = window.innerWidth <= 1023 ? true : false;
    this.brandId = this.activeRoute.snapshot.queryParams["id"];
    this.isAdmin = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;

    if (this.isAdmin) {
      this.configurationSettings.showActionsColumn = true;
    }
  }
  ngOnInit() {
    this.setUserForm();
    this.setTableHeader();
    if (this.brandId) {
      this.brandDetails();
    }
    this.SKUDetailsByBrandId();
    window.scrollTo(0, 0);
    this.subsObj = this.editSKUDetailsService.rowData.subscribe((val: any) => {
      if (Object.keys(this.oldSKUValues).length !== 0) {
        this.updateSKUDetails = val;
        _.extend(this.oldSKUValues, this.updateSKUDetails);
        this.updateSKUData();
      }
    });
    this.events.showBack.emit(true);
  }
  ngOnDestroy() {
    this.events.showBack.emit(false);
  }
  setTableHeader(): void {
    this.tableHead = ["SKU Code", "SKU Name", "SKU Description", "UOM"];
    this.tableColName = ["sku", "name", "description", "uom"];
  }

  SKUDetailsByBrandId(): void {
    this.spinner.show();
    this.userService.skuDetails(this.brandId).subscribe({
      next: (res: Object) => {
        const brandSkuResponse: BrandSkuApiResponse =
          res as BrandSkuApiResponse;
        this.skuData = [];
        if (
          brandSkuResponse &&
          brandSkuResponse.content &&
          brandSkuResponse.content.length
        ) {
          brandSkuResponse.content.forEach((skuDetails: SkuDetails) => {
            let skuDetailsObj = {
              id: skuDetails.id ? skuDetails.id : "",
              sku: skuDetails.code ? skuDetails.code : "",
              name: skuDetails.name ? skuDetails.name : "",
              description: skuDetails.description ? skuDetails.description : "",
              uom: skuDetails.uom ? skuDetails.uom : "",
            };
            this.skuData.push(skuDetailsObj);
            this.isSKUDataEditable.push(false);
          });
        }
        this.spinner.hide();
      },
      error: (_errResponse: string) => {
        this.toastr.error(AppConstant.BRAND_SKU_ERROR);
        this.router.navigate(["brands"]);
        this.spinner.hide();
      },
    });
  }

  addSku(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

  /**
   *  To set user form values
   *  @param values
   */
  setUserForm(values?: SkuFormValues) {
    this.skuForm = this.fb.group({
      code: [
        values ? values.code : "",
        Validators.compose([Validators.required]),
      ],
      name: [
        values ? values.name : "",
        Validators.compose([Validators.required]),
      ],
      description: [values ? values.description : ""],
    });
    this.skuFormModal = this.fb.group({
      sku_code: [
        values ? values.code : "",
        Validators.compose([Validators.required]),
      ],
      sku_name: [
        values ? values.name : "",
        Validators.compose([Validators.required]),
      ],
      sku_desc: [values ? values.description : ""],
      uom: [
        values ? values.uom : "",
        Validators.compose([Validators.required]),
      ],
    });
  }

  updateSKUData(): void {
    if (
      this.oldSKUValues.code === "" ||
      this.oldSKUValues.name === "" ||
      this.oldSKUValues.uom === ""
    ) {
      this.toastr.error("Please fill all the fields");
      return;
    }

    const skuData: any = {
      id: this.oldSKUValues.id,
      skuName: this.oldSKUValues.name.trim(),
      skuDescription: this.oldSKUValues.description.trim(),
      skuUoM: this.oldSKUValues.uom.trim(),
    };

    this.userService.updateSKUDetails(skuData).subscribe(
      (_res: any) => {
        this.oldSKUValues = {};
        this.toastr.success(AppConstant.SKU_UPDATED_SUCCESS);
        this.SKUDetailsByBrandId();
        this.showIndex.index = null;
        this.spinner.hide();
      },
      (errRes: any) => {
        this.spinner.hide();
        if (errRes && errRes.error && errRes.error.message) {
          this.toastr.error(errRes.error.message);
        } else {
          this.toastr.error(AppConstant.SKU_UPDATE_ERROR);
        }
      }
    );
  }

  /**
   *  To submit the user form value for adding SKU
   *  @param user form values
   */

  skuDetailsSubmit(values: any): void {
    this.spinner.show();
    const skuDetails: any = {
      skuCode: values.sku_code.trim(),
      skuName: values.sku_name.trim(),
      skuDescription: values.sku_desc.trim(),
      skuUoM: values.uom.trim(),
      brandId: this.brandId,
    };

    this.userService.addSKUDetails(skuDetails).subscribe({
      next: (_res: any) => {
        this.skuFormModal.reset();
        this.toastr.success(AppConstant.SKU_ADD_SUCCESS);
        this.SKUDetailsByBrandId();
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        if (
          errorResponse &&
          errorResponse.error &&
          errorResponse.error.message
        ) {
          this.toastr.error(errorResponse.error.message);
        } else {
          this.toastr.error(AppConstant.SKU_ADD_ERROR);
        }
        this.spinner.hide();
      },
    });
  }

  skuEdit(event: any): void {
    this.oldSKUValues = {};
    this.oldSKUValues = event;
    this.configurationSettings.editBox = true;
  }

  onWindowResizeSKUs(): void {
    this.ismobileViewSKUBrands = window.innerWidth <= 1023 ? true : false;
  }
  brandDetails(): void {
    this.spinner.show();
    let data = {
      id: this.brandId,
    };
    this.userService.getBrandDetailsByID(data).subscribe({
      next: (brandInfo: any) => {
        if (brandInfo) {
          this.brandDetailsObj = {
            id: brandInfo.id ? brandInfo.id : "",
            code: brandInfo.brandCode ? brandInfo.brandCode : "",
            name: brandInfo.brandName ? brandInfo.brandName : "",
            desc: brandInfo.brandDescription ? brandInfo.brandDescription : "",
            pc: brandInfo.profitCenterName
              ? brandInfo.profitCenterName == "AF"
                ? "DO1301"
                : "DO1401"
              : "",
          };
          this.events.setChangedContentTopText(
            brandInfo.brandName ? brandInfo.brandName : ""
          );
        }
        this.spinner.hide();
      },
      error: (_errInfo: any) => {
        this.toastr.error(AppConstant.BRAND_FETCHING_ERROR);
        this.router.navigate(["brands"]);
        this.spinner.hide();
      },
    });
  }

  /**
   *  Method for exporting the data in CSV format
   *  @param data
   */
  export(data: any): void {
    if (data == "brand") {
      this.ID = this.brandId;
      this.getSKUExportData();
    } else {
      this.ID = "";
      this.getSKUExportData();
    }
  }

  getSKUExportData(): void {
    window.scrollTo(0, 0);
    this.spinner.show();
    this.userService.getSKUsExport(this.ID).subscribe({
      next: (exportsData: any) => {
        this.exportData = [];
        if (exportsData && exportsData.length) {
          exportsData.forEach((exportInfo: any) => {
            let exportObj = {
              id: exportInfo.id ? exportInfo.id : "",
              code: exportInfo.code ? exportInfo.code : "",
              name: exportInfo.name ? exportInfo.name : "",
              description: exportInfo.description ? exportInfo.description : "",
              uom: exportInfo.uom ? exportInfo.uom : "",
              brand_code: exportInfo.brand
                ? exportInfo.brand.code
                  ? exportInfo.brand.code
                  : ""
                : "",
              brand_name: exportInfo.brand
                ? exportInfo.brand.name
                  ? exportInfo.brand.name
                  : ""
                : "",
              brand_id: exportInfo.brand
                ? exportInfo.brand.id
                  ? exportInfo.brand.id
                  : ""
                : "",
              brand_desc: exportInfo.brand
                ? exportInfo.brand.description
                  ? exportInfo.brand.description
                  : ""
                : "",
            };
            this.exportData.push(exportObj);
          });
          let options = {
            fieldSeparator: ",",
            quoteStrings: '"',
            decimalseparator: ".",
            showLabels: true,
            headers: [
              "SKU Id",
              "SKU Code",
              "SKU Name",
              "SKU Description",
              "Unit Of Measurement",
              "Brand Code",
              "Brand Name",
              "Brand Id",
              "Brand Description",
            ],
          };
          new ngxCsv(this.exportData, "skus", options);
        }
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.EXPORT_ERROR);
        this.spinner.hide();
      },
    });
  }
}
