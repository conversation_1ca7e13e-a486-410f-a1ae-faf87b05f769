<!-- <div class="calibration-container">
    <div class="calibration-grid-container">
      <div class="calibration-grid-data-parent-container">
        <div id="foo" class="calibration-grid-data-container">
          <div class="calibration-filter-container">
            <div class="left-column">
              <div class="main-campaign">
                <div class="panel">
                  <div class="panel-body">
                    <div class="tab-button">
                      <a
                        class="profile-tab calibration-tab-width"
             
                        (click)="tabChanged('FIELD_CROPS')"
                      >
                        <span>Field Crop</span>
                      </a>
                      <a
                        class="profile-tab calibration-tab-width"
                
                        (click)="tabChanged('FORAGES')"
                      >
                        <span>Forages</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="right-column">
              <div class="search-container">
                <div class="input-group">
                  <div class="add-button">
                    <button
                      class="add"
                      title="Add"
                      (click)="openUploadCalibration()"
                    >
                      <i class="fa fa-upload" aria-hidden="true"></i>
                      {{ "Upload Calibration" }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="calibration-table">
            <mat-accordion *ngIf="true">
              <mat-expansion-panel *ngFor="let season of calibrationDataList">
                <mat-expansion-panel-header
                  class="mat-expansion-panel-header-container"
                >
                  <mat-panel-title class="mat-title-container">
                    {{ toPascalCase(season.season) }} {{ season.year }}
                  </mat-panel-title>
                  <mat-panel-description
                    class="mat-description-scheme-button-container"
                  >
                  </mat-panel-description>
                </mat-expansion-panel-header>
                <mat-accordion>
                  <mat-expansion-panel
                    *ngFor="let regionDetails of season.calibrations"
                  >
                    <mat-expansion-panel-header
                      class="mat-expansion-panel-header-container"
                    >
                      <mat-panel-title class="mat-title-container">
                        <div class="expansion-data">
                          <span class="region-container"
                            >{{ "Region :" }}
                            <span>
                              {{ regionDetails.region.name }}
                            </span>
                          </span>
  
                          <span class="status-container">{{ "Status :" }}</span>
                          <span class="calibration-status">{{
                            season.isRunning ? "Running" : "Done"
                          }}</span>
                        </div>
                      </mat-panel-title>
                      <mat-panel-description
                        class="mat-description-scheme-button-container"
                      >
                      </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="expansion-data-container">
                      <div class="region-table-data">
                        <table class="custom-table">
                          <thead>
                            <tr>
                              <th>ID</th>
                              <th>File Name</th>
                              <th>Region</th>
                              <th>Season</th>
                              <th>Uploaded Date</th>
                              <th>Uploaded By</th>
                              <th>Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>{{ regionDetails.id }}</td>
                              <td>{{ season.fileName }}</td>
                              <td>{{ regionDetails.region.name }}</td>
                              <td>
                                {{ regionDetails.scanningCategory.season.name }}
                              </td>
                              <td>{{ regionDetails.uploadedDate }}</td>
                              <td>{{ regionDetails.uploadedBy }}</td>
                              <td>
                                <i
                                  class="fa fa-download"
                                  aria-hidden="true"
                                  (click)="downloadCalibrationFile(regionDetails)"
                                ></i>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </mat-expansion-panel>
                </mat-accordion>
              </mat-expansion-panel>
            </mat-accordion>
            <mat-accordion *ngIf="true">
              <mat-expansion-panel *ngFor="let season of calibrationDataList">
                <mat-expansion-panel-header
                  class="mat-expansion-panel-header-container"
                >
                  <mat-panel-title class="mat-title-container">
                    {{ season.year }}
                  </mat-panel-title>
                  <mat-panel-description
                    class="mat-description-scheme-button-container"
                  >
                  </mat-panel-description>
                </mat-expansion-panel-header>
                <mat-accordion>
                  <mat-expansion-panel
                    *ngFor="let regionDetails of season.calibrations"
                  >
                    <mat-expansion-panel-header
                      class="mat-expansion-panel-header-container"
                    >
                      <mat-panel-title class="mat-title-container">
                        <div class="expansion-data">
                          <div>
                            <span class="region-container">{{ "Region :" }}</span
                            >{{ regionDetails.region.name }}
                            <span class="status-container">{{ "Status" }}</span>
                            <span class="calibration-status">{{
                              season.isRunning ? "Running" : "Done"
                            }}</span>
                          </div>
                        </div>
                      </mat-panel-title>
                      <mat-panel-description
                        class="mat-description-scheme-button-container"
                      >
                      </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="expansion-data-container">
                      <div class="region-table-data">
                        <table class="custom-table">
                          <thead>
                            <tr>
                              <th>ID</th>
                              <th>File Name</th>
                              <th>Region</th>
                              <th>Uploaded Date</th>
                              <th>Uploaded By</th>
                              <th>Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>{{ regionDetails.id }}</td>
                              <td>{{ regionDetails.fileName.split("-")[2] }}</td>
                              <td>{{ regionDetails.region.name }}</td>
                              <td>{{ regionDetails.uploadedDate }}</td>
                              <td>{{ regionDetails.uploadedBy }}</td>
                              <td>
                                <i
                                  class="fa fa-download"
                                  aria-hidden="true"
                                  (click)="downloadCalibrationFile(regionDetails)"
                                ></i>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </mat-expansion-panel>
                </mat-accordion>
              </mat-expansion-panel>
            </mat-accordion>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <ng-template #uploadCalibration>
    <div class="change-status-container">
      <div class="main-calibration-container">
        <div class="main-calibration-top-container">
          <div class="eyebrow-heading-container">
            <div class="top-container">
              <div (click)="downloadTemplate()" class="cursor">
                <i class="fa fa-download"></i>
                <p>{{ "Filetemplate.xlsx" }}</p>
              </div>
            </div>
          </div>
          <div class="calibration-top-container">
            <div class="heading-container">
              <h3>{{ "Upload Calibration" }}</h3>
            </div>
          </div>
           <div class="form-input-calibration-container">
                  <div class="row"> 
                    <div class="col-md-4" *ngIf="true">
                      <span>
                        <label *ngIf="true" for="firstName" class="label">Season-Year<i class="required">&nbsp;*</i></label> 
                        <angular2-multiselect
                        [data]="seasonDialogDataList"
                        [(ngModel)]="seasonDialogData"
                        [settings]="seasonYearDropdownSettings"
                        (onSelect)="onSeasonSelect($event)"
                        (onDeSelect)="onSeasonDeselect($event)" 
                        (onDeSelectAll)="onDeselectAllSeason($event)"
                        [ngModelOptions]="{standalone: true}"
                      >
                      </angular2-multiselect>
                      </span>
                    </div> 
                    <div class="col-md-4" *ngIf="true">
                      <span>
                        <label for="firstName" class="label">Year<i class="required">&nbsp;*</i></label> 
                        <angular2-multiselect
                        [data]="seasonDialogDataList"
                        [(ngModel)]="seasonDialogData"
                        [settings]="yearDropdownSettings"
                        (onSelect)="onSeasonSelect($event)"
                        (onDeSelect)="onSeasonDeselect($event)" 
                        (onDeSelectAll)="onDeselectAllSeason($event)"
                        [ngModelOptions]="{standalone: true}"
                      >
                      </angular2-multiselect>
                      </span>
                    </div> 
                    <div class="col-md-4">
                      <span>
                        <label for="firstName" class="label">Region<i class="required">&nbsp;*</i></label>
                        <angular2-multiselect
                        [data]="regionDataList"
                        [(ngModel)]="regions"
                        [settings]="regionDropdownSettings"
                        (onSelect)="onRegionSelect($event)"
                        (onDeSelect)="onRegionDeselect($event)" 
                        (onDeSelectAll)="onDeselectAllRegion($event)" >
                      </angular2-multiselect>
                      </span>
                    </div>
                    <div class="col-md-4 calibration-input1-container"> 
                      <span>
                        <label for="excelFile" class="label">Upload Excel File<i class="required">&nbsp;*</i></label> 
                        <span class="add input-upload-file" title="Upload File"> 
                          <i class="fa fa-upload upload-icon" aria-hidden="true" (click)="fileInput.click()"></i> 
                            <input type="text" class="upload-input-field" (click)="fileInput.click()" readonly [value]="fileName || 'No file selected'">
                            <input
                            readonly
                              hidden
                              type="file" 
                              #fileInput 
                              id="importFile"
                              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel.sheet.macroEnabled.12"
                              style="width: 300px" (change)="uploadedFiles($event)"
                            /> 
                        </span>
                      </span>
                      <div id="error-message" style="display: none; color: red;">Please upload an Excel file (.xlsx, .xls).</div>
                    </div> 
                </div>
              </div> 
                <div class="button-container">
                  <button type="button" class="btn-cancel" (click)="closeCalibrationDialog()">{{'Cancel'}}</button> 
                  <button  type="button" class="btn-submit" (click)="submitCalibrationDetails()">  
                      <div class="upload-button" > 
                        {{'Submit'}}
                      </div>
                    </button>
                </div>
            </div>
        </div>
      </div>
  </ng-template>
   -->

<app-coming-soon [pageName]="'Calibration'" />
