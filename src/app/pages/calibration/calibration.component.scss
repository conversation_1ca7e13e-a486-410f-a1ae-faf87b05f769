@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

.table-striped > tbody > tr:nth-of-type(even) {
  background-color: #f8f8f8;
}
.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #fff;
}
.calibration-container {
  width: 100%;
  margin-left: 14px;
  margin-right: 14px;
  overflow-y: hidden;
  .calibration-grid-container {
    width: 98%;
    float: left;
    border-radius: $border-radius;
    margin-top: 60px;
    position: relative;
    .calibration-grid-data-parent-container {
      background: #ffffff;
      overflow-y: auto;
    }
    .calibration-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;

      .calibration-table {
        font-size: 15px;
        min-width: 100%;
        overflow-y: hidden;
        .mat-title-container {
          color: #1a3661;
          font-weight: 600;
        }

        mat-expansion-panel-header {
          background-color: #ebf3ff;

          .mat-title-container {
            .expansion-data {
              .region-container {
                font-weight: 600;
                margin-right: 10px;
                span {
                  font-weight: 400;
                }
              }
              .status-container {
                margin-left: 10px;
                font-weight: 600;
                margin-right: 10px;
              }
              .calibration-status {
                color: #04b200;
                font-weight: 400;
              }
            }
          }
        }
        .mat-description-scheme-button-container {
          display: flex;
          justify-content: end;

          button {
            margin: 1px;
          }
        }
        .expansion-data-container {
          width: 102%;
          margin: 0px 20px 0px -8px;
          padding: 5px 20px 5px 20px;
          .expansion-data {
            display: flex;
            justify-content: space-between;
            color: #1a3661;
            padding: 5px 0 5px 0;

            border: 1px solid;

            .toggle-container {
              display: flex;
              gap: 8px;
              justify-content: flex-end;
              .update-icon {
                color: #00cc3a;
                font-size: 20px;
                cursor: pointer;
              }
              .close-icon {
                color: #ff0000;
                transform: rotate(180deg);
                font-size: 20px;
                cursor: pointer;
              }
            }
          }
          .region-table-data {
            box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
              0px 4px 5px 0px rgba(0, 0, 0, 0.14),
              0px 1px 10px 0px rgba(0, 0, 0, 0.12);
            table {
              width: 100%;
              border-color: gray;
              thead {
                color: #1a3661;
                font-weight: bold;
                height: 40px;
                text-align: center;
                background-color: #ebf3ff;
                tr {
                  th {
                    border: 1px solid #ddd;
                    text-align: center;
                    font-weight: bold;
                    line-height: 18px;
                  }
                }
              }
              tbody {
                height: 40px;
                tr {
                  td {
                    border: 1px solid #ddd;
                    text-align: center;
                    color: #1a3661;
                    i {
                      cursor: pointer;
                    }
                  }
                }
              }
            }
            .custom-table {
              .border-row {
                border: 1px solid #ddd !important;
                text-align: center !important;
                padding: 10px !important;
              }
            }
          }
        }

        .no-data-expantion {
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 10px;
          color: #666;
        }
      }

      .calibration-filter-container {
        display: flex;
        .left-column {
          width: 30%;
          .main-campaign {
            padding-top: 20px;
            .panel {
              .panel-body {
                .tab-button {
                  min-width: 65%;
                  height: 40px;
                  float: left;
                  background: #fff;
                  margin: 0;
                  margin-top: -15px;
                  font-size: 14px;
                  border-radius: 6px;
                  border: 1px solid rgba(0, 0, 0, 0.15);
                  @media screen and (max-width: 500px) {
                    font-size: 8px;
                  }
                  @media screen and (max-width: 768px) {
                    font-size: 12px;
                  }
                  .profile-tab {
                    float: left;
                    text-align: center;
                    height: 38px;
                    line-height: 50px;
                    cursor: pointer;
                    font-size: 14px;
                    color: #c6c6c6;
                    width: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    i {
                      margin-left: 5px;
                    }
                  }
                  .active {
                    border-bottom: 3px solid $button-color;
                  }
                }
                .tab-button .active {
                  border: 1px solid #1a3661;
                  color: #1a3661;
                  background-color: #e5efff;
                  font-weight: 500;
                  border-radius: 5px 0px 0px 5px;
                }
                .current-tab {
                  font-size: 14px;
                  border-bottom: 3px solid $button-color;
                  color: #195c94;
                  @media screen and (max-width: 500px) {
                    font-size: 8px !important;
                  }
                  @media screen and (max-width: 768px) {
                    font-size: 12px !important;
                  }
                }

                .search-input {
                  display: flex;
                  align-items: center;
                  width: 50%;

                  .input-group-add {
                    padding: 0.5rem 0.75rem;
                    margin-bottom: 0;
                    font-size: 1rem;
                    font-weight: 400;
                    line-height: 1;
                    color: #464a4c;
                    text-align: center;
                    background-color: #fff;
                    border: 1px solid rgba(0, 0, 0, 0.15);
                    border-radius: 0.25rem;
                    width: 85%;
                    margin-left: 11%;

                    i {
                      float: left;
                    }

                    input {
                      border: none;
                      width: 85%;
                      outline: none;
                    }
                    input:focus {
                      border: none;
                    }
                  }

                  input {
                    margin-right: 10px;
                  }
                }
              }
            }
          }
        }
        .right-column {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;

          .search-container {
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: flex-end;

            .export-button button {
              width: 38px;
              height: 37px;
              background-color: #fff;
              color: #1a3661;
              border-radius: 0.25rem;
              border: 1px solid #1a3661;
            }
            .add-button {
              margin: 6px 0 0 0;
              button {
                height: 37px;
                background-color: #1a3661;
                border-radius: 0.25rem;
                border: 1px solid #1a3661;
                color: #fff;
                padding: 0px 14px 0 14px;
              }
            }
            .input-group {
              display: flex;
              justify-content: flex-end;

              .reward-points-filter {
                .radio-container {
                  margin: 15px 0px 0px 0px;

                  span.radio-margin {
                    padding: 5px;
                  }

                  label.radio-label {
                    margin: 0px 0px 0px 5px;
                  }
                }
              }

              .search-input {
                display: flex;
                align-items: center;
                width: 30%;

                .input-group-add {
                  padding: 0.5rem 0.75rem;
                  margin-bottom: 0;
                  font-size: 1rem;
                  font-weight: 400;
                  line-height: 1;
                  color: #464a4c;
                  text-align: center;
                  background-color: #fff;
                  border: 1px solid rgba(0, 0, 0, 0.15);
                  border-radius: 0.25rem;
                  width: 100%;
                  margin-left: 5%;

                  i {
                    float: left;
                  }

                  input {
                    border: none;
                    width: 85%;
                    outline: none;
                    font-size: 14px;
                  }
                  input:focus {
                    border: none;
                  }
                }

                input {
                  margin-right: 10px;
                }

                .input-group
                  > :not(:first-child):not(.dropdown-menu):not(
                    .valid-tooltip
                  ):not(.valid-feedback):not(.invalid-tooltip):not(
                    .invalid-feedback
                  ) {
                  margin-left: calc(var(--bs-border-width) * -10);
                  border-top-left-radius: 0;
                  border-bottom-left-radius: 0;
                }
              }

              .date-picker-container {
                width: 32%;
                height: 52px;
                mat-form-field {
                  width: 92%;
                  margin: 8px 0px 0px 12px;
                }
                .cancel-button {
                  border: 2px solid #FF8033;
                }
                .submit-button {
                  color: #fff;
                  background-color: #FF8033;
                }
              }

              .export-button {
                margin: 6px;
              }
            }
          }

          // Media query for tablets (if needed)
          @media screen and (max-width: 768px) {
            .search-container {
              justify-content: center;
            }
          }
        }
      }
    }
  }
}

.main-calibration-container {
  width: 100%;
  .main-calibration-top-container {
    .calibration-top-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .eyebrow-heading-container {
      display: flex;
      justify-content: flex-end;
      height: 22px;
      .top-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .cursor {
          cursor: pointer;
          display: flex;
          justify-content: flex-end;
          height: 20px;
        }
        .cursor i {
          margin: 3px;
          color: #0070ff;
        }
        .cursor p {
          display: inline;
          color: #0070ff;
        }
      }
    }
    .container-fluid {
      height: 300px;
      overflow-y: scroll;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;

      .upload-container {
        width: 50%;
        height: 250px;
        display: grid;
        justify-content: center;
        text-align: center;
        background-color: #efefef;
        cursor: pointer;

        .upload-icon {
          display: grid;
          align-items: end;
        }
      }

      .container-preview {
        .upload-button {
          button {
            width: 38px;
            height: 37px;
            background-color: #fff;
            border-radius: 0.25rem;
            border: 1px solid #1a3661;
            margin: 0px 0px 10px 560px;
          }
        }
      }
    }

    .preview-fluid {
      height: 300px;
      overflow-y: scroll;
      display: flex;
      justify-content: center;
      align-items: start;
      text-align: center;

      .upload-container {
        width: 50%;
        height: 250px;
        display: grid;
        justify-content: center;
        text-align: center;
        background-color: #efefef;
        cursor: pointer;

        .upload-icon {
          display: grid;
          align-items: end;
        }
      }

      .container-preview {
        .upload-button {
          button {
            width: 38px;
            height: 37px;
            background-color: #fff;
            border-radius: 0.25rem;
            border: 1px solid #1a3661;
            margin: 0px 0px 10px 560px;
          }
        }
      }
    }

    .custom-menu {
      width: 25% !important;
      height: 300px !important;
    }

    .body-container {
      margin-top: -30px;
      h4 {
        display: flex;
        justify-content: center;
        color: #1a3661;
        font-family: $sans-font-family;
        font-weight: 600;
      }
    }

    .msg-container {
      p {
        display: flex;
        justify-content: center;
        color: #000000;
        font-family: $sans-font-family;
      }
    }

    .heading-container {
      color: #1a3661;
      font-family: $sans-font-family;
      h3 {
        font-weight: 800;
      }
    }
    .calibration-input1-container {
      .span-field {
        width: 48%;
      }
      span {
        .input-field {
          font-size: 1rem;
          font-weight: 400;
          line-height: 1;
          color: #464a4c;
          padding: 0px 24px 0px 10px;
          background-color: #fff;
          border: none;
          outline: none;
          border-radius: 0.25rem;
          height: 38px;
          width: 100%;
        }

        i.fa.fa-upload.upload-icon {
          position: absolute;
          margin: 10px -16px 0px 0px;
        }

        .upload-input-field {
          font-size: 14px;
          font-weight: 400;
          line-height: 1;
          color: #464a4c;
          padding-left: 8px;
          background-color: #fff;
          border: none;
          outline: none;
          border-radius: 0.25rem;
          height: 30px;
          width: 100%;
          cursor: pointer;
        }
      }

      .input-upload-file {
        display: flex;
        justify-content: flex-end;
        border: 1px solid #1a3661;
        border-radius: 0.25rem;
        height: 36px;
        width: 100%;
        font-size: 14px;
        font-weight: 400;
        line-height: 1;
        color: #464a4c;
        padding-left: 8px;
        padding-right: 28px;
      }
    }
  }
  .button-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 5%;
    button {
      height: 40px;
      width: 30%;
      border: none;
      border-radius: 5px;
    }
    .btn-cancel {
      background-color: #fff;
      color: #1a3661;
      font-family: sans-serif;
      font-weight: 600;
      border: 1px solid #1a3661;
    }
    .btn-submit {
      background-color: #1a3661;
      color: #fff;
      font-family: $sans-font-family;
      font-weight: 600;
      cursor: pointer;
    }
    .disable-submit {
      opacity: 0.8;
      cursor: not-allowed;
      &:hover {
        opacity: 0.8;
        cursor: not-allowed;
      }
    }
  }
}

:host ::ng-deep .mat-expansion-panel-header {
  margin-bottom: 10px;
}

:host ::ng-deep .brands-multi-select .cuppa-dropdown .selected-list .c-btn {
  border-radius: 5px;
  padding: 8px;
  width: 100%;
  border-color: #1a3661;
}

:host ::ng-deep .selected-list .c-btn {
  width: 100%;
}

.mat-expansion-indicator {
  display: none;
}
.label {
  margin-left: 3px;
}
