import { ChangeDetectionStrategy, Component, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import _ from 'lodash';
import { ToastrService } from 'ngx-toastr';
import { Observable } from 'rxjs';
import { CalibrationService } from 'src/app/app-services/calibration.service';
import { RegionsService } from 'src/app/app-services/regions-service';
import { RewardPointsService } from 'src/app/app-services/reward-points-service';
import { SeasonService } from 'src/app/app-services/season.service';
import { SidebarServiceService } from 'src/app/app-services/sidebar-service.service';
import { GlobalEvents } from 'src/app/helpers/global.events';
import { Utility } from 'src/app/shared/utility/utility';
import { BaThemeSpinner } from 'src/app/theme/services/baThemeSpinner/baThemeSpinner.service';
import { MatExpansionModule } from '@angular/material/expansion';
import { CommonModule } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ComingSoonComponent } from 'src/app/shared/coming-soon/coming-soon.component';


export interface year {
  id: number;
  name: string;
}

const Year_Data: year[] = [
  { id: 1, name: '2024' },
  { id: 2, name: '2025' },
  { id: 3, name: '2026' },
  { id: 4, name: '2027' },
  { id: 5, name: '2028' },
  { id: 6, name: '2029' },
  { id: 7, name: '2030' },
];


// @Component({
//   selector: 'app-calibration',
//   templateUrl: './calibration.component.html',
//   styleUrl: './calibration.component.scss'
// })

@Component({
   selector: 'app-calibration',
  templateUrl: './calibration.component.html',
  styleUrl: './calibration.component.scss',
  changeDetection: ChangeDetectionStrategy.Default,
  imports: [
    MatExpansionModule,
    CommonModule,
    ComingSoonComponent
  ],
  providers: [BaThemeSpinner]
})
export class CalibrationComponent {


  // constructor(private spinner: BaThemeSpinner,){
  //   this.spinner.hide();
  // }

  @ViewChild('uploadCalibration') uploadCalibration: TemplateRef<any> | any;
  uploadCalibrationDialogRef: MatDialogRef<any> | any;
  panelOpenState = false;
  isFieldCrop: boolean = true;
  isForages: boolean = false;
  bussinessUnitDetails: any = [];
  seasonDialogDataList: any = [];
  seasonDialogData: any = [];
  bussunessUnit: any = [];
  yearDataList: any = [];
  year: any = [];
  selectedRegionCodes: any = [];
  regionDataList: any = [];
  regions: any = [];
  calibrationDataList: any = [];
  calibrationID = 0;
  selectedRegionId: number = 0;
  totalRecordCount: number = 0;
  selectedYear: string = '';
  selectedFCseasonID: string = '';
  formData: any;
  fileName: string = '';
  calibrationJSONData: any;
  bussinessUnitDropdownSettings = {
    text: 'Select Business Unit',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  seasonYearDropdownSettings = {
    text: 'Select Season',
    enableSearchFilter: true,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  yearDropdownSettings = {
    text: 'Select Year',
    enableSearchFilter: true,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  regionDropdownSettings = {
    text: 'Select Region',
    enableSearchFilter: true,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  constructor(
    private spinner: BaThemeSpinner,
    private router: Router,
    public events: GlobalEvents,
    public toastr: ToastrService,
    private calibrationService: CalibrationService,
    private utility: Utility,
    private regionsService: RegionsService,
    private sidebarService: SidebarServiceService,
    private rewardPointService: RewardPointsService,
    public dialog: MatDialog,
    private seasonService: SeasonService
  ) {
    this.spinner.hide();
    this.events.setChangedContentTopText('Calibration');
  }

  ngOnInit(): void {
    this.getAllScanningCategory();
    this.getAllBuDetails();
    this.regionsDropdown();
    this.bussinessUnitDropdown();
    this.tabChanged();
  }

  getAllScanningCategory() {
    this.calibrationService.returnScanningCategoryData().subscribe({
      next: (data) => { },
      error: (errorResponse) => {
        this.spinner.hide();
        let error: any = this.utility.decrypt(errorResponse.error)
        error = JSON.parse(error);
        this.toastr.error(error.message);
      },
    });
  }

  /**
   * Method for the page change event
   * @param page
   */
  regionsDropdown(): void {
    this.regionDataList = [];
    let data = {
      scanCategoryID: this.isFieldCrop ? 1 : 2,
    };
    const allRegions = this.regionsService.getRegionDropdownDataCalibration(data);
    allRegions.subscribe((regionDetails: any) => {
      regionDetails = JSON.parse(this.utility.decrypt(regionDetails));
      regionDetails.forEach((regionInfo: any) => {
        const regionInfoObj = {
          id: regionInfo.id,
          name: regionInfo.name,
          code: regionInfo.code,
          zoneId: regionInfo.zoneId,
          active: regionInfo.active,
        };
        this.regionDataList.push(regionInfoObj);
      });
    });
  }

  /**
   *
   * @param event
   */
  onRegionSelect(event: any) {
    this.selectedRegionId = event.id;
    if (!_.includes(this.selectedRegionCodes, event.id)) {
      this.selectedRegionCodes.push(event.id);
    }
  }

  onRegionDeselect(event: any) {
    if (_.includes(this.selectedRegionCodes, event.id)) {
      _.remove(this.selectedRegionCodes, (item) => item === event.id);
    }
    this.selectedRegionId = 0;
  }

  onSelectAllRegion(event: any) {
    if (event && event.length) {
      this.selectedRegionCodes = [];
      this.selectedRegionCodes = _.map(event, 'id');
    }
  }

  onDeselectAllRegion(event: any) {
    this.selectedRegionCodes = [];
    this.selectedRegionId = 0;
  }

  /**
   *
   * @param event
   */
  bussinessUnitDropdown() {
    this.bussinessUnitDetails = [];
    const allRegions = this.calibrationService.returnBusinessUnitDropdownData();
    allRegions.subscribe((BUDetails: any) => {
      BUDetails = JSON.parse(this.utility.decrypt(BUDetails));
      BUDetails.forEach((BUInfo: any) => {
        const BUInfoObj = {
          id: BUInfo.id,
          name: BUInfo.name,
          active: BUInfo.active,
        };
        this.bussinessUnitDetails.push(BUInfoObj);
      });
    });
  }

  /**
   * This method will used for getting season response from api
   */
  getAllSeasonData() {
    let data = {
      scanCategoryId: this.calibrationID,
    };
    this.seasonService.returnAllCalibrationSeasonData(data).subscribe({
      next: (seasonDetails: any) => {
        seasonDetails = JSON.parse(this.utility.decrypt(seasonDetails));
        this.seasonDialogDataList = [];

        if (this.calibrationID == 1) {
          seasonDetails.forEach((seasonInfo: any) => {
            const seasonObject = {
              id: seasonInfo.id,
              scanYear: seasonInfo.scanYear,
              name:
                seasonInfo.season.name.charAt(0).toUpperCase() +
                seasonInfo.season.name.slice(1).toLowerCase() +
                ' - ' +
                seasonInfo.scanYear,
            };
            this.seasonDialogDataList.push(seasonObject);
          });
        } else if (this.calibrationID == 2) {
          seasonDetails.forEach((seasonInfo: any) => {
            const seasonObject = {
              id: seasonInfo.id,
              scanYear: seasonInfo.scanYear,
              name: seasonInfo.scanYear,
            };
            this.seasonDialogDataList.push(seasonObject);
          });
        }
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
        let error: any = this.utility.decrypt(errorResponse.error)
        error = JSON.parse(error);
        if ('Full authentication is required to access this resource' == error.message) {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  /**
   * This method will used for getting year response
   */
  getSeasonRes() {
    Year_Data.forEach((data) => {
      const seasonObject = {
        id: data.id,
        name: data.name,
      };
      this.yearDataList.push(seasonObject);
    });
  }

  /**
   * This method will used for getting bussiness unit response from api
   */
  getAllBuDetails() {
    if (this.isFieldCrop) {
      let data = {
        scanCategoryId: this.isFieldCrop ? 1 : 2,
      };
      this.calibrationService.returnAllBusinessUnitData(data).subscribe({
        next: (businessUnitDetails: any) => {
          let fileName: any = '';
          businessUnitDetails = JSON.parse(
            this.utility.decrypt(businessUnitDetails)
          );
          this.calibrationDataList = [];
          businessUnitDetails.forEach(
            (businessUnitInfo: any, index: number) => {
              businessUnitInfo.calibrations.forEach((element: any) => {
                fileName = '';
                fileName = element.fileName.split('-').pop();
              });
              let businessUnitObject = {
                year: businessUnitInfo.year,
                season: businessUnitInfo.season,
                calibrations: businessUnitInfo.calibrations,
                fileName: fileName,
              };
              this.calibrationDataList.push(businessUnitObject);
            }
          );
          this.totalRecordCount = this.calibrationDataList.length;
          this.spinner.hide();
        },
        error: (errorResponse: any) => {
          this.spinner.hide();
          let error: any = this.utility.decrypt(errorResponse.error)
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message);
          }
        },
      });
    } else if (this.isForages) {
      let fileName: any = '';
      let data = {
        scanCategoryId: this.isFieldCrop ? 1 : 2,
      };
      this.calibrationService.returnAllBusinessUnitData(data).subscribe({
        next: (businessUnitDetails: any) => {
          businessUnitDetails = JSON.parse(
            this.utility.decrypt(businessUnitDetails)
          );
          this.calibrationDataList = [];
          businessUnitDetails.forEach(
            (businessUnitInfo: any, index: number) => {
              businessUnitInfo.calibrations.forEach((element: any) => {
                fileName = element.fileName.split('-').pop();
              });
              let businessUnitObject = {
                year: businessUnitInfo.year,
                season: businessUnitInfo.season,
                calibrations: businessUnitInfo.calibrations,
                fileName: fileName,
              };
              this.calibrationDataList.push(businessUnitObject);
            }
          );
          this.totalRecordCount = this.calibrationDataList.length;
          this.spinner.hide();
        },
        error: (errorResponse: any) => {
          this.spinner.hide();
          let error: any = this.utility.decrypt(errorResponse.error)
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message);
          }
        },
      });
    } else {
      let data = {
        scanCategoryId: this.isFieldCrop ? 1 : 2,
      };
      this.calibrationService.returnAllBusinessUnitData(data).subscribe({
        next: (businessUnitDetails: any) => {
          businessUnitDetails = JSON.parse(
            this.utility.decrypt(businessUnitDetails)
          );
          this.calibrationDataList = [];
          businessUnitDetails.content.forEach(
            (businessUnitInfo: any, index: number) => {
              let businessUnitObject = {
                seasonName: businessUnitInfo.scanningCategory.season.name,
                year: businessUnitInfo.scanningCategory.scanYear,
                scanningCategoryAndRegionMapping:
                  businessUnitInfo.scanningCategoryAndRegionMapping,
              };
              this.calibrationDataList.push(businessUnitObject);
            }
          );
          this.spinner.hide();
        },
        error: (errorResponse: any) => {
          this.spinner.hide();
          let error: any = this.utility.decrypt(errorResponse.error)
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message);
          }
        },
      });
    }
  }

  getAllBusinessUnitDropdown() { }

  openUploadCalibration() {
    // this.rewardPointService._disabledSidebar.emit(true);
    // let sidebarValue = this.sidebarService.getBooleanValue();
    // this.rewardPointService._openedPopup.emit(true);
    // this.uploadCalibrationDialogRef = this.dialog.open(this.uploadCalibration, {
    //   width: '50%',
    //   disableClose: true,
    //   panelClass: 'confirm-dialog-container',
    //   data: '',
    //   hasBackdrop: true,
    // });
    // this.uploadCalibrationDialogRef.afterClosed().subscribe((result) => {
    //   this.rewardPointService._disabledSidebar.emit(false);
    //   this.rewardPointService._sidebarPin.emit(false);
    // });
  }

  /**
   * This method will used for close upload calibration and clear data
   */
  closeCalibrationDialog() {
    this.bussunessUnit = [];
    this.calibrationID = 0;
    this.seasonDialogData = [];
    this.selectedFCseasonID = '';
    this.regions = [];
    this.selectedRegionId = 0;
    this.fileName = '';
    this.formData = new FormData();
    this.uploadCalibrationDialogRef.close();
  }

  downloadTemplate() {
    this.spinner.show();
    const exports: Observable<Blob> =
      this.calibrationService.downloadTemplate();
    exports.subscribe({
      next: (exportsData: Blob) => {
        this.saveDataAsExcel(exportsData, 'Calibration_Template');
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
        let error: any = this.utility.decrypt(errorResponse.error)
        error = JSON.parse(error);
        if ('Full authentication is required to access this resource' == error.message) {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  downloadCalibrationFile(file: any) {
    this.spinner.show();
    let data = {
      id: file.id,
    };
    const exports: Observable<Blob> =
      this.calibrationService.downloadCalibrationFile(data);
    exports.subscribe({
      next: (exportsData: Blob) => {
        this.saveDataAsExcel(exportsData, file.fileName.split('-')[2]);
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
        let error: any = this.utility.decrypt(errorResponse.error)
        error = JSON.parse(error);
        if ('Full authentication is required to access this resource' == error.message) {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  saveDataAsExcel(data: Blob, fileName: string): void {
    const excelData = new Blob([data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    // saveAs(excelData, fileName + '.xlsx');
  }

  uploadedFiles(event: any): void {
    const file = event.target.files[0];
    this.fileName = file.name;
    this.formData = new FormData();
    this.formData.append('file', file);
  }

  validateExcelFile(event: any) {
    const allowedExtensions = /(\.xlsx|\.xls)$/i;
    const fileInput = event.target;
    const errorMessage = document.getElementById('error-message');

    if (!allowedExtensions.exec(fileInput.value)) {
      fileInput.value = '';
      this.toastr.error('Please upload a valid Excel file.');
    }
  }

  submitCalibrationDetails() {
    this.spinner.show();
    this.calibrationJSONData = {
      scanningCategory: {
        id: 0,
        isActive: true,
        season: {
          id: '',
          uId: '',
          name: '',
          active: true,
        },
        scanCategory: {
          id: '',
          name: '',
          description: '',
          active: true,
          uId: '',
        },
        scanYear: '',
        uScanYear: '',
        uId: this.utility.encrypt(this.selectedFCseasonID.toString()),
      },
      region: {
        uId: this.utility.encrypt(this.selectedRegionId.toString()),
      },
      uploadedBy: '',
    };
    this.calibrationJSONData = JSON.stringify(this.calibrationJSONData);
    this.formData.append('calibration', this.calibrationJSONData);
    this.calibrationService.returnAddCalibrationData(this.formData).subscribe({
      next: (calibrationData: any) => {
        this.closeCalibrationDialog();
        this.getAllBuDetails();
        calibrationData = JSON.parse(this.utility.decrypt(calibrationData));
        this.spinner.hide();
        if (calibrationData.body.error) {
          this.toastr.error(calibrationData.body.message);
        } else {
          this.toastr.success(calibrationData.body.message);
          this.closeCalibrationDialog();
        }
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
        let error: any = this.utility.decrypt(errorResponse.error)
        error = JSON.parse(error);
        if ('Full authentication is required to access this resource' == error.message) {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
        this.closeCalibrationDialog();
      },
    });
  }

  tabChanged(tabChangeEvent?: any): void {
    if (tabChangeEvent === 'FIELD_CROPS') {
      this.isFieldCrop = true;
      this.isForages = false;
      this.spinner.show();
      this.calibrationDataList = [];
      this.calibrationID = 1;
      this.getAllBuDetails();
      this.getAllSeasonData();
      this.regionsDropdown();
    } else if (tabChangeEvent === 'FORAGES') {
      this.isFieldCrop = false;
      this.isForages = true;
      this.spinner.show();
      this.calibrationDataList = [];
      this.calibrationID = 2;
      this.getAllBuDetails();
      this.getAllSeasonData();
      this.regionsDropdown();
    } else {
      this.isFieldCrop = true;
      this.isForages = false;
      this.calibrationDataList = [];
      this.calibrationID = 1;
      this.getAllBuDetails();
      this.getAllSeasonData();
      this.regionsDropdown();
    }
  }

  toPascalCase(input: string): string {
    if (!input) {
      return '';
    }
    return input.replace(/(\w)(\w*)/g, function (_, first, rest) {
      return first.toUpperCase() + rest.toLowerCase();
    });
  }

  onBussinessUnitSelect(event: any) {
    this.calibrationID = event.id;
  }

  onBussinessUnitDeselect(event: any) {
    this.calibrationID = 0;
  }

  onDeselectAllBussinessUnit(event: any) {
    this.calibrationID = 0;
  }

  onSeasonSelect(event: any) {
    this.selectedFCseasonID = event.id;
  }

  onSeasonDeselect(event: any) {
    this.selectedFCseasonID = '';
  }

  onDeselectAllSeason(event: any) {
    this.selectedFCseasonID = '';
  }

  onYearSelect(event: any) { }

  onYearDeselect(event: any) { }

  onDeselectAllYear(event: any) { }
}
