import { Component, OnInit, AfterViewInit } from "@angular/core";
import { BaThemeSpinner } from "src/app/theme/services/baThemeSpinner";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { RegionsService } from "src/app/app-services/regions-service";
import { CustomDropdownComponent, DropdownOption } from "src/app/shared/components/custom-dropdown/custom-dropdown.component";
import * as Highcharts from 'highcharts';
import { HighchartsChartModule } from 'highcharts-angular';
import AccessibilityModule from 'highcharts/modules/accessibility';
import { NumberFormatService } from "src/app/shared/shared/number-format.service";
import { Utility } from "src/app/shared/utility/utility"; // Import the Utility service

AccessibilityModule(Highcharts);

@Component({
  selector: "dashboard",
  styleUrls: ["./dashboard.scss"],
  templateUrl: "./dashboard.html",
  standalone: true,
  imports: [CommonModule, FormsModule, CustomDropdownComponent, HighchartsChartModule],
})
export class Dashboard implements OnInit, AfterViewInit {
  topRegions: any[] = [];
  topZones: any[] = [];
  totalRegions: any = {};
  totalZones: any = {};
  topLeaders: any = {};
  selectedRegionName: any = 'All';
  accessibility!: {
    enabled: true;
  };
  Highcharts: typeof Highcharts = Highcharts;
  chartOptions: Highcharts.Options = {
    credits: {
      enabled: false
    },
    chart: {
      type: 'column'
    },
    title: {
      text: ''
    },
    legend: {
      enabled: false
    },
    xAxis: {
      categories: [],
      title: {
        text: 'Zone'
      }
    },
    yAxis: {
      min: 0,
      max: 100,
      title: {
        text: 'Percentage %'
      }
    },
    tooltip: {
      formatter: function () {
        return `<b>${this.point.name}</b><br/>Percentage: <b>${this.point.y}%</b>`;
      }
    },
    series: [
      {
        name: '',
        type: 'column',
        data: [],
        colorByPoint: true
      }
    ]
  };

  // Filter properties
  regions: DropdownOption[] = [];
  selectedRegion: string = '';
  zones: DropdownOption[] = [];
  selectedZone: string = '';
  fiscalYears: DropdownOption[] = [];
  selectedFY: string = '';
  isLoading: boolean = false;
  useHardcodedData: boolean = true;
  regionSortColumn: string | null = null;
  regionSortDirection: 'asc' | 'desc' = 'asc';
  originalTopRegions: any[] = [];
  zoneSortColumn: string | null = null;
  zoneSortDirection: 'asc' | 'desc' = 'asc';
  originalTopZones: any[] = [];
  leaderSortColumn: string | null = null;
  leaderSortDirection: 'asc' | 'desc' = 'asc';
  originalTopLeaders: any[] = [];

  constructor(
    private spinner: BaThemeSpinner,
    private regionsService: RegionsService,
    private numberFormateService: NumberFormatService,
    private utility: Utility // Inject the Utility service
  ) { }

  ngOnInit() {
    this.loadFilterData();
    // this.fetchDashboardData();
    // this.getTopLeader();
  }

  ngAfterViewInit() {
    // No need for manual chart initialization with highcharts-angular
  }

  loadFilterData() {
    this.isLoading = true;
    this.useHardcodedData = false;
    if (this.useHardcodedData) {
      this.isLoading = false;
    } else {
      this.loadRegionsFromAPI();
      this.loadFiscalYearsFromAPI();
    }
  }

  loadRegionsFromAPI() {
    this.regions = [];
    this.regionsService.getRegion().subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        const mappedRegions = leaderArray.map((item: any) => ({
          id: item.id,
          name: item.name,
          code: item.code,
        }));
  
        // Prepend "All" option with empty id
        this.regions = [
          {
            id: '',
            name: 'All',
            code: null
          },
          ...mappedRegions
        ];
  
        this.selectedRegion = '';
      },
      (error) => {
        console.error('Failed to fetch regions', error);
      }
    );
  }



  loadZonesFromAPI(id: any) {
    const data = {
      zoneId: id
    };
    this.zones = [];
    this.regionsService.getAllZonesByPC(data).subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.zones = leaderArray.map((item: any) => ({
          id: item.id,
          name: item.name,
          code: item.code,
        }));
      },
      (error) => {
        error = this.utility.encryptString(error.encryptedBody)
        console.error( error);
      }
    );
  
  }
  loadFiscalYearsFromAPI() {
  this.fiscalYears = [];
  this.regionsService.getAllFY().subscribe(
    (response: any) => {
      let leaderArray;
      try {
        // First, check if response is a string and parse it if needed
        let parsedResponse = response;
        if (typeof response === 'string') {
          parsedResponse = JSON.parse(response);
        }
        
        // Check if response has encryptedBody property
        if (parsedResponse && parsedResponse.encryptedBody) {
          // Decrypt the encryptedBody
          const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
          leaderArray = JSON.parse(decrypted);
        } else {
          // Try to parse the response directly
          leaderArray = JSON.parse(response);
        }
      } catch (error) {
        console.error('Error parsing fiscal year response:', error);
        leaderArray = [];
      }
      
      // Check if leaderArray is actually an array
      if (Array.isArray(leaderArray)) {
        this.fiscalYears = leaderArray.map((item: any) => ({
          id: item.id,
          name: item.financialYear,
        }));

        // Automatically select current year
        const currentYear = new Date().getFullYear().toString();
        const matchedFY = this.fiscalYears.find(fy => fy.name.includes(currentYear));

        if (matchedFY) {
          this.selectedFY = matchedFY.id;
          this.fetchDashboardData(); // Optional: fetch data on load
        }
      } else {
        console.error('Fiscal year data is not an array:', response);
        this.fiscalYears = [];
      }
    },
    (error) => {
      console.error('Failed to fetch fiscal years', error);
    }
  );
}

  checkLoadingComplete() {
    if (this.regions.length > 0 && this.zones.length > 0 && this.fiscalYears.length > 0) {
      this.isLoading = false;
    }
  }

  onSelectionChange(event: { type: string, value: string, name: string }) {
    this.spinner.show();
    if (event.type === 'region') {
      this.selectedRegionName = event.name;
      this.selectedRegion = event.value;
      this.onRegionChange();
    } else if (event.type === 'zone') {
      this.selectedZone = event.value;
      this.onZoneChange();
    } else if (event.type === 'fy') {
      this.selectedFY = event.value;
      this.onFYChange();
    }
    this.fetchDashboardData();
    // this.getTopLeader();
  }

  onRegionChange() {
    this.loadZonesFromAPI(this.selectedRegion);
    this.selectedZone = '';
    // this.applyFilters();
  }

  onZoneChange() {
    // this.applyFilters();
  }

  onFYChange() {
    // this.applyFilters();
  }


  fetchDashboardData() {
    this.getTopRegion();
    this.getTopZone();
    this.getTopLeader();
  }

  getTopZone() {
    let data = {
      regionId: this.selectedRegion,
      yearId: this.selectedFY
    };
    
    this.topZones = [];
    this.totalZones = [];
    this.regionsService.getTopZone(data).subscribe(
      (response: any) => {
        let topZoneArray;
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = response;
          if (typeof response === 'string') {
            parsedResponse = JSON.parse(response);
          }
          
          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            topZoneArray = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response
            try {
              const decrypted = this.utility.decrypt(response);
              topZoneArray = JSON.parse(decrypted);
            } catch (e) {
              // If that fails, try to parse the response directly
              topZoneArray = JSON.parse(response);
            }
          }
        } catch (error) {
          console.error('Error parsing top zone response:', error);
          topZoneArray = { dashboardSummaryDTO: [] };
        }
        
        if (topZoneArray && Array.isArray(topZoneArray.dashboardSummaryDTO)) {
            this.topZones = topZoneArray.dashboardSummaryDTO.map((item: any) => ({
              name: item.name,
              totalTargetAmount: item.totalTargetAmount,
              achievedTotalAmount: item.achievedTotalAmount,
              achievedTotalPercentage: item.achievedTotalPercentage,
              // Formatted versions for display in tables
              convenioFY24: this.numberFormateService.formatNumber(item.totalTargetAmount),
              avanceCrop: this.numberFormateService.formatNumber(item.achievedCropProtectionAmount),
              avanceNPP: this.numberFormateService.formatNumber(item.achievedNppAmount),
              avanceAmount: this.numberFormateService.formatNumber(item.achievedTotalAmount)
            }));

            let totalZoneAmount = {
              convenioFY24: this.numberFormateService.formatNumber(topZoneArray?.overallTotalTargetAmount),
              avanceCrop: this.numberFormateService.formatNumber(topZoneArray.overallAchievedCropProtectionAmount),
              avanceNPP: this.numberFormateService.formatNumber(topZoneArray.overallAchievedNppAmount),
              avanceAmount: this.numberFormateService.formatNumber(topZoneArray.overallAchievedTotalAmount),
              achievedTotalPercentage: this.numberFormateService.formatPercentage(topZoneArray.overallAchievedTotalPercentage)
            };
            this.totalZones = totalZoneAmount;
      
          // Update chartOptions with new data
          // this.updateChart();
        } else {
          // this.updateChart();
          this.topZones = [];
          console.error('Top zone data is not in expected format:', topZoneArray);
        }
        this.updateChart();
        this.spinner.hide();
      },
      (error) => {
        this.spinner.hide();
        this.topZones = [];
        console.error('Failed to fetch top zones', error);
      }
    );
  }

  getTopRegion() {
    let data = {
      regionId: this.selectedRegion,
      yearId: this.selectedFY
    };
    
    this.topRegions = [];
    this.totalRegions = [];
    this.regionsService.getTopRegion(data).subscribe(
      (response: any) => {
        let topRegionArray;
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = response;
          if (typeof response === 'string') {
            parsedResponse = JSON.parse(response);
          }
          
          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            topRegionArray = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response
            try {
              const decrypted = this.utility.decrypt(response);
              topRegionArray = JSON.parse(decrypted);
            } catch (e) {
              // If that fails, try to parse the response directly
              topRegionArray = JSON.parse(response);
            }
          }
        } catch (error) {
          console.error('Error parsing top region response:', error);
          topRegionArray = { dashboardSummaryDTO: [] };
        }
        
        if (topRegionArray && Array.isArray(topRegionArray.dashboardSummaryDTO)) {
          this.topRegions = topRegionArray.dashboardSummaryDTO.map((item: any) => ({
            name: item.name || '',
            convenioFY24: this.numberFormateService.formatNumber(item.totalTargetAmount) || 0,
            avanceCrop: this.numberFormateService.formatNumber(item.achievedCropProtectionAmount || 0),
            avanceNPP: this.numberFormateService.formatNumber(item.achievedNppAmount || 0),
            avanceAmount: this.numberFormateService.formatNumber(item.achievedTotalAmount || 0),
            achievedTotalPercentage: this.numberFormateService.formatPercentage(item.achievedTotalPercentage || 0)
          }));
          
          this.originalTopRegions = JSON.parse(JSON.stringify(this.topRegions));
          let totalAmount = {
            convenioFY24: this.numberFormateService.formatNumber(topRegionArray?.overallTotalTargetAmount) || 0,
            avanceCrop: this.numberFormateService.formatNumber(topRegionArray?.overallAchievedCropProtectionAmount || 0),
            avanceNPP: this.numberFormateService.formatNumber(topRegionArray?.overallAchievedNppAmount || 0),
            avanceAmount: this.numberFormateService.formatNumber(topRegionArray?.overallAchievedTotalAmount || 0),
            achievedTotalPercentage: this.numberFormateService.formatPercentage(topRegionArray?.overallAchievedTotalPercentage || 0)
          };
                    
          this.totalRegions = totalAmount;
        } else {
          console.error('Top region data is not in expected format:', topRegionArray);
        }
        
        this.spinner.hide();
      },
      (error) => {
        this.spinner.hide();
        console.error('Failed to fetch top regions', error);
      }
    );
  }

  getTopLeader() {
    this.spinner.show();
    let data = {
      regionId: this.selectedRegion,
      yearId: this.selectedFY
    };
    
    this.topLeaders = [];
    this.regionsService.getTopLeader(data).subscribe(
      (response: any) => {
        let leaderArray;
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = response;
          if (typeof response === 'string') {
            parsedResponse = JSON.parse(response);
          }
          
          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            leaderArray = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response
            try {
              const decrypted = this.utility.decrypt(response);
              leaderArray = JSON.parse(decrypted);
            } catch (e) {
              // If that fails, try to parse the response directly
              leaderArray = JSON.parse(response);
            }
          }
        } catch (error) {
          console.error('Error parsing top leader response:', error);
          leaderArray = [];
        }
        
        if (Array.isArray(leaderArray)) {
          this.topLeaders = leaderArray.map((item: any) => ({
            name: item.name,
            assignedTarget: this.numberFormateService.formatNumber(item.totalTargetAmount),
            achievedTarget: this.numberFormateService.formatNumber(item.achievedTotalAmount),
            achievedBonus: this.numberFormateService.formatNumber(item.totalBonus),
          }));
          this.originalTopLeaders = JSON.parse(JSON.stringify(this.topLeaders));
        } else {
          console.error('Top leader data is not an array:', leaderArray);
        }
        
        this.spinner.hide();
      },
      (error) => {
        this.spinner.hide();
        console.error('Failed to fetch top leaders', error);
      }
    );
  }

updateChart() {
  if (!this.topZones || this.topZones.length === 0) {
    this.chartOptions = {
      credits: { enabled: false },
      chart: { type: 'column' },
      title: { 
        text: 'No Data Available',
        style: {
          fontSize: '14px',
          fontWeight: '400',
          color: 'black'
        }
      },
      xAxis: { categories: [], title: { text: 'Zone' } },
      yAxis: { min: 0, max: 100, title: { text: 'Percentage %' } },
      series: [{
        name: '',
        type: 'column',
        data: [],
        dataLabels: { enabled: false }
      }],
      plotOptions: {
        column: { colorByPoint: true, colors: ['#FF8E49'] }
      }
    };
    return;
  }

  // Prepare chart data with original styling but fixed functionality
  const chartData = this.topZones.map(zone => ({
    name: zone.name,
    y: Math.min(zone.achievedTotalPercentage, 100), // Cap at 100% for display
    actualPercentage: zone.achievedTotalPercentage, // Keep original value
    totalTargetAmount: this.numberFormateService.formatNumber(zone.totalTargetAmount),
    achievedTotalAmount: this.numberFormateService.formatNumber(zone.achievedTotalAmount)
  }));

  this.chartOptions = {
    credits: {
      enabled: false
    },
    chart: {
      type: 'column'
    },
    title: {
      text: '',
      style: {
        fontSize: '12px', // Decrease the font size
        fontWeight: 'normal', // Optional: Adjust font weight
        color: '#666666' // Optional: Adjust text color
      }
    },
    legend: {
      enabled: false
    },
    xAxis: {
      categories: chartData.map(item => item.name),
      title: {
        text: 'Zone'
      }
    },
    yAxis: {
      min: 0,
      max: 100,
      title: {
        text: 'Percentage %'
      }
    },
    tooltip: {
      formatter: function () {
        const point = this.point as any;
        return `
          Total Amount: <b>Mex$ ${point.totalTargetAmount}</b><br/>
          Achieved Amount: <b>Mex$ ${point.achievedTotalAmount}</b><br/>
          Achieved Percentage: <b>${point.actualPercentage}%</b>
        `;
      }
    },
    plotOptions: {
      column: {
        colorByPoint: true,
        colors: ['#FF8E49'] 
      }
    },
    series: [{
      name: '',
      type: 'column',
      data: chartData,
      dataLabels: {
        enabled: true,
        format: '{point.actualPercentage}%'
      }
    }]
  };
}

  // Helper method to safely parse JSON with error handling
  safelyParseJSON(jsonString: string): any {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('Error parsing JSON:', error);
      return null;
    }
  }

  // Helper method to handle API responses with potential encryption
  handleApiResponse(response: any): any {
    // Check if response has encryptedBody property
    if (response && response.encryptedBody) {
      try {
        // Decrypt the encryptedBody
        const decrypted = this.utility.decrypt(response.encryptedBody);
        return this.safelyParseJSON(decrypted);
      } catch (error) {
        console.error('Error decrypting encryptedBody:', error);
      }
    }
    
    // If no encryptedBody or decryption failed, try to decrypt the entire response
    try {
      const decrypted = this.utility.decrypt(response);
      return this.safelyParseJSON(decrypted);
    } catch (error) {
      // If that fails, try to parse the response directly
      return this.safelyParseJSON(response);
    }
  }

  sortRegionsData(column: string): void {
    if (this.regionSortColumn === column) {
      if (this.regionSortDirection === 'asc') {
        this.regionSortDirection = 'desc';
      } else if (this.regionSortDirection === 'desc') {
        this.regionSortColumn = null;
        this.regionSortDirection = 'asc';
        if (this.originalTopRegions && this.originalTopRegions.length > 0) {
          this.topRegions = JSON.parse(JSON.stringify(this.originalTopRegions));
        }
        return;
      }
    } else {
      this.regionSortColumn = column;
      this.regionSortDirection = 'asc';
    }
    const data = [...this.topRegions];
    
    data.sort((a, b) => {
      let valueA = a[column];
      let valueB = b[column];
      
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        const numA = parseFloat(valueA.replace(/[^0-9.-]+/g, ''));
        const numB = parseFloat(valueB.replace(/[^0-9.-]+/g, ''));
        
        if (!isNaN(numA) && !isNaN(numB)) {
          return this.regionSortDirection === 'asc' ? numA - numB : numB - numA;
        }
      }
      
      valueA = String(valueA || '').toLowerCase();
      valueB = String(valueB || '').toLowerCase();
      
      return this.regionSortDirection === 'asc' 
        ? valueA.localeCompare(valueB) 
        : valueB.localeCompare(valueA);
    });
    
    this.topRegions = data;
  }

  sortZonesData(column: string): void {
    if (this.zoneSortColumn === column) {
      if (this.zoneSortDirection === 'asc') {
        this.zoneSortDirection = 'desc';
      } else if (this.zoneSortDirection === 'desc') {
        this.zoneSortColumn = null;
        this.zoneSortDirection = 'asc';
        if (this.originalTopZones && this.originalTopZones.length > 0) {
          this.topZones = JSON.parse(JSON.stringify(this.originalTopZones));
        }
        return;
      }
    } else {
      this.zoneSortColumn = column;
      this.zoneSortDirection = 'asc';
    }
    const data = [...this.topZones];
    
    data.sort((a, b) => {
      let valueA = a[column];
      let valueB = b[column];
      
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        const numA = parseFloat(valueA.replace(/[^0-9.-]+/g, ''));
        const numB = parseFloat(valueB.replace(/[^0-9.-]+/g, ''));
        
        if (!isNaN(numA) && !isNaN(numB)) {
          return this.zoneSortDirection === 'asc' ? numA - numB : numB - numA;
        }
      }
      valueA = String(valueA || '').toLowerCase();
      valueB = String(valueB || '').toLowerCase();
      
      return this.zoneSortDirection === 'asc' 
        ? valueA.localeCompare(valueB) 
        : valueB.localeCompare(valueA);
    });
    
    this.topZones = data;
  }

  sortLeadersData(column: string): void {
    if (this.leaderSortColumn === column) {
      if (this.leaderSortDirection === 'asc') {
        this.leaderSortDirection = 'desc';
      } else if (this.leaderSortDirection === 'desc') {
        // Third click - reset to default order
        this.leaderSortColumn = null;
        this.leaderSortDirection = 'asc';
        
        // Reset to original data order
        if (this.originalTopLeaders && this.originalTopLeaders.length > 0) {
          this.topLeaders = JSON.parse(JSON.stringify(this.originalTopLeaders));
        }
        return;
      }
    } else {
      this.leaderSortColumn = column;
      this.leaderSortDirection = 'asc';
    }

    // Sort the data
    const data = [...this.topLeaders];
    
    data.sort((a, b) => {
      // Special handling for index column (Sr.No)
      if (column === 'index') {
        const indexA = data.indexOf(a);
        const indexB = data.indexOf(b);
        return this.leaderSortDirection === 'asc' ? indexA - indexB : indexB - indexA;
      }
      
      let valueA = a[column];
      let valueB = b[column];
      
      // Handle numeric values with currency formatting
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        // Remove currency formatting if present
        const numA = parseFloat(valueA.replace(/[^0-9.-]+/g, ''));
        const numB = parseFloat(valueB.replace(/[^0-9.-]+/g, ''));
        
        if (!isNaN(numA) && !isNaN(numB)) {
          return this.leaderSortDirection === 'asc' ? numA - numB : numB - numA;
        }
      }
      
      // Handle string values
      valueA = String(valueA || '').toLowerCase();
      valueB = String(valueB || '').toLowerCase();
      
      return this.leaderSortDirection === 'asc' 
        ? valueA.localeCompare(valueB) 
        : valueB.localeCompare(valueA);
    });
    
    this.topLeaders = data;
  }
}
