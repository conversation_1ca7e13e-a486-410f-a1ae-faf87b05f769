@import "../../theme/sass/conf/mixins";


/* Add proper stacking context */
.dashboard-container {
  width: 100%;
  padding: 15px;
  display: flex;
  flex-direction: column;

}

.dashboard-filters {
  margin-top: 70px;
  // margin-bottom: 20px;
  width: 100%;
  // padding: 15px 15px;
  position: relative;
  z-index: 2; /* Higher than container but lower than header elements */
}

.filter-container {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  max-width: 1200px;
  // margin: 0 auto;
  margin-left: 10px !important;
  justify-content: flex-start ;
}

.filter-row {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.filter-item {
  position: relative;
  min-width: 200px;
  flex: 1;
  margin-bottom: 10px;
}

.filter-dropdown {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  position: relative;
  /* z-index: 5; removed */
}

.dropdown-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: #ff7f50;
  background-color: #fff0eb;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  /* z-index: 6; removed */
}

.dropdown-icon i {
  font-size: 12px;
}

.filter-dropdown:focus {
  outline: none;
  border-color: #ff7f50;
}

/* Add this to ensure dropdown options are visible */
select option {
  background-color: white;
  color: #333;
  padding: 10px;
}

/* Override the default blue background for hovered options */
select option:hover {
  background-color: #ff7f50 !important;
  color: white !important;
}

/* For webkit browsers (Chrome, Safari) */
@media screen and (-webkit-min-device-pixel-ratio:0) {
  select option:hover {
    background-color: #ff7f50 !important;
  }
}

/* For Firefox */
@-moz-document url-prefix() {
  select option:hover {
    background-color: #ff7f50 !important;
    box-shadow: 0 0 10px 100px #ff7f50 inset !important;
  }
}

/* Ensure dropdowns appear below header elements */
.custom-dropdown {
  position: relative;
  width: 100%;
}

.selected-option {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.options-container {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #ddd;
  border-radius: 0 0 8px 8px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
  box-shadow: 0 5px 10px rgba(0,0,0,0.1);
  opacity: 0;
  visibility: hidden;
  
  /* Hide scrollbar initially but reserve space for it */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    width: 0px;
    background: transparent; /* Chrome/Safari/Webkit */
  }
}

.options-container.show {
  max-height: 200px;
  overflow-y: auto;
  opacity: 1;
  visibility: visible;
  
  /* Show scrollbar when dropdown is open */
  scrollbar-width: thin; /* Firefox */
  -ms-overflow-style: auto; /* IE and Edge */
  &::-webkit-scrollbar {
    width: 6px;
    background: #f1f1f1;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #ff7f50;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }
  
  /* Add a small delay before showing scrollbar */
  animation: showScrollbar 0.3s 0.1s forwards;
}

@keyframes showScrollbar {
  from {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  to {
    scrollbar-width: thin;
    -ms-overflow-style: auto;
  }
}

/* Ensure consistent height for options to prevent layout shifts */
.option {
  padding: 10px 15px;
  cursor: pointer;
  height: 40px; /* Fixed height for each option */
  display: flex;
  align-items: center;
}

.option:hover {
  background-color: #ff7f50;
  color: white;
}

.option.selected {
  background-color: #fff0eb;
  color: #ff7f50;
}

/* Ensure the dashboard content stays below the filters */
.dashboard-content {
  position: relative;
  // margin-top: 20px;
}

/* Ensure filter container has proper stacking context */
.filter-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

/* Search box styling */
.search-box {
  padding: 10px;
  position: relative;
  border-bottom: 1px solid #eee;
}

.search-box input {
  width: 100%;
  padding: 8px 30px 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.search-box input:focus {
  border-color: #ff7f50;
}

.search-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 14px;
}

.options-list {
  max-height: 160px;
  overflow-y: auto;
}

.no-results {
  padding: 15px;
  text-align: center;
  color: #999;
  font-style: italic;
}

/* Adjust the options container to accommodate the search box */
.options-container.show {
  max-height: 250px; /* Increased to accommodate search box */
}

/* Dashboard tables layout */
.dashboard-tables {
  display: flex;
  flex-direction: row;
  gap: 30px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 15px;
  flex: 1;
  min-width: 45%;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
  position: sticky;
  left: 0;
}

/* Create a separate div for the table with overflow */
.table-scroll-container {
  overflow-x: auto;
  width: 100%;
  
  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: #FF8033 #f1f1f1;
  
  /* WebKit browsers (Chrome, Safari) scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background: #f1f1f1;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #FF8033;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }
  
  /* For Internet Explorer */
  -ms-overflow-style: auto;
}

/* Specific scrollbar styling for this component */
.table-scroll-container {
  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb:vertical {
    background-color: #FF8033;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb:horizontal {
    background-color: #999999;
    border-radius: 4px;
  }
}

/* Remove the mixin call that's causing the error */
/* @include scrollbars(.5em, #FF8033, #f1f1f1); */

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  border: 1px solid #ddd;
}

.data-table th, 
.data-table td {
  padding: 10px 15px;
  text-align: center;
  border: 1px solid #ddd; /* Add borders to all cells */
}

.data-table th:first-child,
.data-table td:first-child {
  text-align: center;
}

.currency-cell {
  white-space: nowrap;
  text-align: right;
  font-variant-numeric: tabular-nums;
}

.data-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #555;
}

.data-table thead th {
  color: #ff7f50;
  font-weight: 500;
  border-bottom: 2px solid #ddd; /* Thicker bottom border for header */
}

.total-row {
  font-weight: bold;
  background-color: #fff0eb;
}

.total-row td {
  border-top: 2px solid #ddd; /* Thicker top border for total row */
}

.data-table tr:hover {
  background-color: #f5f5f5;
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table thead th {
  color: #ff7f50;
  font-weight: 500;
}

.total-row {
  font-weight: bold;
  background-color: #fff0eb;
}

.total-row td {
  border-top: 2px solid #eee;
}

/* Center align table headers for all dashboard tables */
.data-table th {
  text-align: center !important;
  background-color: #F6F6F6;
  font-weight: 600;
  color: #555;
}

/* Keep the first column (names) left-aligned */
.data-table td:first-child {
  text-align: center;
}

/* Keep all other data cells right-aligned (for currency values) */
.data-table td:not(:first-child) {
  text-align: center;
}

/* Specific styling for the leaders table */
.leaders-table th {
  text-align: center !important;
}

.leaders-table td:first-child {
  text-align: center; /* Center the Sr.No column */
}

.leaders-table td:nth-child(2) {
  text-align: center; /* Left-align the Name column */
}

.leaders-table td:not(:first-child):not(:nth-child(2)) {
  text-align: center; /* Right-align all other columns (numeric values) */
}

/* Make sure the page header/title has higher z-index */
:host ::ng-deep .content-top {
  position: relative;
  z-index: 5; /* Higher z-index to ensure it stays on top */
}

/* Ensure the page top (header) stays above everything */
:host ::ng-deep .page-top {
  z-index: 10; /* Highest z-index */
}

/* Regional Progress Container - Main wrapper */
.regional-progress-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch; /* Make all children same height */
  width: 100%;
  gap: 30px;
  margin-top: 40px; /* Added margin-top */
  margin-bottom: 30px;
}

/* Chart Container - Left side */
.chart-section {
  flex: 1;
  min-width: 45%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 15px;
  display: flex;
  flex-direction: column;
  height: 100%; /* Ensure full height */
}

.chart-and-legend {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
  position: sticky;
  left: 0;
}

highcharts-chart {
  width: 350px !important;
  height: 350px !important;
  display: block;
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 35%;
  margin-left: 5%;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  display: inline-block;
}

.legend-label {
  color: #333;
}

/* Tooltip styling for the chart */
.chart-tooltip {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

/* Leaders Table Container - Right side */
.leaders-table-container {
  flex: 1;
  min-width: 45%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 15px;
  height: 100%; /* Ensure full height */
}

.leaders-table {
  width: 100%;
  border-collapse: collapse;
}

.leaders-table th {
  color: #FF8033;
  font-weight: 500;
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #ddd;
}

.leaders-table td {
  padding: 8px 10px;
  border-bottom: 1px solid #f0f0f0;
}

.leaders-table .percentage-cell {
  text-align: center;
  color: #FF8033;
  font-weight: 500;
}

/* Media query for smaller screens */
@media (max-width: 1200px) {
  .regional-progress-container {
    flex-direction: column;
    align-items: center;
  }
  
  .chart-section,
  .leaders-table-container {
    width: 100%;
    min-width: 100%;
  }
  
  .leaders-table-container {
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .chart-and-legend {
    flex-direction: column;
  }
  
  highcharts-chart {
    width: 100% !important;
  }
  
  .chart-legend {
    width: 100%;
    margin-left: 0;
    margin-top: 20px;
  }
}

/* Sorting styles */
.sortable {
  cursor: pointer;
  
  .clickable {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    
    i {
      margin-left: 8px;
      padding: 1px 2px;
    }
  }
  
  &:hover {
    background-color: rgba(255, 128, 51, 0.1);
  }
}

/* Custom arrow styles */
.fa-sort-asc:before {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 10px solid #222;
  opacity: 0.8;
}

.fa-sort-desc:before {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 10px solid #222;
  opacity: 0.8;
}

// Add styles for sort arrows
.sort-icon {
  display: inline-block !important;
  margin-left: 5px !important;
  color: #ff7f50 !important;
}

.sort-header {
  display: inline-flex !important;
  align-items: center !important;
  white-space: nowrap !important;
  cursor: pointer;
  user-select: none;
  
  &:hover {
    background-color: rgba(255, 127, 80, 0.1); // Light coral background on hover
  }
  
  &.active {
    color: #ff7f50;
  }
}

th {
  transition: background-color 0.2s ease;
}

.data-table thead tr {
  display: table-row !important;
  width: 100% !important;
}

.data-table th {
  display: table-cell !important;
  white-space: nowrap !important;
  vertical-align: middle !important;
}

.sort-header {
  white-space: nowrap !important;
  display: inline !important;
}

.sort-icon {
  display: inline !important;
  margin-left: 5px !important;
  color: #ff7f50 !important;
  vertical-align: middle !important;
}
