<button mat-icon-button class="close-button" (click)="onCloseDialog()">
  <i class="material-icons close-icon" style="color: primary">close</i>
</button>

<div class="parent-container">
  <div class="next-prev">
    <div class="icon">
      <div class="cropName">
        <h2>{{ name }}</h2>
      </div>
    </div>
  </div>
  <div class="table-card">
    <mat-card class="mat-elevation-z8">
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th
                scope="col"
                style="border-right: 1px solid #0000001a; font-weight: bold"
              >
                Slabs
              </th>
              <th scope="col" style="font-weight: bold">Retailers Count</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of ELEMENT_DATA; let i = index">
              <td scope="row">
                <span *ngIf="dataLength - 1 > i"
                  >{{ item.minQuantity }}-{{ item.maxQuantity }}</span
                >
                <span *ngIf="dataLength - 1 == i"
                  >{{ item.minQuantity }} & More
                </span>
              </td>
              <td>
                {{ item.totalCustomer }}
              </td>
            </tr>
          </tbody>
        </table>
        <div class="no-result" *ngIf="+dataLength === 0">
          No Slabs found
        </div>
      </div>
    </mat-card>
  </div>
</div> 