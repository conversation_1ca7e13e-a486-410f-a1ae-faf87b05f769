.close-button {
  float: right;
  border: none;
  background: none;
  &:focus {
    cursor: pointer;
    outline: none;
  }
}
.parent-container {
  width: 500px;
  height: 90%;
  margin: 0px 64px 0px 100px;
}
.next-prev {
  padding: 0px;
  margin: 0px 0px 0px 0px;
  text-align: center;
  font-size: 32px;
  color: #151965;
  position: relative;
  height: 40px;
  .icon {
    position: relative;
    display: inline-flex;
    margin: 0px 0px 40px 0px;

    .cropName {
      padding: 0px;
      margin: 0px 0px 0px 0px;
    }
  }
  mat-icon:first-child {
    font-size: 52px;
    margin: 0px 100px 0px 0px;
  }
  mat-icon:last-child {
    font-size: 52px;
    margin: 0px 0px 0px 80px;
  }
}
.table-card {
  width: 456.09px;
  height: 308.19px;
  margin: 0px 0px 0px 20px;

  mat-card {
    border-radius: 8px;
    margin: 25px 0px 0px 20px;
    padding: 0px;

    .no-result{
      text-align: center;
      padding: 5px;
    }
  }
  table {
    width: 100%;
    table-layout: fixed;
    border-bottom: none;
    text-align: center;
    td {
      border-radius: 8px;
      border: 1px solid #0000001a;
      border-radius: 10px;
      border-right: none;
    }
    th {
      border-top: none;
      border-bottom: 1px solid #0000001a;
      text-align: center;
    }
    tr {
      border-bottom: none;
      border-right: none;
      border-radius: 3px;
      padding: 0px;
      margin-top: -100px;
    }
  }
}
hr {
  border: none;
  height: 1px;
  background-color: #c0c8c9;
  background-image: -webkit-linear-gradient(0deg, #fff, #c0c8c9, #fff);
  margin-bottom: 20px;
  max-width: 440px;
}

a {
  color: #3d464d;
}
