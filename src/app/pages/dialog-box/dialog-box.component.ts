import { Component, Inject, OnInit } from "@angular/core";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { TerritoriesService } from "src/app/app-services/territories-service";
import { BaThemeSpinner } from "../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { NgFor, NgIf } from "@angular/common";
import { MatCardModule } from "@angular/material/card";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { DashboardService } from "src/app/app-services/dashboard.service";
import { Router } from "@angular/router";
import { ToastrService } from "ngx-toastr";
export interface DialogData {
  animal: string;
  name: string;
}

export interface PeriodicElement {
  name: string;
  position: number;
  weight: number;
  symbol: string;
}

@Component({
    selector: "app-dialog-box",
    templateUrl: "./dialog-box.component.html",
    styleUrls: ["./dialog-box.component.scss"],
    imports: [MatCardModule, NgFor, NgIf],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService,]
})
export class DialogBoxComponent implements OnInit {
  public ELEMENT_DATA: any = [];
  code: any;
  name: any = "";
  territoryCode: any = [];
  terriCode: any = [];

  constructor(
    private territoryService: TerritoriesService,
    private spinner: BaThemeSpinner,
    public dialogRef: MatDialogRef<DialogBoxComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    public dialog: MatDialog,
    private dashboardService : DashboardService,
    private router: Router,
    public toastr: ToastrService,
  ) {}

  ngOnInit() {
    this.spinner.hide();
    // this.code = localStorage.getItem("code");
    // this.name = localStorage.getItem("name");
    
    this.dashboardService._brandName.subscribe(brandDetails=>{
      this.code = brandDetails.code;
      this.name = brandDetails.name;
      this.getdetails();
    })
    
  }
  onCloseDialog(): void {
    this.dialogRef.close(true);
  }

  onNoClick(): void { 
    this.dialogRef.close();
    this.territoryCode = [];
  }
  dataLength!: number;
  getdetails(): void {
    this.territoryCode = [];
    this.territoryCode = localStorage.getItem("slabTerritoryCode");
    this.territoryCode =
      this.territoryCode !== null ? JSON.parse(this.territoryCode) : [];
    const territoryCodeString = localStorage.getItem("slabTerritoryCode");
    this.territoryCode =
      territoryCodeString !== null ? JSON.parse(territoryCodeString) : []; 
    // this.territoryService
    //   .getSlabCountTableList(this.code, { territoryCodes: this.territoryCode })
    //   .subscribe({
    //     next :(data: any) => {
    //     this.dataLength = Object.keys(data).length;
    //     this.ELEMENT_DATA = data; 
    //   },
    //   error : (errorResponse : any)=>{
    //     let errorMsg = errorResponse.status;
    //     if (+errorMsg ===  401 || +errorMsg ===  404) {  
    //       localStorage.clear();
    //       this.router.navigate([""]);
    //       this.toastr.success("Signed Out Successfully");
    //     } else{
    //       this.toastr.error("Error while fetching brand details");
    //     } 
    //   }
    // });
  }
}
