import {async, ComponentFixture, TestBed} from '@angular/core/testing';
import {ForgotPassword} from './forgot-password.component';

describe('ForgotPassword', () => {
    let component: ForgotPassword;
    let fixture: ComponentFixture<ForgotPassword>;

    beforeEach(async(() => {
        TestBed.configureTestingModule({
    imports: [ForgotPassword]
}).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(ForgotPassword);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
