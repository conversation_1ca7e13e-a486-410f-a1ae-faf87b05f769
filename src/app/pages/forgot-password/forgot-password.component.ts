import { Component, ViewEncapsulation } from "@angular/core";
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from "@angular/forms";
import { Router } from "@angular/router";
import {
  BaMenuService,
  BaThemePreloader,
  BaThemeSpinner,
} from "../../theme/services";
import { EmailValidator } from "../../theme/validators/email.validator";
import { AppConstant } from "../../constants/app.constant";
import { NgClass, NgIf } from "@angular/common";

@Component({
    selector: "forgot-password",
    encapsulation: ViewEncapsulation.None,
    templateUrl: "forgot-password.html",
    styleUrls: ["forgot-password.scss"],
    imports: [FormsModule, ReactiveFormsModule, NgClass, NgIf],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class ForgotPassword {
  form: FormGroup;
  loyaltyImage: any = AppConstant.LOYALTY_IMAGE;
  submitted: boolean = false;

  constructor(
    fb: FormBuilder,
    private router: Router,
    private spinner: BaThemeSpinner
  ) {
    this.form = fb.group({
      email: [
        "",
        Validators.compose([Validators.required, EmailValidator.validate]),
      ],
    });
  }
  ngAfterViewInit() {
    this.spinner.hide();
  }

  navigateToHome() {
    this.router.navigate([""]);
  }
  navigateToSignIn() {
    this.router.navigate(["/login"]);
  }
  navigateToResetPassword() {
    this.router.navigate(["/reset-password"]);
  }
}
