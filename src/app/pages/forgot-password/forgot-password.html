<div class="forgot-password-container">
  <div class="row forgot-password-segment">
    <div class="col-md-12 forgot-div-style">
      <div class="sign-up-title forgot-div-style">
        <img [src]="loyaltyImage" (click)="navigateToHome()" />
      </div>
    </div>
    <div class="col-md-12 signup">
      <div class="forgot-password-text">Forgot Password</div>
      <form [formGroup]="form" class="form-horizontal">
        <div
          class="form-group row col-md-12 input-button forgot-div-style"
          [ngClass]="{'has-error': (!form.get('email')?.valid && form.get('email')?.touched), 'has-success': (form.get('email')?.valid && form.get('email')?.touched)}"
        >
          <div class="input-group sign-up-input">
            <span class="input-group-addon" id="basic-addon2"
              ><i class="fa fa-envelope" aria-hidden="true"></i
            ></span>
            <input
              #emailField
              formControlName="email"
              type="email"
              class="form-control inputbox right-border"
              id="inputEmail3"
              placeholder="Email"
              autofocus
              tabindex="1"
            />
          </div>
          <div class="sign-up-error">
            <span
              *ngIf="!form.get('email')?.valid && form.get('email')?.touched"
              class="help-block sub-little-error confpass"
              >Email is {{!form.get('email')?.valid && form.get('email')?.value
              === ''?'required':'not valid'}}.</span
            >
          </div>
        </div>

        <div
          class="form-group row col-md-12 input-button submit-button forgot-div-style"
        >
          <input
            [ngClass]="{'disable-submit' : (!form.valid || submitted) }"
            [disabled]="!form.valid || submitted"
            type="submit"
            class="btn-style signUpButton"
            value="SEND"
            tabindex="2"
          />
        </div>
      </form>
      <div class="col-md-12 already-have-account forgot-div-style">
        <div class="col-md-12 forgot-div-style sign-up-footer">
          <span>Already have an account? </span>
          <a
            (keyup.enter)="navigateToSignIn()"
            (click)="navigateToSignIn()"
            tabindex="3"
            ><span class="forgot-password">Sign In</span></a
          >
          <div class="col-md-12">
            <span>Have code? </span>
            <a
              (keyup.enter)="navigateToResetPassword()"
              (click)="navigateToResetPassword()"
              tabindex="4"
              ><span class="forgot-password">Set your Password</span></a
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
