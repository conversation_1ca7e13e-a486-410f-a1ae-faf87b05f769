@import "../../theme/sass/auth";
@import "../../../styles";

.forgot-password-container {
  text-align: center;
  .forgot-password-segment {
    width: 28%;
    display: inline-block;
    .forgot-div-style {
      text-align: center;
      font-size: 15px;
      font-weight: 500;
      letter-spacing: 0.005em;
    }
    .sign-up-title {
      width: 100%;
      display: inline-block;
      margin-top: 15%;
      img {
        cursor: pointer;
        width: 150px;
      }
    }
    .signup {
      border: 1px solid gainsboro;
      background-color: $white-bg;
      border-radius: $border-radius;
      margin-top: 10px;
      cursor: text;
      .forgot-div-style {
        text-align: center;
        font-size: 15px;
        margin-top: 6%;
        margin-bottom: 16px;
        font-weight: 500;
        letter-spacing: 0.005em;
        cursor: text;
        .sign-up-input {
          height: 55px;
        }
      }
      .right-border {
        border-bottom-right-radius: $border-radius;
        border-top-right-radius: $border-radius;
      }
      .input-group {
        .input-group-addon {
          border-bottom-left-radius: $border-radius;
          border-top-left-radius: $border-radius;
        }
      }
      .submit-button {
        margin-top: 5.1%;
        cursor: pointer;
      }
      .forgot-password-text {
        width: 80%;
        margin: 0 auto;
        margin-top: 7%;
        font-size: 15px;
      }
      .disable-submit {
        opacity: 0.8;
        cursor: not-allowed;
        &:hover {
          opacity: 0.8;
          cursor: not-allowed;
        }
      }
      .help-block {
        color: red;
      }
      .already-have-account {
        margin-bottom: 5%;
      }
    }
    .sign-up-error {
      float: left;
      margin-top: -25px;
      width: 100%;
    }
    .sub-little-error {
      font-size: 12px;
    }
    a {
      color: $signin-link-color;
    }
    .forgot-password {
      cursor: pointer;
      font-size: 15px;
      color: $signin-link-color;
      &:hover {
        text-decoration: underline;
      }
    }
    .sign-up-footer {
      line-height: initial;
    }
  }
}

@media screen and (min-width: 200px) and (max-width: 700px) {
  .forgot-password-segment {
    width: 100%;
  }
  .forgot-password-text {
    display: inline-block;
    width: 27%;
    padding: 0;
  }
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
  .forgot-password-segment {
    width: 60% !important;
  }
  .forgot-password-text {
    display: inline-block;
    width: 27%;
  }
}

@media screen and (min-width: 200px) and (max-width: 700px) {
  .forgot-password-segment {
    width: 100% !important;
  }
  .forgot-password-text {
    display: inline-block;
    width: 27%;
    padding: 0;
  }
}
