import { ComponentFixture, TestBed } from "@angular/core/testing";
import { HelpComponent } from "./help.component";

//     let component: HelpComponent;
//     let fixture: ComponentFixture<HelpComponent>;

//     beforeEach(async(() => {
//         TestBed.configureTestingModule({
//     imports: [HelpComponent]
// })
//             .compileComponents();
//     }));

//     beforeEach(() => {
//         fixture = TestBed.createComponent(HelpComponent);
//         component = fixture.componentInstance;
//         fixture.detectChanges();
//     });

//     it('should create', () => {
//         expect(component).toBeTruthy();
//     });
// });
describe("HelpComponent", () => {
  let component: HelpComponent;
  let fixture: ComponentFixture<HelpComponent>;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [HelpComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HelpComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
