import { Component, ViewEncapsulation } from "@angular/core";
import { BaThemeSpinner } from "../../theme/services/baThemeSpinner";
import { RouterLink } from "@angular/router";

@Component({
    selector: "not-found",
    encapsulation: ViewEncapsulation.None,
    templateUrl: "not-found.html",
    styleUrls: ["not-found.scss"],
    imports: [RouterLink]
})
export class NotFoundComponent {
  constructor(private spinner: BaThemeSpinner) {
    this.spinner.hide();
  }
}
