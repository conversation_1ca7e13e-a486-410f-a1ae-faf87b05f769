<div class="notification-container">
  <div class="notification-grid-container">
    <div class="notification-data-parent-container">
      <div id="foo" class="notification-data-container">
        <div class="state-multi-select-container">
          <div class="label-text">Region <i class="required">&nbsp;*</i></div>
          <angular2-multiselect
            class="dropdown"
            [data]="stateDetails"
            [(ngModel)]="stateDate"
            [settings]="stateDropdownSettings"
            (onSelect)="selectedState($event)"
            (onDeSelect)="deSelectedState($event)"
            (onSelectAll)="selectedAllState($event)"
            (onDeSelectAll)="deSelectedAllState($event)"
          >
          </angular2-multiselect>
        </div>
        <div class="notification-text">
          <div class="label-text">
            Notification Title <i class="required">&nbsp;*</i>
          </div>
          <textarea
            minlength="5"
            maxlength="255"
            placeholder="Enter Notification Title"
            class="text-input"
            [(ngModel)]="notificationTitle"
            (keypress)="descriptionValidation($event)"
          ></textarea>
          <mat-hint> {{ notificationTitle.length }}/255</mat-hint>
        </div>
        <div class="notification-text">
          <div class="label-text">
            Notification Text <i class="required">&nbsp;*</i>
          </div>
          <textarea
            minlength="5"
            maxlength="255"
            placeholder="Enter Notification Text"
            class="text-input"
            [(ngModel)]="notificationText"
            (keypress)="descriptionValidation($event)"
          ></textarea>
          <mat-hint> {{ notificationText.length }}/255</mat-hint>
        </div>

        <div class="button-container">
          <button
            type="button"
            (click)="submitNotificationForm()"
            class="send-btn button-style"
            [disabled]="
              !(
                notificationTitle !== '' &&
                notificationText !== '' &&
                selectedStateCodes.length
              )
            "
          >
            Send
          </button>
        </div>
      </div>
      <div class="priview-image">
        <div class="imageBackground">
          <div class="notificationParentContainer"> 
            <div [innerHTML]="notificationText"></div>
          </div>      
        </div> 
      </div>
    </div>
  </div>
</div>
