@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

$font-size: 13px;

.notification-container {
  width: 100%;
  margin-left: 14px;
  overflow-y: hidden;
  .notification-grid-container {
    width: 98%;
    float: left;
    border-radius: $border-radius;
    margin-top: 120px;
    position: relative;
    background: #fff;
    border-radius: 2px;
    padding: 10px 15px 25px;
    .notification-data-parent-container {
      min-height: 485px;
      background: #ffffff;
      overflow-y: auto;
      display: grid;
      grid-template-columns: 50% 50%;

      .priview-image {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 400px;

        .imageBackground {
          background-image: url("../../../assets/img/notificationImage.png");
          background-size: cover;
          background-position: center;
          width: 200px;
          height: 400px;
          margin-top: 25px;
        }
      }
    }
    .notification-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;

      .notification-text {
        width: 100%;
        float: left;
        padding: 10px;
        @media screen and (max-width: 768px) {
          width: 100%;
          float: left;
        }
      }

      .title-input {
        width: 100%;
        float: left;
        height: 100px;
        border-radius: 5px;
        padding: 5px;
        border: 1px solid #102d69;
      }
      textarea {
        resize: none;
      }
      .text-input {
        width: 100%;
        float: left;
        height: 100px;
        border-radius: 5px;
        padding: 10px;
        border: 1px solid #102d69;
      }
      .text-input::placeholder {
        position: absolute;
        top: 10px;
        left: 10px;
      }
      .state-multi-select-container {
        width: 100%;
        float: left;
        padding: 10px;

        .state-multi-select {
          width: 20%;
          float: left;
          height: 80px;
          overflow: auto;
          padding: 20px 10px 10px 10px;
          border: 2px solid #102d69;
          border-radius: 5px;

          @media screen and (max-width: 768px) {
            width: 100%;
            float: left;
          }

          .state-container {
            display: flex;
            align-items: center;
            margin-bottom: 10px;

            .state-checkbox {
              margin-right: 10px;
            }
            .state-checkbox-label {
              width: 100%;
              margin: 0px !important;
            }
            .selected-checkbox-label {
              color: black;
            }
          }
        }
        .selected-list .countplaceholder {
          position: absolute !important;
          right: 55px !important;
          top: 50% !important;
          transform: translateY(-50%) !important;
        }
      }
      .notification-preview-container {
        width: 100%;
        float: left;
        padding: 10px;
        margin-bottom: 20px;

        .preview-card {
          width: 25%;
          float: left;

          @media screen and (max-width: 768px) {
            width: 100%;
            float: left;
          }

          .preview-section {
            padding: 20px;
            background: #97bcd84d;
            color: #374767;
            border-radius: 5px;

            .preview-lable {
              padding-bottom: 10px;
            }
            .preview-details {
              font-weight: 600;
            }
            .preview-image {
              width: 23px;
              height: 23px;
            }
          }
        }
      }

      .label-text {
        color: #374767;
        font-weight: bold;
        margin-bottom: 5px;
      }
      input[type="checkbox"] {
        width: 16px;
        height: 16px;
        accent-color: black;
      }
    }
    .button-container {
      width: 100%;
      float: left;
      padding: 10px;
      button {
        width: 30%;
        height: 40px;
        float: right;
        background-color: #ff8033;
        color: white;
        font-size: 15px;
        font-weight: bold;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        @media screen and (max-width: 767px) {
          width: 100%;
        }
      }
      .send-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

:host ::ng-deep.cuppa-dropdown {
  position: relative;
  width: 100% !important;
}

:host ::ng-deep .brands-multi-select .cuppa-dropdown .selected-list .c-btn {
  width: 100% !important;
}

:host ::ng-deep .clear-all {
  width: 8px;
  position: absolute;
  top: 50%;
  right: 35px;
  transform: translateY(-50%);
}

.notificationTitleField {
  font-family: sans-serif;
  font-size: 9px;
  font-weight: bold;
  color: #000;
  position: absolute;
  left: 73.4%;
  top: 22%;
  width: 150px;
  background-color: #e9eff7;
  font-size: 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.notificationTextField {
  font-weight: 500;
  color: #265572;
  position: absolute;
  left: 73.4%;
  top: 26%;
  width: 150px;
  background-color: #e9eff7;
  font-size: 10px;
}

.notification-text textarea {
  white-space: pre-line;
}

.notificationParentContainer {
  white-space: pre-line;
  background: #ebf3ff;
  width: 83%;
  height: 100%;
  position: relative;
  font-size: 10px;
  left: 17px;
  top: 49px;
  overflow: auto;
  min-height: 100px;
  max-height: 335px;
  word-wrap: break-word;
  border-radius: 5px;
  padding: 10px 10px;
}
