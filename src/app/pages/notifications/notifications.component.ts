import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { BaThemeSpinner } from '../../theme/services';
import { UserService } from '../../app-services/user-service';
import { GlobalEvents } from '../../helpers/global.events';
import { NotificationService } from '../../app-services/notification-service';
import { AppConstant } from '../../constants/app.constant';
import * as _ from 'lodash';
import { Utility } from 'src/app/shared/utility/utility';

import { CommonModule } from '@angular/common';
// import { NotificationsComponent } from './notifications.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { MatFormFieldModule } from '@angular/material/form-field';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss'],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    AngularMultiSelectModule,
    MatFormFieldModule,
    // routing,
  ],
})
export class NotificationsComponent implements OnInit {
  notificationText: string = '';
  notificationTitle: string = '';
  allStateSelected: any;
  selectedItemsList = [];
  checkedIDs: any = [];
  territoriesDetails: any = [];
  selectedStateCodes: any = [];
  stateList: any = [];
  stateDetails: any = [];
  stateDate: any = [];
  stateDropdownSettings = {
    text: 'Select Region',
    enableSearchFilter: true,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 1,
    autoPosition: false,
  };
  constructor(
    private routes: ActivatedRoute,
    private _spinner: BaThemeSpinner,
    private events: GlobalEvents,
    public userService: UserService,
    public toastr: ToastrService,
    public notificationService: NotificationService,
    private utility: Utility,
    private router: Router
  ) {}

  ngOnInit() {
    window.scrollTo(0, 0);
    this._spinner.hide();
    this.getAllStateNames();
  }

  /**
   * Triggered when a region is selected in multi select dropdown
   * @param event
   */
  selectedState(event: any) {
    if (!_.includes(this.selectedStateCodes, event.code)) {
      this.selectedStateCodes.push(event.code);
    }
  }

  /**
   * Triggered when a region is deselected in multi select dropdown
   * @param event
   */
  deSelectedState(event: any) {
    if (_.includes(this.selectedStateCodes, event.code)) {
      _.remove(this.selectedStateCodes, (item) => item === event.code);
    }
  }

  /**
   * Triggered when all regions are selected in multi select dropdown
   * @param event
   */
  selectedAllState(event: any) {
    if (event && event.length) {
      this.selectedStateCodes = [];
      this.selectedStateCodes = _.map(event, 'code');
    }
  }

  /**
   * Triggered when all regions are deselected in multi select dropdown
   * @param event
   */
  deSelectedAllState(event: any) {
    this.selectedStateCodes = [];
  }

  /**
   *  Called automatically after view init
   */
  ngAfterViewInit() {
    this.events.setChangedContentTopText('Notifications');
  }

  //Checked boxes
  changeSelection() {
    this.fetchSelectedItems();
  }

  allSelection() {
    this.stateList.forEach((value: any, index: any) => {
      value.isChecked = this.allStateSelected;
    });
    this.fetchSelectedItems();
  }

  fetchSelectedItems() {
    this.selectedItemsList = this.stateList.filter((value: any, index: any) => {
      return value.isChecked;
    });
  }

  fetchCheckedIDs() {
    this.checkedIDs = [];
    this.stateList.forEach((value: any, index: any) => {
      if (value.isChecked) {
        this.checkedIDs.push(value.id);
      }
    });
  }

  getAllStateNames() {
    const stateNames = this.userService.getAllStateNames();
    stateNames.subscribe({
      next: (states: any) => {
        let statesData = JSON.parse(this.utility.decrypt(states));
        this.stateDetails = [];
        if (statesData) {
          statesData.forEach((state: any) => {
            let stateObj = {
              id: state.id,
              code: state.code,
              name: state.name,
            };
            this.stateDetails.push(stateObj);
          });
        }
      },
      error: (errorResponse: any) => {
        let error:any =this.utility.decrypt(errorResponse.error)
        error= JSON.parse(error);
        if('Full authentication is required to access this resource'==error.message){
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  checkFormValidation(): boolean {
    return !(
      this.notificationTitle != '' &&
      this.notificationText != '' &&
      this.selectedStateCodes.length
    );
  }

  submitNotificationForm(): void {
    this._spinner.show();
    const selectedStateIds: any = [];
    this.selectedItemsList.forEach((item: any) => {
      selectedStateIds.push(item.code);
    });
    const requestPayload = {
      title: this.utility.encrypt(this.notificationTitle),
      description: this.utility.encrypt(this.notificationText),
      stateCodes: this.selectedStateCodes,
    };

    this.notificationService.addNotification(requestPayload).subscribe({
      next: (res: any) => {
        this.notificationTitle = '';
        this.notificationText = '';
        this.selectedStateCodes = [];
        this.selectedStateCodes = [];
        this.stateDate = [];
        this.getAllStateNames();
        this.toastr.success(AppConstant.NOTIFICATION_SUCCESS);
        this._spinner.hide();
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error:any =this.utility.decrypt(errorResponse.error)
        error= JSON.parse(error);
        if('Full authentication is required to access this resource'==error.message){
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  descriptionValidation(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }
    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57) ||
      '!@#$%&()-=+:;\'"{}[]<>.,'.indexOf(event.key) !== -1
    ) {
    } else {
      event.preventDefault();
    }

    return true;
  }

  sanitizeInput(input: string): string {
    let sanitizedValue = input.replace(/<[^>]*>/g, '');
    sanitizedValue = sanitizedValue.replace(/[&^*#$!@()%]/g, '');
    return sanitizedValue;
  }

  // Event handler for input field
  onInputChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const inputValue = inputElement.value;
    const sanitizedValue = this.sanitizeInput(inputValue);
    inputElement.value = sanitizedValue;
  }

  // Event handler for paste event
  onPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedText = event.clipboardData!.getData('text/plain');
    const sanitizedValue = this.sanitizeInput(pastedText);
    document.execCommand('insertText', false, sanitizedValue);
  }
}
