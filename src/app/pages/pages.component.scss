@import "../theme/sass/conf/conf";
@import "../theme/components/baSidebar/baSidebar";

::ng-deep {
  @mixin layout-collapsed() {
    .al-main {
      margin-left: 50px;
      background-color: #fff;
    }

    .al-main-TM-50 {
      margin-left: 50px !important;
    }

    .al-footer {
      padding-left: 83px !important;
    }
  }

  @mixin sidebar-collapsed() {
    .al-sidebar {
      width: 52px;

      .fa-angle-down,
      .fa-angle-up {
        opacity: 0;
      }

      .al-sidebar-sublist {
        position: absolute;
        top: -1px;
        left: 52px;
        @include bg-translucent-dark(0.8);
        width: 0;
        display: block;
        overflow: hidden;
        transition: width 0.5s ease;

        &.slide-right {
          width: 135px;
        }

        &::before {
          display: none;
        }

        li {
          &::before {
            display: none;
          }

          a {
            padding-left: 18px;
            padding-right: 18px;
            min-width: 130px;
            white-space: nowrap;
          }
        }
      }

      .sidebar-hover-elem,
      .sidebar-select-elem {
        left: 48px;
      }
    }
  }

  @mixin sidebar-overlap() {
    .al-sidebar {
      width: 210px;
      @include bg-translucent-dark(0.75);
      transition: width 0.5s ease;

      .fa-angle-down,
      .fa-angle-up {
        opacity: 1;
      }

      .al-sidebar-sublist {
        @include default-sublist();
        top: auto;
        left: auto;
        background: none;
        width: auto;
        overflow: visible;
        transition: none;
      }

      .sidebar-hover-elem,
      .sidebar-select-elem {
        left: 210px - 4;
        transition: left 0.5s ease;
      }
    }
  }

  @mixin sidebar-hidden() {
    .al-sidebar {
      width: 0;
    }

    .sidebar-hover-elem,
    .sidebar-select-elem {
      display: none;
    }
  }

  @media (min-width: 1200px) {
    .menu-collapsed {
      @include layout-collapsed();

      // .al-content {
      //   width: calc(100% - 50px);
      // }
    }
  }

  @media (max-width: 1200px) and (min-width: $resXS) {
    @include layout-collapsed();

    // .al-content {
    //   width: calc(100% - 50px);
    // }
  }

  @media (min-width: $resXS) {
    .menu-collapsed {
      @include sidebar-collapsed();
    }
  }

  @media (max-width: 1200px) {
    @include sidebar-overlap();
  }

  @media (max-width: $resXS) {
    .menu-collapsed {
      @include sidebar-hidden();

      // .al-content {
      //   width: calc(100%);
      // }
    }

    .al-main {
      margin-left: 0;
    }

    .al-main-TM-50 {
      margin-left: 0;
    }

    .al-footer {
      padding-left: 0;
    }
  }

  @media (max-width: $resXS) {
    .al-content {
      width: calc(100%);
    }
  }
}

// Layout SCSS

$left-space: $sidebar-width;

@include scrollbars(.5em, #d9d9d9, rgba(0, 0, 0, 0));

html {
  min-width: 320px;
}

html, body {
  min-height: 100%;
  height: 100%;
  min-width: $resMin;
  background-color: $body-bg;
  font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif !important;
}

main {
  min-height: 100%;
  position: relative;
  color: #666666;

  @include body-bg();

  .additional-bg {
    display: none;
    @include additional-bg();
  }
}

@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  html {
    overflow: hidden;
    height: 100%;
  }
  body {
    overflow: auto;
    height: 100%;
  }
}

a {
  transition: color 0.5s ease;
  outline: 0 !important;
}

.body-bg {
  display: none;
}

.al-header {
  display: block;
  height: 49px;
  margin: 0;
  background-repeat: repeat-x;
  position: relative;
  z-index: 905;
  color: #444444;
}

.al-main {
  margin-left: $left-space;
  padding: 52px 0 0 0;
  min-height: 620px;
  position: relative;
  background-color: $body-bg;
}

.al-main-TM {
  margin-left: 0 !important;
}

.al-footer {
  height: 34px;
  padding: 0 18px 0 ($left-space + 20px);
  width: 100%;
  line-height: 35px;
  position: absolute;
  display: block;
  bottom: 0;
  font-size: 13px;
  float: left;
  position: relative;
  background-color: $body-bg;
  color: $default-text;
  transition: padding-left 0.5s ease;
  overflow: hidden;
}

.al-footer-TM {
  padding-left: 15px;
}

.al-footer-main {
  float: left;
  margin-left: 15px;
}

.al-copy {
  float: left;
  color: #374767;
  a{
    color: #374767;
  }
}

.al-footer-right {
  float: right;
  margin-right: 12px;
  i {
    margin: 0 4px;
    color: $danger;
    font-size: 12px;
  }
  a {
    margin-left: 4px;
    color: $default-text;
    &:hover {
      color: $danger;
    }
  }
}

.al-share {
  margin: -6px 0 0 12px;
  padding: 0;
  list-style: none;
  float: left;
  li {
    list-style: none;
    float: left;
    margin-left: 16px;
    i {
      cursor: pointer;
      transition: all 0.1s ease;
      color: white;
      padding: 6px;
      box-sizing: content-box;
      font-size: 16px;
      &:hover {
        transform: scale(1.2);
      }
    }
    i.fa-facebook-square {
      color: $facebook-color;
    }
    i.fa-twitter-square {
      color: $twitter-color;
    }
    i.fa-google-plus-square {
      color: $google-color;
    }
  }
}

.al-content {
  position: fixed;
  width: calc(100%);
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  padding: 8px 27px 8px 12.8px;
  z-index:9
}

.al-content-width-TM {
  width: 100% !important;

}

@media screen and (max-width: $resXS) {
  .al-content {
    padding: 8px 20px;
    z-index: 3;
  }
}

.vis-hidden {
  visibility: hidden;
  position: absolute;
  top: -9999px;
  left: -9999px;
}

.icon-up, .icon-down {
  width: 5px;
  height: 13px;
  display: block;
}

.icon-up {
  background: url('/assets/img/arrow-green-up.svg') no-repeat 0 0;
}

.icon-down {
  background: url('/assets/img/arrow-red-down.svg') no-repeat 0 0;
}

.disable-text-selection {
  -webkit-touch-callout: none;
  user-select: none;
}

.align-right {
  text-align: right
}

.amcharts-chart-div > a {
  font-size: 6px !important;
}

.content-panel {
  padding-left: 22px;
  padding-top: 26px;
}

@media (max-width: 590px) {
  .al-footer-right {
    float: none;
    margin-bottom: 19px;
    margin-right: 0;
  }
  .al-footer {
    height: 76px;
    text-align: center;
  }
  .al-main {
    padding-bottom: 76px;
  }
  .al-footer-main {
    float: none;
    display: inline-block;
  }
}

.full-invisible {
  visibility: hidden !important;
  * {
    visibility: hidden !important;
  }
}

.irs-grid-text {
  color: $default-text;
}

.text-right {
  text-align: right !important;
}

.text-left {
  text-align: left !important;
}

.text-center {
  text-align: center !important;
}

:host ::ng-deep {
  #pagination {
    padding: 0 0;
    display: inline-block;
    margin: 10px 2px;
    border-radius: 25px;
    
    ul {
      margin: 0;
      padding: 0;
      line-height: 1.5;
      
      li {
        height: 35px;
        text-align: center;
        min-width: 35px;
        line-height: 27px;
        border: none;
        border-right: 1px solid #d6d6d6;
        
        &:last-child {
          border-right: none;
        }
        
        a {
          color: #333;
          text-decoration: none;
          
          &:hover {
            color: #FF8033;
          }
        }
      }
      
      .current {
        background-color: #FF8033;
        color: white;
        
        a {
          color: white;
        }
      }
    }
  }
  
  .pagination-direction {
    display: flex;
    justify-content: center;
    margin-top: 15px;
  }
}

// Add these styles to ensure the export icon is visible

  .export-button {
    display: block !important; // Force display
    visibility: visible !important;
    opacity: 1 !important;
    
    button {
      width: 38px;
      height: 38px;
      border-radius: 0.25rem;
      border: 1px solid #FF8033;
      background: #FF8033;
      color: #fff;
      display: flex !important;
      align-items: center;
      justify-content: center;
      
      i {
        font-size: 18px;
        display: inline-block !important;
        visibility: visible !important;
      }
    }
  }

  /* Global fix for angular2-multiselect borders in production */
angular2-multiselect .c-btn {
  border: 1px solid #FF8033 !important;
  border-radius: 4px !important;
  min-height: 36px !important;
  display: flex !important;
  align-items: center !important;
}

/* Fix for selected items */
angular2-multiselect .selected-item {
  background: #FF8033 !important;
  border: 1px solid #FF8033 !important;
}

/* Fix for dropdown list */
angular2-multiselect .dropdown-list {
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) !important;
}

/* Specific fix for category dropdown */
.category angular2-multiselect .c-btn {
  border: 1px solid #FF8033 !important;
  border-radius: 4px !important;
  min-height: 36px !important;
}

.cdk-overlay-connected-position-bounding-box {
  width: 200px !important;
}

// .toast-container {
//   position: fixed !important;
//   z-index: 999999 !important; 
//   pointer-events: auto !important;
//   top: 12px !important;
//   right: 12px !important;
//   padding: 30px;
  
  
//   .toast-success {
//     background-color: #51A351 !important;
//   }
  
//   .toast-error {
//     background-color: #BD362F !important;
//   }
  
//   .toast-info {
//     background-color: #2F96B4 !important;
//   }
  
//   .toast-warning {
//     background-color: #F89406 !important;
//   }
// }


// // Fix toast animations
// .toast-top-right {
//   top: 12px !important;
//   right: 12px !important;
// }

// .toast-top-left {
//   top: 12px !important;
//   left: 12px !important;
// }

// .toast-bottom-right {
//   right: 12px !important;
//   bottom: 12px !important;
// }

// .toast-bottom-left {
//   bottom: 12px !important;
//   left: 12px !important;
// }

// .toast-title {
//   font-weight: bold !important;
//   margin-bottom: 5px !important;
// }

// .toast-message {
//   word-wrap: break-word !important;
// }
