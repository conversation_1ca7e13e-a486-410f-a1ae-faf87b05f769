import { Component } from "@angular/core";
import {
  Routes,
  Router,
  RouterEvent,
  NavigationStart,
  NavigationCancel,
  NavigationError,
  RouterOutlet,
  RouterLink,
  RouterModule,
} from "@angular/router";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

// Angular Material Modules
import { MatSelectModule } from "@angular/material/select";
import { MatDialogModule } from "@angular/material/dialog";
import { MatIconModule } from "@angular/material/icon";
import { MatCardModule } from "@angular/material/card";

// 3rd Party Modules
import { HighchartsChartModule } from "highcharts-angular";
import { AngularMultiSelectModule } from "angular2-multiselect-dropdown";

// Theme Services & Components
import { BaThemeSpinner } from "../theme/services/baThemeSpinner/baThemeSpinner.service";
import { BaThemePreloader } from "../theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "../theme";
import { BaSidebar } from "../theme/components/baSidebar/baSidebar.component";
import { BaPageTop } from "../theme/components/baPageTop/baPageTop.component";
import { BaContentTop } from "../theme/components/baContentTop/baContentTop.component";
import { BaBackTop } from "../theme/components/baBackTop/baBackTop.component";
import { BaMenu, BaMenuItem } from "../theme/components";
import { BaScrollPosition, BaSlimScroll } from "../theme/directives";

// App-specific Components
import { TerritoriesComponent } from "./territories";
import { AllTerritoriesComponent } from "./territories/all-territories/all-territories.component";
import { TerritoryFormComponent } from "./territories/territory-form";
import { DialogBoxComponent } from "./dialog-box/dialog-box.component";
import { BrandFormComponent } from "./brands/brand-form";
import { Dashboard } from "./dashboard/dashboard.component";

// Menu Configurations
import { ADMIN_PAGES_MENU, ZM_PAGES_MENU_AF } from "./pages.menu";
import { TM_PAGES_MENU_AF, RM_PAGES_MENU } from "./pages.menu";
import { SBO_PAGES_MENU } from "./pages.menu";

// Helpers
import { AuthenticationHelper } from "../helpers/authentication";

@Component({
  selector: "pages",
  styleUrls: [
    './pages.component.scss'
  ],
  template: `
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <div (window:resize)="onWindowResize()"></div>
    <div>
      <ba-sidebar></ba-sidebar>
    </div>
    <ba-page-top></ba-page-top>
    <div class="al-main al-main-TM-50">
      <div [ngClass]="{ 'al-content-width-TM': isTM }" class="al-content">
        <ba-content-top></ba-content-top>
      </div>
      <div>
        <router-outlet></router-outlet>
      </div>
    </div>
    <footer [ngClass]="{ 'al-footer-TM': isTM }" class="al-footer clearfix">
      <div class="al-footer-main clearfix">
        <div class="al-copy">
          &copy; <a translate>uplagro.com </a>{{ currentYear }}
        </div>
      </div>
    </footer>
    <ba-back-top [position]="200"></ba-back-top>
  `,
  imports: [
    // Angular Core
    CommonModule,
    RouterModule,
    RouterOutlet,
    FormsModule,
    ReactiveFormsModule,
    // Angular Material
    MatSelectModule,
    MatDialogModule,
    MatIconModule,
    MatCardModule,
    // Third-party
    HighchartsChartModule,
    AngularMultiSelectModule,
    // Theme Components and Directives
    BaSidebar,
    BaPageTop,
    BaContentTop,
    BaBackTop
],
  providers: [BaThemePreloader, BaThemeSpinner, BaMenuService],
  standalone: true,
})
export class Pages {
  currentYear: any;
  isTM: boolean = false;
  ismobileView: boolean = false;

  constructor(
    private menuService: BaMenuService,
    private router: Router,
    private spinner: BaThemeSpinner
  ) {
    this.router.events.subscribe((event: any) => {
      if (event instanceof RouterEvent) {
        this.navigationInterceptor(event);
      }

      // Listen to localStorage changes (e.g., logout in another tab)
      window.addEventListener("storage", (event: StorageEvent) => {
        if (event.key === "token") {
          location.reload();
        }
      });
    });

    this.isTM = parseInt(AuthenticationHelper.getRoleID() || "") === 2;
    this.ismobileView = window.innerWidth <= 1023;
  }

  ngOnInit() {
    const roleID = Number(localStorage.getItem("roleID"));
    const pc = String(localStorage.getItem("profitCenter"));

    switch (roleID) {
      case 1:
        this.menuService.updateMenuByRoutes(<Routes>ADMIN_PAGES_MENU);
        break;
      case 2:
        this.menuService.updateMenuByRoutes(<Routes>TM_PAGES_MENU_AF);
        break;
      case 3:
        this.menuService.updateMenuByRoutes(<Routes>RM_PAGES_MENU);
        break;
      case 4:
        this.menuService.updateMenuByRoutes(<Routes>ZM_PAGES_MENU_AF);
        break;
      case 5:
        this.menuService.updateMenuByRoutes(<Routes>SBO_PAGES_MENU);
        break;
    }

    this.currentYear = new Date().getFullYear();
  }

  navigationInterceptor(event: RouterEvent): void {
    if (event instanceof NavigationStart) {
      this.spinner.show();
    }
    if (event instanceof NavigationCancel || event instanceof NavigationError) {
      this.spinner.hide();
    }
  }

  onWindowResize(): void {
    this.ismobileView = window.innerWidth <= 1023;
  }
}
