::ng-deep {
  clear: both;
  float: left;
  width: 100%;
  height: 100%;
  background: white;
}

.policy-container {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  text-align: justify;
  margin-top: 20px;
  .page-content {
    text-align: left;
    margin-left: 9%;
    margin-right: 9%;
    text-align: justify;
    content-visibility: auto !important;

    .content-heading {
      font-weight: bold;
      text-decoration: underline;
      justify-content: center;
    }

    .policy-points {
      font-weight: bold;
      margin-left: -26px;
    }
    .content-point {
      text-decoration: underline;
      margin-left: -25px;
    }

    li {
      margin-left: -9px !important;
      color: black;
    }

    ol.roman {
      margin-left: -40px;
      list-style-position: inside;

      li {
        padding-left: 15px;
        text-indent: -15px;

        &::before {
          font-weight: bold;
        }
      }

      ol.alpha {
        margin-left: -25px;

        ol.number {
          margin-left: -20px;
        }
      }

      p {
        margin-bottom: 25px;
      }
    }

    .list {
      margin-left: 15px;
    }
    p{
      color: black;
    }
  }
}

.li-content {
  margin-left: -12px !important;
}
.li-class {
  margin-left: -24px !important;
}
@media screen and (max-width: 600px) {
  .policy-container {
    text-align: left;
    margin-top: 20px;
    .page-content {
      text-align: left;
      margin-left: 2%;
      margin-right: 2%;
      text-align: left;
      content-visibility: auto !important;

      .content-heading {
        font-weight: bold;
        text-decoration: underline;
        justify-content: center;
        text-transform: uppercase;
      }
    }
  }
  .li-content {
    margin-left: -25px !important;
  }
  li {
    margin-left: 0px;
  }
  .li-class {
    margin-left: -24px !important;
  }
}
