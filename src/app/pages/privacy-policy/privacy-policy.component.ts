import { Component, OnInit } from "@angular/core";
import { RouterModule } from "@angular/router";
import { BaMenuService, BaThemePreloader, BaThemeSpinner } from "../../theme/services";
import { CommonModule } from "@angular/common";
@Component({
    selector: "app-privacy-policy",
    templateUrl: "./privacy-policy.component.html",
    styleUrls: ["./privacy-policy.component.scss"],
    imports: [CommonModule, RouterModule],
    providers: [BaThemeSpinner]
})
export class PrivacyPolicyComponent implements OnInit {
  constructor(private spinner: BaThemeSpinner) {
    this.spinner.hide();
  }
  ngOnInit(): void {}
}
