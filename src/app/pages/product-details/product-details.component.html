<div (window:resize)="onWindowResizeProductDetails()"></div>
<div class="product-details-container">
  <div class="product-details-grid-container">
    <div class="product-details-grid-action">
      <div
        class="product-details-grid-search-container"
        [ngClass]="{
          'padding-right-dd padding-left-0': !ismobileViewPD,
          'width-100': ismobileViewPD
        }"
      >
        <label class="margin-bottom-0">Financial Year</label>
        <div class="float-left width-100 fy-input-div label-center">
          <select
            id="fy_id"
            class="form-control float-left width-100 fy-input"
            disabled
            [(ngModel)]="fyID"
          >
            <option hidden value="">Select Financial Year</option>
            <option
              *ngFor="let FY of FYData"
              [selected]="FY.show == true"
              value="{{ FY.id }}"
            >
              {{ FY.FY }}
            </option>
          </select>
        </div>
      </div>
      <div
        class="product-details-grid-search-container"
        [ngClass]="{
          'padding-right-dd padding-left-dd': !ismobileViewPD,
          'width-100': ismobileViewPD
        }"
      >
        <label class="margin-bottom-0">Territory</label>
        <div class="float-right width-100 label-center">
          <select
            class="form-control float-left width-100"
            (change)="SelctedTerritoryName($event)"
            [(ngModel)]="territoryCode"
          >
            <option hidden value="">Select Territory</option>
            <option
              *ngFor="let territory of territoriesData"
              value="{{ territory.code }}"
            >
              {{ territory.name }} - {{ territory.code }}
            </option>
          </select>
        </div>
      </div>
      <div
        class="product-details-grid-search-container"
        [ngClass]="{
          'padding-right-dd padding-left-dd': !ismobileViewPD,
          'width-100': ismobileViewPD
        }"
      >
        <label class="margin-bottom-0">Customer</label>
        <div class="float-left width-100 label-center">
          <div class="float-left width-100">
            <angular2-multiselect
              [data]="customerListPD"
              [(ngModel)]="selectedCustomerPD"
              [settings]="custDropdownPDSettings"
              (onSelect)="customerSelectionPD($event)"
              (onDeSelect)="customerDeselectionPD()"
            >
            </angular2-multiselect>
          </div>
        </div>
      </div>
      <div
        class="product-details-grid-search-container"
        [ngClass]="{
          'padding-left-dd padding-right-0': !ismobileViewPD,
          'width-100': ismobileViewPD
        }"
      >
        <label class="margin-bottom-0">Brand</label>
        <div class="float-right width-100 label-center">
          <div class="float-left width-100">
            <angular2-multiselect
              [data]="brandsListPD"
              [(ngModel)]="selectedbrandsPD"
              [settings]="brandsDropdownPDSettings"
              (onSelect)="brandSelectionPD($event)"
              (onDeSelect)="brandDeselectionPD()"
            >
            </angular2-multiselect>
          </div>
        </div>
      </div>

      <div
        [ngClass]="{
          'padding-left-0 padding-right-0 width-100': ismobileViewPD
        }"
        class="product-details-grid-action-container"
      >
        <div class="product-details-grid-action-add">
          <button
            [ngClass]="{
              'disable-search': isNotTerritory || isNotCustomer || isNotBrand
            }"
            class="add"
            type="button"
            [disabled]="isNotTerritory || isNotCustomer || isNotBrand"
            (click)="prevSalesSKUData()"
          >
            <i class="fa fa-search"></i>&nbsp;Search New
          </button>
        </div>
      </div>
    </div>

    <div class="product-details-grid-action">
      <div class="float-left width-100 overflow-auto">
        <table class="table table-striped table-list table-border">
          <thead>
            <tr class="font-size-table-header">
              <th class="min-width-data-col">Data</th>
              <th class="min-width-unit-col">Unit</th>
              <th class="table-width-custom">Total</th>
              <th class="table-width-custom">Apr</th>
              <th class="table-width-custom">May</th>
              <th class="table-width-custom">Jun</th>
              <th class="table-width-custom">Jul</th>
              <th class="table-width-custom">Aug</th>
              <th class="table-width-custom">Sep</th>
              <th class="table-width-custom">Oct</th>
              <th class="table-width-custom">Nov</th>
              <th class="table-width-custom">Dec</th>
              <th class="table-width-custom">Jan</th>
              <th class="table-width-custom">Feb</th>
              <th class="table-width-custom">Mar</th>
            </tr>
          </thead>
          <tbody>
            <tr class="prev-year-sales-table-background">
              <th rowspan="2" class="prev-year-tm-budget-sales-vol">
                <div>Previous Year</div>
                <div>[{{ prevYear }}]</div>
              </th>
              <th>
                <div>Selected Customer - Total Sale Value</div>
                <div>(₹ in Lakhs)</div>
              </th>
              <ng-container *ngIf="isPrevSales">
                <td
                  class="overflow-property-table"
                  *ngFor="let prevYear of prevYearData"
                  title="{{ getFixedValueSale(prevYear.sale) }}"
                >
                  <span>{{ getFixedValueSale(prevYear.sale) }}</span>
                </td>
              </ng-container>
              <ng-container *ngIf="!isPrevSales">
                <td *ngFor="let defaultSales of defaultArr">
                  <span>{{ defaultSales.sale }}</span>
                </td>
              </ng-container>
            </tr>
            <tr class="prev-year-sales-table-background">
              <th>
                <div>Selected Brand - Total Territory Volume</div>
                <div>(KG or L)</div>
              </th>
              <ng-container *ngIf="isBrandVol">
                <td
                  class="overflow-property-table"
                  *ngFor="let prevYear of prevYearData"
                  title="{{ getFixedValue(prevYear.vol) }}"
                >
                  <span>{{ getFixedValue(prevYear.vol) }}</span>
                </td>
              </ng-container>
              <ng-container *ngIf="!isBrandVol">
                <td *ngFor="let defaultSales of defaultArr">
                  <span>{{ defaultSales.vol }}</span>
                </td>
              </ng-container>
            </tr>

            <tr class="budget-guidance-table-background">
              <th class="prev-year-tm-budget-sales-vol">
                <div>Budget Guidance</div>
                <div>[{{ reffBuddgetYear }}]</div>
              </th>
              <th>
                <div>Total Territory Budget Value - Guidance</div>
                <div>(₹ in Lakhs)</div>
              </th>
              <ng-container *ngIf="isBudgetGuidanceData">
                <td
                  class="overflow-property-table"
                  *ngFor="let budgetGuidance of budgetGuidanceData"
                  title="{{ getFixedValueSale(budgetGuidance.sale) }}"
                >
                  <span>{{ getFixedValueSale(budgetGuidance.sale) }}</span>
                </td>
              </ng-container>
              <ng-container *ngIf="!isBudgetGuidanceData">
                <td *ngFor="let defaultSales of defaultArr">
                  <span>{{ defaultSales.sale }}</span>
                </td>
              </ng-container>
            </tr>

            <tr>
              <th rowspan="3" class="prev-year-tm-budget-sales-vol">
                <span>TM Budget</span>
              </th>
              <th>
                <div>Selected Customer - Total Sale Value</div>
                <div>(₹ in Lakhs)</div>
              </th>
              <ng-container *ngIf="isTMBudgetCustSale">
                <td
                  class="overflow-property-table"
                  *ngFor="let tmBudget of tmBudgetData"
                  title="{{ getFixedValueSale(tmBudget.sale) }}"
                >
                  <span>{{ getFixedValueSale(tmBudget.sale) }}</span>
                </td>
              </ng-container>
              <ng-container *ngIf="!isTMBudgetCustSale">
                <td *ngFor="let defaultSales of defaultArr; let i = index">
                  <span *ngIf="!isEditable">{{ defaultSales.sale }}</span>
                </td>
              </ng-container>
            </tr>
            <tr>
              <th>
                <div>Selected Brand - Total Territory Volume</div>
                <div>(KG or L)</div>
              </th>
              <ng-container *ngIf="isTMBudgetVolSale">
                <td
                  class="overflow-property-table"
                  *ngFor="let tmBudget of tmBudgetData; let i = index"
                  title="{{ getFixedValue(tmBudget.vol) }}"
                >
                  <span>{{ getFixedValue(tmBudget.vol) }}</span>
                </td>
              </ng-container>
              <ng-container *ngIf="!isTMBudgetVolSale">
                <td *ngFor="let defaultSales of defaultArr; let i = index">
                  <span *ngIf="!isEditable">{{ defaultSales.vol }}</span>
                </td>
              </ng-container>
            </tr>
            <tr>
              <th>
                <div>Total Territory Budget Value (₹ in Lakhs)</div>
              </th>
              <ng-container *ngIf="isTMBudgetData">
                <td
                  class="overflow-property-table"
                  *ngFor="let tmBudget of tmBudgetData; let i = index"
                  title="{{ getFixedValueSale(tmBudget.saleTotalTerritory) }}"
                >
                  <span>{{
                    getFixedValueSale(tmBudget.saleTotalTerritory)
                  }}</span>
                </td>
              </ng-container>
              <ng-container *ngIf="!isTMBudgetData">
                <td *ngFor="let defaultSales of defaultArr; let i = index">
                  <span *ngIf="!isEditable">{{ defaultSales.sale }}</span>
                </td>
              </ng-container>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="product-details-grid-action">
      <div class="float-left width-100 overflow-auto">
        <table class="table table-striped table-list table-border">
          <thead>
            <tr class="font-size-table-header">
              <th class="table-width-custom-cust-brand-vol min-width-145"></th>
              <th class="table-width-custom-cust-brand-vol"></th>
              <th class="table-width-custom-cust-brand-vol">Total</th>
              <th class="table-width-custom-cust-brand-vol">Apr</th>
              <th class="table-width-custom-cust-brand-vol">May</th>
              <th class="table-width-custom-cust-brand-vol">Jun</th>
              <th class="table-width-custom-cust-brand-vol">Jul</th>
              <th class="table-width-custom-cust-brand-vol">Aug</th>
              <th class="table-width-custom-cust-brand-vol">Sep</th>
              <th class="table-width-custom-cust-brand-vol">Oct</th>
              <th class="table-width-custom-cust-brand-vol">Nov</th>
              <th class="table-width-custom-cust-brand-vol">Dec</th>
              <th class="table-width-custom-cust-brand-vol">Jan</th>
              <th class="table-width-custom-cust-brand-vol">Feb</th>
              <th class="table-width-custom-cust-brand-vol">Mar</th>
              <th
                class="table-width-custom-cust-brand-vol"
                title="Previous Year Sales Volume"
              >
                Previous Year Sales Volume
              </th>
              <th
                class="table-width-custom-cust-brand-vol"
                title="Budget Guidance Volume"
              >
                Budget Guidance Volume
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <th class="prev-year-tm-budget-sales-vol">
                Selected Customer + Selected Brand - Volumes (KG or L)
              </th>
              <th class="vertical-middle">
                <i
                  *ngIf="!isEditable"
                  class="fa fa-pencil-square-o pencil-square-icon"
                  title="Edit"
                  (click)="editTMBudget('edit')"
                ></i>
                <i
                  *ngIf="isEditable"
                  class="fa fa-close float-left pencil-square-icon cancel-icon left-15"
                  title="Cancel"
                  (click)="editTMBudget('cancel')"
                ></i>
                <i
                  *ngIf="isEditable"
                  class="fa fa-check-square-o float-left pencil-square-icon update-color left-25"
                  title="Update"
                  (click)="editTMBudget('update')"
                ></i>
              </th>
              <td
                *ngFor="let brandVol of brandVolumeData; let i = index"
                class="overflow-property-table"
              >
                <span *ngIf="!isEditable">{{
                  getFixedValue(brandVol.total)
                }}</span>
                <input
                  *ngIf="isEditable"
                  type="text"
                  class="form-control padding-5"
                  value="{{ getFixedValue(brandVol.total) }}"
                  (blur)="addSaleVol('total', i)"
                  (input)="changeInputValue($event, 'total', i)"
                  (keypress)="utilityHelper.validateNumber($event)"
                />

                (input)="changeInputValue(($event.target as
                HTMLInputElement).value, 'total', i)"
                (keypress)="utilityHelper.validateNumber($event)">
              </td>
              <td class="overflow-property-table">0</td>
              <td class="overflow-property-table">0</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="product-details-grid-action">
      <div class="float-left width-100 overflow-auto">
        <table class="table table-striped table-list table-border">
          <thead class="table-head">
            <tr class="font-size-table-header">
              <th
                class="border-row head-row vertical-middle min-width-230 text-left"
              >
                SKU Code - Description - UOM
              </th>
              <th class="border-row head-row sku-table-header">
                TM Budget Volume
              </th>
              <th class="border-row head-row sku-table-header">Apr</th>
              <th class="border-row head-row sku-table-header">May</th>
              <th class="border-row head-row sku-table-header">Jun</th>
              <th class="border-row head-row sku-table-header">Jul</th>
              <th class="sku-table-header">Aug</th>
              <th class="sku-table-header">Sep</th>
              <th class="sku-table-header">Oct</th>
              <th class="sku-table-header">Nov</th>
              <th class="sku-table-header">Dec</th>
              <th class="sku-table-header">Jan</th>
              <th class="sku-table-header">Feb</th>
              <th class="sku-table-header">Mar</th>
              <th class="sku-table-header" title="Previous Year Sales Volume">
                Previous Year Sales Volume
              </th>
              <th class="sku-table-header" title="Budget Guidance Volume">
                Budget Guidance Volume
              </th>
            </tr>
          </thead>
          <tbody class="sku-body">
            <ng-container *ngIf="isSKUData">
              <tr *ngFor="let productData of productDetailsData; let i = index">
                <td
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                  "
                  class="text-left"
                >
                  <span
                    [attr.title]="productData.sku_code_desc_uom"
                    style="flex: 1"
                    >{{ productData.sku_code_desc_uom }}</span
                  >
                  <span
                    style="width: 40px; display: flex; justify-content: center"
                  >
                    <i
                      *ngIf="!isSKUDataEditable[i]"
                      class="fa fa-pencil-square-o float-right pencil-square-icon"
                      title="Edit"
                      (click)="editSKUData('edit', i)"
                    ></i>
                    <i
                      *ngIf="isSKUDataEditable[i]"
                      style="padding: 0 3px"
                      class="fa fa-close float-right pencil-square-icon cancel-icon"
                      title="Cancel"
                      (click)="editSKUData('cancel', i)"
                    ></i>
                    <i
                      *ngIf="isSKUDataEditable[i]"
                      style="padding: 0 3px"
                      class="fa fa-check-square-o float-right pencil-square-icon update-color"
                      title="Update"
                      (click)="updateSKUDataSales(productData.counter)"
                    ></i>
                  </span>
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.tm_budget_volume)"
                    >{{ getFixedValue(productData.tm_budget_volume) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'tm_budget_volume'
                      )
                    "
                    value="{{ getFixedValue(productData.tm_budget_volume) }}"
                    (blur)="updateSKUData(i, 'tm_budget_volume')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.apr)"
                    >{{ getFixedValue(productData.apr) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.apr) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'apr'
                      )
                    "
                    (blur)="updateSKUData(i, 'apr')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.may)"
                    >{{ getFixedValue(productData.may) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.may) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'may'
                      )
                    "
                    (blur)="updateSKUData(i, 'may')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.jun)"
                    >{{ getFixedValue(productData.jun) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.jun) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'jun'
                      )
                    "
                    (blur)="updateSKUData(i, 'jun')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.jul)"
                    >{{ getFixedValue(productData.jul) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.jul) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'jul'
                      )
                    "
                    (blur)="updateSKUData(i, 'jul')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.aug)"
                    >{{ getFixedValue(productData.aug) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.aug) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'aug'
                      )
                    "
                    (blur)="updateSKUData(i, 'aug')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.sept)"
                    >{{ getFixedValue(productData.sept) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.sept) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'sept'
                      )
                    "
                    (blur)="updateSKUData(i, 'sept')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.oct)"
                    >{{ getFixedValue(productData.oct) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.oct) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'oct'
                      )
                    "
                    (blur)="updateSKUData(i, 'oct')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.nov)"
                    >{{ getFixedValue(productData.nov) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.nov) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'nov'
                      )
                    "
                    (blur)="updateSKUData(i, 'nov')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.dec)"
                    >{{ getFixedValue(productData.dec) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.dec) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'dec'
                      )
                    "
                    (blur)="updateSKUData(i, 'dec')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.jan)"
                    >{{ getFixedValue(productData.jan) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.jan) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'jan'
                      )
                    "
                    (blur)="updateSKUData(i, 'jan')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.feb)"
                    >{{ getFixedValue(productData.feb) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.feb) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'feb'
                      )
                    "
                    (blur)="updateSKUData(i, 'feb')"
                  />
                </td>
                <td class="overflow-property-table">
                  <span
                    *ngIf="!isSKUDataEditable[i]"
                    [attr.title]="getFixedValue(productData.mar)"
                    >{{ getFixedValue(productData.mar) }}</span
                  >
                  <input
                    *ngIf="isSKUDataEditable[i]"
                    type="text"
                    class="form-control padding-5"
                    value="{{ getFixedValue(productData.mar) }}"
                    (input)="
                      changeInputValueSKUData(
                        $event,
                        productData.counter,
                        i,
                        'mar'
                      )
                    "
                    (blur)="updateSKUData(i, 'mar')"
                  />
                </td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
              </tr>
            </ng-container>
            <ng-container *ngIf="!isSKUData">
              <tr>
                <td class="overflow-property-table text-center">-</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
                <td class="overflow-property-table">0</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
</div>
<ng-container #warningModalPopup>
  <div class="previewModal">
    <div class="modal-header">
      <div>
        <h4 class="warning-popup text-center">Warning</h4>
      </div>
    </div>
    <div class="confirmButtonsContainer confirmationModalPopUp">
      <div class="text-center">The value of the total is 0.</div>
      <div class="text-center">You need to update the <b>SKU</b> first.</div>
    </div>
  </div>
</ng-container>
