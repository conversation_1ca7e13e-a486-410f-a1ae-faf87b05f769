@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

$font-size: 13px;

.product-details-container {
  width: 96%;
  margin: 70px 2.5% 15px 2.5%;
  .product-details-grid-container {
    width: 99%;
    float: left;
    border-radius: $border-radius;
    margin-top: 5px;
    position: relative;
    .width-50 {
      width: 50%;
    }
    .width-100 {
      width: 100% !important;
    }
    .width-25 {
      width: 25%;
    }
    .margin-bottom-10 {
      margin-bottom: 10px;
    }
    .margin-bottom-0 {
      margin-bottom: 0;
    }
    .padding-5 {
      padding: 5px;
    }
    .padding-top-10 {
      padding-top: 10px;
    }
    .padding-right-0 {
      padding-right: 0 !important;
    }
    .padding-left-dd {
      padding-left: 5px !important;
    }
    .padding-right-dd {
      padding-right: 5px !important;
    }
    .padding-left-0 {
      padding-left: 0 !important;
    }
    .font-size-table-header {
      font-size: 13px;
    }
    .product-details-grid-action {
      float: left;
      padding: 10px;
      width: 100%;
      position: relative;
      margin-bottom: 10px;
      overflow-y: visible;
      background: #fff;
      border-radius: $border-radius;
      box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.1);
      display: block;
      .overflow-auto {
        overflow: auto;
      }
      .product-details-grid-search-container {
        width: 20%;
        float: left;
        padding: 1% 0;
        .product-details-grid-search-input {
          @include gridSearchInput();
          width: 44%;
          .input-fields-product-details {
            padding: 0.5rem 0.75rem;
            font-size: 14px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            line-height: 1.25;
          }
        }
        @include inputRightBorder($border-radius);
        .input-group {
          margin-bottom: 0;
          @include inputAddOnBorder($border-radius);
        }
        @include placeholderColor(#666, 0.7);
        .fy-input-div ::-webkit-input-placeholder {
          /* Chrome/Opera/Safari */
          color: rgba(102, 102, 102, 0.7);
        }
        .fy-input-div ::-moz-placeholder {
          /* Firefox 19+ */
          color: rgba(102, 102, 102, 0.7);
        }
        .fy-input-div :-ms-input-placeholder {
          /* IE 10+ */
          color: rgba(102, 102, 102, 0.7);
        }
        .fy-input-div :-moz-placeholder {
          /* Firefox 18- */
          color: rgba(102, 102, 102, 0.7);
        }
        .fy-input {
          height: 38px;
          color: #666666;
          &:disabled {
            border-color: rgba(0, 0, 0, 0.15);
          }
        }
        .ngx-dropdown-container .ngx-dropdown-list-container {
          z-index: 4;
          box-shadow: none;
          position: relative;
          top: 2px;
          ul.selected-items li {
            background-color: #a22e2a !important;
          }
          ul li:hover {
            background-color: #a22e2a !important;
            color: #ffffff !important;
          }
        }
        .ngx-dropdown-container {
          height: 38px;
          border: 1px solid rgba(0, 0, 0, 0.15);
          border-radius: 5px;
          .ngx-dropdown-button {
            border: none;
            height: 36px;
            &:focus {
              outline: none;
            }
          }
          button {
            span {
              color: #464a4c;
            }
          }
          &:focus {
            outline: none;
          }
        }
        .label-center {
          display: flex;
          align-items: center;
        }
        .customer-dd-PD {
          .dropdown-list {
            z-index: 1;
            .list-area {
              width: 300px;
            }
            .pure-checkbox input[type="checkbox"] + label {
              font-size: 12px;
            }
            .pure-checkbox input[type="checkbox"] + label:before {
              width: 10px;
              height: 10px;
            }
            .pure-checkbox input[type="checkbox"] + label:after {
              left: 2px;
              height: 2px;
              margin-top: -5px;
            }
            ul {
              li {
                padding: 2px 10px !important;
              }
              li:nth-child(-n + 20) {
                background: hsla(195, 53%, 79%, 0.7);
              }
            }
          }
          .selected-list {
            overflow: hidden;
            .c-btn {
              height: 38px;
              white-space: nowrap;
              span {
                overflow: hidden;
                text-overflow: ellipsis;
                word-break: break-all;
              }
            }
            .c-angle-down {
              width: 8px;
              height: 10px;
              top: 40%;
            }
            .c-angle-up {
              width: 10px;
              height: 10px;
              top: 40%;
            }
          }
        }
      }
      .table-border {
        border: 1px solid #ddd;
        font-size: 11px !important;
        th {
          border: 1px solid #ddd;
          color: #0059b3;
          font-weight: bold;
          td {
            border: 1px solid #ddd;
            vertical-align: middle !important;
          }
        }
        tr {
          border: 1px solid #ddd;
          background: #ffffff;
          td {
            border: 1px solid #ddd;
            text-align: right;
            vertical-align: middle !important;
            color: #0059b3;
          }
        }
        .line-height-10 {
          line-height: 10px;
        }
        .prev-year-tm-budget-sales-vol {
          vertical-align: middle;
        }
        thead {
          tr {
            background: rgba(0, 0, 0, 0.1);
          }
        }
        .pencil-square-icon {
          font-size: 19px;
          cursor: pointer;
          display: block;
          text-align: center;
        }
        .update-color {
          position: relative;
          color: #00cc3a;
        }
        .cancel-icon {
          color: red;
          position: relative;
        }
        .left-15 {
          left: 15px;
        }
        .left-25 {
          left: 25px;
        }
      }
      .product-details-grid-action-container {
        width: 20%;
        float: right;
        padding: 1% 0;
        padding-top: 15px;
        display: block;
        position: relative;
        .product-details-grid-action-add {
          @include addButtonContainer();
          .disable-search {
            opacity: 0.8;
            cursor: not-allowed;
            &:hover {
              opacity: 0.8;
              cursor: not-allowed;
            }
          }
          @media only screen and (max-width: 1023px) {
            width: 100%;
          }
          width: 100%;
          padding: 17px 0 0;
        }
      }
      .table-width-custom {
        min-width: 70px;
      }
      .min-width-data-col {
        min-width: 105px;
        width: 105px;
        max-width: 105px;
      }
      .min-width-unit-col {
        min-width: 193px;
        width: 193px;
        max-width: 193px;
        @media only screen and (max-width: 1280px) {
          min-width: 170px;
          width: 170px;
          max-width: 170px;
        }
      }
      .table-width-custom-cust-brand-vol {
        min-width: 70px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 70px;
      }
      .vertical-middle {
        vertical-align: middle;
      }
      .min-width-145 {
        min-width: 228px;
        width: 228px;
        max-width: 228px;
        @media only screen and (max-width: 1280px) {
          min-width: 205px;
          width: 205px;
          max-width: 215px;
        }
      }
      .overflow-property-table {
        overflow: hidden;
        max-width: 70px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      table {
        font-size: 11px;
        .sku-body {
          tr {
            td {
              padding: 0 10px;
            }
            td:first-child {
              border: 0;
            }
          }
        }
      }
      .prev-year-sales-table-background {
        background: rgba(255, 165, 0, 0.8) !important;
      }
      .budget-guidance-table-background {
        background: rgba(135, 206, 232, 0.8) !important;
      }
      .table-head {
        color: #0059b3;
        font-size: 13px;
        font-weight: bold;
        text-align: center;
        .action-col-width {
          width: 200px;
        }
        .head-row {
          font-weight: bold;
          text-align: left;
          height: 40px;
          line-height: 18px;
        }
        th {
          &:nth-child(2) {
            width: 160px;
          }
        }
        .machine-head-row {
          th {
            &:nth-child(4) {
              width: 150px;
            }
          }
        }
        .table-width-custom {
          min-width: 70px;
        }
        .min-width-230 {
          min-width: 298px;
          width: 298px;
          max-width: 298px;
          @media only screen and (max-width: 1280px) {
            min-width: 285px;
            width: 285px;
            max-width: 285px;
          }
        }
        .bg-color {
          background-color: rgba(0, 0, 0, 0.1);
        }
        .border-row {
          border: 1px solid $table-border-color;
          text-align: center;
          padding: 10px;
        }
        .fixed-min-width {
          min-width: 100px;
        }
        .sku-table-header {
          text-align: left;
          vertical-align: middle;
          min-width: 70px;
          max-width: 70px;
          width: 70px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: inherit;
        }
      }
    }
    .padding-top-bottom-10 {
      padding-top: 10px;
      padding-bottom: 10px;
    }
    .product-details-grid-data-parent-container {
      min-height: 200px;
      background: #ffffff;
      overflow-y: auto;
    }
    .product-details-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;

      .product-details-table {
        font-size: 15px;
        min-width: 100%;
        overflow-y: hidden;
      }
    }
    .back-btn {
      background-color: #FF8033;
      border-radius: 3px;
      color: white;
      font-size: 15px;
      cursor: pointer;
      float: right;
    }
  }
}

@media screen and (min-width: 200px) and (max-width: 575px) {
  .product-details-container {
    .product-details-grid-container {
      .product-details-grid-action {
        .product-details-grid-search-container {
          width: 100%;
          .product-details-grid-search-input {
            width: 100%;
            .input-group-addon {
              padding: 0.2rem 0.5rem;
              margin-bottom: 0;
              font-size: 1rem;
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: 576px) and (max-width: 767px) {
  .product-details-container {
    .product-details-grid-container {
      .product-details-grid-action {
        .product-details-grid-search-container {
          width: 45%;
          .product-details-grid-search-input {
            width: 80%;
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 768px) {
  .product-details-container {
    .product-details-grid-container {
      .product-details-grid-data-parent-container {
      }
      .product-details-grid-action {
      }
    }
  }
}

.confirmUserActiveContainer {
  @include confirmDialogueActiveInActive();
}

.modal-backdrop.fade {
  opacity: 0.6;
}

.confirmUserActiveContainer .fade {
  opacity: 1 !important;
}

.confirmUserActiveContainer {
  .modal-content {
    padding: 0;
  }
  .warning-popup {
    background: #FF8033;
    color: #fff;
    padding: 10px;
  }
  .confirmButtonsContainer {
    height: auto;
    margin-bottom: 20px;
  }
  button.close {
    top: 5px;
    z-index: 1;
    color: #fff;
    opacity: 1;
  }
}

.disable-submit {
  opacity: 0.8;
  cursor: not-allowed;
  &:hover {
    opacity: 0.8;
    cursor: not-allowed;
  }
}
