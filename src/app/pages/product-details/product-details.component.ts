import {
  Component,
  ViewEncapsulation,
  ViewChild,
  TemplateRef,
} from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { GlobalEvents } from "../../helpers/global.events";
import { AuthenticationHelper } from "../../helpers/authentication";
import { UserService } from "../../app-services/user-service";
import { AppConstant } from "../../constants/app.constant";
import { UtilityHelper } from "../../helpers/utility";
import * as _ from "lodash";
import { MatDialog } from "@angular/material/dialog";
import { DropdownSettings } from "angular2-multiselect-dropdown/lib/multiselect.interface";
// import { AngularMultiSelectModule } from "angular2-multiselect-dropdown";
import { FormsModule } from "@angular/forms";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from "@angular/common";
import { map } from "rxjs";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { AngularMultiSelectModule } from "angular2-multiselect-dropdown";

@Component({
    selector: "nga-product-details",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["product-details.component.scss"],
    templateUrl: "product-details.component.html",
    imports: [NgClass, FormsModule, NgFor, NgIf, AngularMultiSelectModule],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class ProductDetailsComponent {
  custDropdownPDSettings!: DropdownSettings;
  utilityHelper: any = UtilityHelper;
  productDetailsData: any = [];
  editProductDetailsData: any = [];
  calcProductDetailsData: any = [];
  tableHead: any = [];
  tableColName: any = [];
  FYData: any = [];
  profitCenter: any;
  territoriesData: any = [];
  customerData: any = [];
  materialMaster: any = [];
  totalSales: number = 0;
  totalVol: number = 0;
  totalGuidanceSales: number = 0;
  tmBudgetSelectedCustSales: number = 0;
  tmBudgetSelectedBrandVol: number = 0;
  totalTerritoryTMBudgetSale: number = 0;
  configurationSettings: any = {
    showPagination: true,
    perPage: 10,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    actionsColumnName: "Actions",
    noDataMessage: "No data found",
  };
  activeData: any;
  territoryCode: any = "";
  isNotTerritory: boolean = true;
  isNotCustomer: boolean = true;
  isNotBrand: boolean = true;
  isPrevSales: boolean = false;
  isBrandVol: boolean = false;
  isBudgetGuidanceData: boolean = false;
  isTMBudgetData: boolean = false;
  isTMBudgetCustSale: boolean = false;
  isTMBudgetVolSale: boolean = false;
  isSKUData: boolean = false;
  isEditable: boolean = false;
  isSKUDataEditable: any = [];
  model: any;
  tmBudgetID: any;
  showErrorBlockSection: boolean = false;
  ismobileViewPD: boolean = false;
  nextYear: number;
  reffBuddgetYear: string;
  prevYear: string;
  customerDetails: any = [];
  brandDetails: any = [];
  fyID: any;
  financialYear!: any;
  custDropdownOptions: any = [];
  brandDropdownOptions: any = [];
  customerListPD: any = [];
  brandsListPD: any = [];
  prevYearData: any = [];
  prevYearVolData: any = [];
  brandVolumeData: any = [];
  defaultArr: any = [
    { name: "total", sale: 0, vol: 0 },
    { name: "apr", sale: 0, vol: 0 },
    { name: "may", sale: 0, vol: 0 },
    { name: "jun", sale: 0, vol: 0 },
    { name: "jul", sale: 0, vol: 0 },
    { name: "aug", sale: 0, vol: 0 },
    { name: "sep", sale: 0, vol: 0 },
    { name: "oct", sale: 0, vol: 0 },
    { name: "nov", sale: 0, vol: 0 },
    { name: "dec", sale: 0, vol: 0 },
    { name: "jan", sale: 0, vol: 0 },
    { name: "feb", sale: 0, vol: 0 },
    { name: "mar", sale: 0, vol: 0 },
  ];
  defaultSaleArr = [
    { name: "apr", sale: 0 },
    { name: "may", sale: 0 },
    { name: "jun", sale: 0 },
    { name: "jul", sale: 0 },
    { name: "aug", sale: 0 },
    { name: "sep", sale: 0 },
    { name: "oct", sale: 0 },
    { name: "nov", sale: 0 },
    { name: "dec", sale: 0 },
    { name: "jan", sale: 0 },
    { name: "feb", sale: 0 },
    { name: "mar", sale: 0 },
  ];
  defaultSaleArrCopy: any = [];
  budgetGuidanceData: any = [];
  tmBudgetData: any = [];
  editBrandVolumeData: any = [];
  editCalcTMBudgetData: any = [];
  selectedCustomerPD: any = [];
  selectedbrandsPD: any = [];
  custDropdownPDSettingsL: any = {
    text: "Select Customer",
    enableSearchFilter: true,
    classes: "myclass customer-dd-PD",
    labelKey: "name_code",
    singleSelection: true,
    enableFilterSelectAll: false,
  };
  brandsDropdownPDSettings = {
    text: "Select Brand",
    enableSearchFilter: true,
    classes: "myclass customer-dd-PD",
    labelKey: "name",
    singleSelection: true,
    enableFilterSelectAll: false,
  };

  @ViewChild("warningModalPopup")
  warningModalPopup!: TemplateRef<any>;

  constructor(
    public dialog: MatDialog,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private events: GlobalEvents,
    public userService: UserService
  ) {
    this.custDropdownPDSettings = {
      singleSelection: true,
      text: "Select",
    };
    window.scrollTo(0, 0);
    this.ismobileViewPD = window.innerWidth <= 1023 ? true : false;
    this.getFinancialYear();
    this.fyID = localStorage.getItem("fy_id");

    this.customerDetails = localStorage.getItem("customer_details");
    if (this.customerDetails) {
      this.selectedCustomerPD.push(JSON.parse(this.customerDetails));
    }

    this.brandDetails = localStorage.getItem("brand_details");
    if (this.brandDetails) {
      this.selectedbrandsPD.push(JSON.parse(this.brandDetails));
    }

    this.territoryCode = localStorage.getItem("territory_code");
    this.defaultSaleArrCopy = JSON.parse(JSON.stringify(this.defaultSaleArr));
    const date = new Date();
    const month = date.getMonth() + 1;
    if (month >= 4 && month <= 12) {
      this.nextYear = date.getFullYear() + 1;
    } else {
      this.nextYear = date.getFullYear();
    }
    this.reffBuddgetYear =
      this.nextYear +
      "-" +
      (parseInt(this.nextYear.toString().slice(2, 4)) + 1);
    this.prevYear =
      this.nextYear - 1 + "-" + parseInt(this.nextYear.toString().slice(2, 4));
    this.profitCenter = AuthenticationHelper.getProfitCenter();
  }
  ngOnInit() {
    this.setTableHeader();
  }
  getFinancialYear(): void {
    let loggedInUserID = AuthenticationHelper.getUserID();
    this.spinner.show();
    let data = {
      params: "byUserId",
      id: loggedInUserID,
    };
    this.userService.getFinancialYear(data).subscribe({
      next: (tmBudgetData: any) => {
        this.FYData = [];
        tmBudgetData.forEach((tmBudgetInfo: any) => {
          let tmBudgetInfoObj = {
            id: tmBudgetInfo.id,
            FY:
              tmBudgetInfo.startDate.slice(0, 4) +
              "-" +
              tmBudgetInfo.endDate.slice(2, 4),
            show: this.isShowFY(
              tmBudgetInfo.startDate.slice(0, 4) +
                "-" +
                tmBudgetInfo.endDate.slice(2, 4)
            ),
          };
          this.FYData.push(tmBudgetInfoObj);
        });
        this.getTerritories(data);
      },
      error: (errorResponse: any) => {
        this.toastr.error(AppConstant.FINANCIAL_YEAR_FETCHING_ERROR);
        this.spinner.hide();
      },
    });
  }

  isShowFY(FY: any): boolean {
    const FYear = FY.slice(0, 4);
    if (FYear == this.nextYear) {
      return true;
    } else {
      return false;
    }
  }
  getTerritories(res: any): void {
    this.userService.getAllTerritory(res).subscribe({
      next: (territoriesData: any) => {
        this.territoriesData = [];
        territoriesData.forEach((territoryInfo: any) => {
          const territoryInfoObj = {
            id: territoryInfo.id,
            name: territoryInfo.name,
            code: territoryInfo.code,
          };
          this.territoriesData.push(territoryInfoObj);
        });

        let data = {
          code: this.territoryCode,
        };

        this.isNotTerritory = false;
        this.getAllCustomers(data);
      },
      error: (errorResponse: any) => {
        this.toastr.error(AppConstant.TERRITORY_FETCHING_ERROR);
        this.spinner.hide();
      },
    });
  }

  getAllCustomers(res: any): void {
    this.userService.getAllCustomers(res).subscribe({
      next: (customersData: any) => {
        this.isNotCustomer = false;
        this.custDropdownOptions = [];
        this.customerListPD = [];
        customersData.forEach((customerInfo: any) => {
          const customerInfoObj = {
            id: customerInfo.id,
            name: customerInfo.name,
            code: customerInfo.code,
            name_code: customerInfo.name + " - " + customerInfo.code,
          };
          this.custDropdownOptions.push(customerInfoObj);
          this.customerListPD = this.custDropdownOptions;
        });
        this.getAllProduct();
      },
      error: () => {
        this.toastr.error(AppConstant.CUSTOMER_FETCHING_ERROR);
        this.spinner.hide();
      },
    });
  }

  getAllProduct(): void {
    let data = {
      profitCenter: AuthenticationHelper.getProfitCenter(),
    };
    this.userService.getBrands().subscribe({
      next: (brandsData: any) => {
        this.brandDropdownOptions = [];
        this.brandsListPD = [];
        brandsData.forEach((brandsInfo: any) => {
          const brandInfoObj = {
            id: brandsInfo.id,
            name: brandsInfo.name,
            code: brandsInfo.code,
          };
          this.brandDropdownOptions.push(brandInfoObj);
          this.brandsListPD = this.brandDropdownOptions;
        });
        this.isNotBrand = false;
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.toastr.error(AppConstant.BRANDS_FETCHING_ERROR);
        this.spinner.hide();
      },
    });
  }

  editTMBudget(val: any): void {
    this.isSKUDataEditable = [];
    switch (val) {
      case "edit":
        this.isEditable = true;
        break;
      case "cancel":
        this.isEditable = false;
        this.editBrandVolumeData = JSON.parse(
          JSON.stringify(this.editCalcTMBudgetData)
        );
        this.brandVolumeData = JSON.parse(
          JSON.stringify(this.editCalcTMBudgetData)
        );
        break;
      case "update":
        this.updateTMBudgetSales();
        break;
    }
    this.productDetailsData = JSON.parse(
      JSON.stringify(this.calcProductDetailsData)
    );
  }
  updateTMBudgetSales(): void {
    if (this.editCalcTMBudgetData[0].total == 0) {
      this.dialog.open(this.warningModalPopup, {
        width: "500px",
        height: "250px",
        disableClose: false,
        panelClass: "confirm-dialog-container",
        data: "",
        hasBackdrop: false,
      });
      return;
    }

    this.spinner.show();
    let arrSKUCodes: any = [];
    this.productDetailsData.forEach((res: any) => {
      const id = res.sku_code_desc_uom.split(" -")[0];
      arrSKUCodes.push(id);
    });
    let data = {
      profitCenterFinanceYearsId: this.fyID,
      customerCode: this.customerDetails["code"],
      territoryCode: this.territoryCode,
      profitCenterCode: this.profitCenter,
      brandCode: this.brandDetails["code"],
    };

    let updateData = {
      ...data,
      skuCode: arrSKUCodes,
      aprVolume: this.editBrandVolumeData[1].total,
      mayVolume: this.editBrandVolumeData[2].total,
      junVolume: this.editBrandVolumeData[3].total,
      julVolume: this.editBrandVolumeData[4].total,
      augVolume: this.editBrandVolumeData[5].total,
      sepVolume: this.editBrandVolumeData[6].total,
      octVolume: this.editBrandVolumeData[7].total,
      novVolume: this.editBrandVolumeData[8].total,
      decVolume: this.editBrandVolumeData[9].total,
      janVolume: this.editBrandVolumeData[10].total,
      febVolume: this.editBrandVolumeData[11].total,
      marVolume: this.editBrandVolumeData[12].total,
    };
    let updateSKUTableObservable = this.userService.SKUTotalUpdate(updateData);

    updateSKUTableObservable.subscribe({
      next: (newdata: any) => {
        this.toastr.success("Information updated successfully");
        this.getPreviousYearSales();
        this.getMaterialDetails();
        this.brandVolume();
        this.isEditable = false;
      },
      error: (errorResponse: any) => {
        this.toastr.error(AppConstant.BRAND_SKU_UPDATE_ERROR);
        this.spinner.hide();
      },
    });
  }

  SelctedTerritoryName(event: any): void {
    this.spinner.show();
    this.selectedCustomerPD = [];
    this.isNotTerritory = false;
    this.territoryCode = event.target.value;
    const data = {
      code: this.territoryCode,
    };
    localStorage.setItem("territory_code", data.code);
    localStorage.setItem("fy_id", this.fyID);
    this.getAllCustomers(data);
  }

  customerSelectionPD(event: any): void {
    this.spinner.show();
    this.isNotCustomer = false;
    localStorage.setItem("customer_details", JSON.stringify(event));
    this.customerDetails = JSON.parse(JSON.stringify(event));
    this.spinner.hide();
  }

  customerDeselectionPD(): void {
    this.spinner.show();
    this.isNotCustomer = true;
    localStorage.removeItem("customer_details");
    this.spinner.hide();
  }

  brandSelectionPD(event: any): void {
    this.spinner.show();
    this.isNotBrand = false;
    localStorage.setItem("brand_details", JSON.stringify(event));
    this.brandDetails = JSON.parse(JSON.stringify(event));
    this.spinner.hide();
  }

  brandDeselectionPD(): void {
    this.spinner.show();
    this.isNotBrand = true;
    localStorage.removeItem("brand_details");
    this.spinner.hide();
  }

  getPreviousYearSales(): void {
    this.spinner.show();
    let data = {
      profitCenterFinanceYearsId: this.fyID,
      customerCode: this.customerDetails["code"],
      territoryCode: this.territoryCode,
      profitCenterCode: this.profitCenter,
      brandCode: encodeURI(this.brandDetails["code"]),
    };
    this.userService.getAllPreviousYearSales(data).subscribe(
      (data: any) => {
        this.isPrevSales = false;
        this.isBrandVol = false;
        this.isBudgetGuidanceData = false;
        this.isTMBudgetData = false;
        this.isTMBudgetCustSale = false;
        this.isTMBudgetVolSale = false;
        this.prevYearData = [];
        this.prevYearVolData = [];
        this.tmBudgetData = [];
        this.budgetGuidanceData = [];
        this.defaultSaleArr = [];
        this.defaultSaleArr = JSON.parse(
          JSON.stringify(this.defaultSaleArrCopy)
        );
        if (
          data &&
          data.customerTotalSale &&
          data.customerTotalSale.length !== 0
        ) {
          this.isPrevSales = true;
          this.prevYearData = [
            { name: "apr", sale: data.customerTotalSale[0].aprSale / 100000 },
            { name: "may", sale: data.customerTotalSale[0].maySale / 100000 },
            { name: "jun", sale: data.customerTotalSale[0].junSale / 100000 },
            { name: "jul", sale: data.customerTotalSale[0].julSale / 100000 },
            { name: "aug", sale: data.customerTotalSale[0].augSale / 100000 },
            { name: "sep", sale: data.customerTotalSale[0].sepSale / 100000 },
            { name: "oct", sale: data.customerTotalSale[0].octSale / 100000 },
            { name: "nov", sale: data.customerTotalSale[0].novSale / 100000 },
            { name: "dec", sale: data.customerTotalSale[0].decSale / 100000 },
            { name: "jan", sale: data.customerTotalSale[0].janSale / 100000 },
            { name: "feb", sale: data.customerTotalSale[0].febSale / 100000 },
            { name: "mar", sale: data.customerTotalSale[0].marSale / 100000 },
          ];
        } else {
          this.prevYearData = this.defaultSaleArr;
        }
        if (data && data.brandTotalSale && data.brandTotalSale.length !== 0) {
          this.isBrandVol = true;
          _.each(this.prevYearData, (res) => {
            _.each(data.brandTotalSale, (volD) => {
              _.extend(res, { vol: volD[res.name + "Volume"] });
            });
          });
        }
        this.totalSales = 0;
        this.totalVol = 0;
        this.prevYearData.forEach((res: any) => {
          this.totalSalesFunc(res.sale, res.vol);
        });
        const salesVoldata = {
          name: "total",
          sale: this.totalSales,
          vol: this.totalVol,
        };
        this.prevYearData.unshift(salesVoldata);
        if (
          data &&
          data.territoryManagerBudgetGuidance &&
          data.territoryManagerBudgetGuidance.length !== 0
        ) {
          this.isBudgetGuidanceData = true;
          this.budgetGuidanceData = [
            {
              name: "apr",
              sale: data.territoryManagerBudgetGuidance[0].aprSale / 100000,
            },
            {
              name: "may",
              sale: data.territoryManagerBudgetGuidance[0].maySale / 100000,
            },
            {
              name: "jun",
              sale: data.territoryManagerBudgetGuidance[0].junSale / 100000,
            },
            {
              name: "jul",
              sale: data.territoryManagerBudgetGuidance[0].julSale / 100000,
            },
            {
              name: "aug",
              sale: data.territoryManagerBudgetGuidance[0].augSale / 100000,
            },
            {
              name: "sep",
              sale: data.territoryManagerBudgetGuidance[0].sepSale / 100000,
            },
            {
              name: "oct",
              sale: data.territoryManagerBudgetGuidance[0].octSale / 100000,
            },
            {
              name: "nov",
              sale: data.territoryManagerBudgetGuidance[0].novSale / 100000,
            },
            {
              name: "dec",
              sale: data.territoryManagerBudgetGuidance[0].decSale / 100000,
            },
            {
              name: "jan",
              sale: data.territoryManagerBudgetGuidance[0].janSale / 100000,
            },
            {
              name: "feb",
              sale: data.territoryManagerBudgetGuidance[0].febSale / 100000,
            },
            {
              name: "mar",
              sale: data.territoryManagerBudgetGuidance[0].marSale / 100000,
            },
          ];
        } else {
          this.budgetGuidanceData = this.defaultSaleArr;
        }
        this.totalGuidanceSales = 0;
        this.budgetGuidanceData.forEach((res: any) => {
          this.totalGuidanceSalesFunc(res.sale);
        });
        const guidanceSalesdata = {
          name: "total",
          sale: this.totalGuidanceSales,
        };
        this.budgetGuidanceData.unshift(guidanceSalesdata);

        if (
          data &&
          data.selectedCustomerBudgetedValue &&
          data.selectedCustomerBudgetedValue.length !== 0
        ) {
          this.isTMBudgetCustSale = true;
          this.tmBudgetData = [
            {
              name: "apr",
              sale: data.selectedCustomerBudgetedValue[0].aprSale / 100000,
            },
            {
              name: "may",
              sale: data.selectedCustomerBudgetedValue[0].maySale / 100000,
            },
            {
              name: "jun",
              sale: data.selectedCustomerBudgetedValue[0].junSale / 100000,
            },
            {
              name: "jul",
              sale: data.selectedCustomerBudgetedValue[0].julSale / 100000,
            },
            {
              name: "aug",
              sale: data.selectedCustomerBudgetedValue[0].augSale / 100000,
            },
            {
              name: "sep",
              sale: data.selectedCustomerBudgetedValue[0].sepSale / 100000,
            },
            {
              name: "oct",
              sale: data.selectedCustomerBudgetedValue[0].octSale / 100000,
            },
            {
              name: "nov",
              sale: data.selectedCustomerBudgetedValue[0].novSale / 100000,
            },
            {
              name: "dec",
              sale: data.selectedCustomerBudgetedValue[0].decSale / 100000,
            },
            {
              name: "jan",
              sale: data.selectedCustomerBudgetedValue[0].janSale / 100000,
            },
            {
              name: "feb",
              sale: data.selectedCustomerBudgetedValue[0].febSale / 100000,
            },
            {
              name: "mar",
              sale: data.selectedCustomerBudgetedValue[0].marSale / 100000,
            },
          ];
        } else {
          this.tmBudgetData = this.defaultSaleArr;
        }
        if (
          data &&
          data.selectedBrandTotalUpdatedVolume &&
          data.selectedBrandTotalUpdatedVolume.length !== 0
        ) {
          this.isTMBudgetVolSale = true;
          _.each(this.tmBudgetData, (res) => {
            _.each(data.selectedBrandTotalUpdatedVolume, (volD) => {
              _.extend(res, { vol: volD[res.name + "Volume"] });
            });
          });
        }

        if (
          data &&
          data.territoryBudgetedValue &&
          data.territoryBudgetedValue.length !== 0
        ) {
          this.isTMBudgetData = true;
          _.each(this.tmBudgetData, (res) => {
            _.each(data.territoryBudgetedValue, (saleData) => {
              _.extend(res, {
                saleTotalTerritory: saleData[res.name + "Sale"] / 100000,
              });
            });
          });
        }
        this.tmBudgetSelectedCustSales = 0;
        this.tmBudgetSelectedBrandVol = 0;
        this.totalTerritoryTMBudgetSale = 0;
        this.tmBudgetData.forEach((res: any) => {
          this.tmBudgetSalesFunc(res.sale, res.vol, res.saleTotalTerritory);
        });
        const tmBudgetCustSalesdata = {
          name: "total",
          sale: this.tmBudgetSelectedCustSales,
          vol: this.tmBudgetSelectedBrandVol,
          saleTotalTerritory: this.totalTerritoryTMBudgetSale,
        };
        this.tmBudgetData.unshift(tmBudgetCustSalesdata);
        this.spinner.hide();
      },
      (errorResponse: any) => {
        this.toastr.error(AppConstant.PREV_YEAR_SALES_FETCHING_ERROR);
        this.spinner.hide();
      }
    );
  }

  totalSalesFunc(val: any, vol: any): any {
    this.totalSales += val;
    this.totalVol += vol;
  }

  totalGuidanceSalesFunc(val: any): void {
    this.totalGuidanceSales += val;
  }

  tmBudgetSalesFunc(val: any, vol: any, territorySale: any): void {
    this.tmBudgetSelectedCustSales += val;
    this.tmBudgetSelectedBrandVol += vol;
    this.totalTerritoryTMBudgetSale += territorySale;
  }

  skusVolFunc(
    aprVol: any,
    mayVol: any,
    junVol: any,
    julVol: any,
    augVol: any,
    sepVol: any,
    octVol: any,
    novVol: any,
    decVol: any,
    janVol: any,
    febVol: any,
    marVol: any
  ): number {
    let total = 0;
    total =
      aprVol +
      mayVol +
      junVol +
      julVol +
      augVol +
      sepVol +
      octVol +
      novVol +
      decVol +
      janVol +
      febVol +
      marVol;
    return total;
  }
  getMaterialDetails(): void {
    let data = {
      customerCode: this.customerDetails["code"],
      brandCode: encodeURI(this.brandDetails["code"]),
      territoryCode: this.territoryCode,
      profitCenterCode: this.profitCenter,
      profitCenterFinanceYearsId: this.fyID,
    };

    this.userService.materialMaster(data).subscribe({
      next: (materialMasterData: any) => {
        this.productDetailsData = [];
        this.editProductDetailsData = [];
        this.calcProductDetailsData = [];
        this.isSKUDataEditable = [];
        this.isSKUData = false;
        let count = 0;

        materialMasterData.forEach((materialMasterInfo: any) => {
          if (materialMasterData) {
            this.isSKUData = true;
            let UoM = " - " + materialMasterInfo.skusUom;
            if (materialMasterInfo.skusUom == null) {
              UoM = "";
            }

            const tmBudgetInfoObj = {
              counter: count,
              sku_code_desc_uom:
                materialMasterInfo.skusCode +
                " - " +
                materialMasterInfo.skusDescription +
                UoM,
              tm_budget_volume: 0,
              apr: materialMasterInfo.aprVolume,
              may: materialMasterInfo.mayVolume,
              jun: materialMasterInfo.junVolume,
              jul: materialMasterInfo.julVolume,
              aug: materialMasterInfo.augVolume,
              sept: materialMasterInfo.sepVolume,
              oct: materialMasterInfo.octVolume,
              nov: materialMasterInfo.novVolume,
              dec: materialMasterInfo.decVolume,
              jan: materialMasterInfo.janVolume,
              feb: materialMasterInfo.febVolume,
              mar: materialMasterInfo.marVolume,
            };

            this.isSKUDataEditable.push(false);
            this.productDetailsData.push(tmBudgetInfoObj);
            count = count + 1;
          }
        });

        this.productDetailsData.forEach((res: any) => {
          let productDetailsSKUsVol = 0;
          productDetailsSKUsVol = this.skusVolFunc(
            res.apr,
            res.may,
            res.jun,
            res.jul,
            res.aug,
            res.sept,
            res.oct,
            res.nov,
            res.dec,
            res.jan,
            res.feb,
            res.mar
          );
          this.productDetailsData[res.counter].tm_budget_volume =
            productDetailsSKUsVol;
        });

        this.editProductDetailsData = JSON.parse(
          JSON.stringify(this.productDetailsData)
        );

        this.calcProductDetailsData = JSON.parse(
          JSON.stringify(this.productDetailsData)
        );

        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.toastr.error(AppConstant.PERCENTAGE_GROWTH_FETCH_ERROR);
        this.spinner.hide();
      },
    });
  }

  brandVolume(): void {
    let data = {
      customerCode: this.customerDetails["code"],
      brandCode: encodeURI(this.brandDetails["code"]),
      territoryCode: this.territoryCode,
      profitCenterCode: this.profitCenter,
      profitCenterFinanceYearsId: this.fyID,
    };

    this.userService.brandVolumeDetails(data).subscribe({
      next: (brandVolumeData: any) => {
        this.brandVolumeData = [];
        this.editBrandVolumeData = [];
        this.editCalcTMBudgetData = [];

        this.brandVolumeData = [
          {
            name: "aprTotal",
            total: parseFloat(
              brandVolumeData.aprVolume === null ? 0 : brandVolumeData.aprVolume
            ),
          },
          {
            name: "mayTotal",
            total: parseFloat(
              brandVolumeData.mayVolume === null ? 0 : brandVolumeData.mayVolume
            ),
          },
          {
            name: "junTotal",
            total: parseFloat(
              brandVolumeData.junVolume === null ? 0 : brandVolumeData.junVolume
            ),
          },
          {
            name: "julTotal",
            total: parseFloat(
              brandVolumeData.julVolume === null ? 0 : brandVolumeData.julVolume
            ),
          },
          {
            name: "augTotal",
            total: parseFloat(
              brandVolumeData.augVolume === null ? 0 : brandVolumeData.augVolume
            ),
          },
          {
            name: "sepTotal",
            total: parseFloat(
              brandVolumeData.sepVolume === null ? 0 : brandVolumeData.sepVolume
            ),
          },
          {
            name: "octTotal",
            total: parseFloat(
              brandVolumeData.octVolume === null ? 0 : brandVolumeData.octVolume
            ),
          },
          {
            name: "novTotal",
            total: parseFloat(
              brandVolumeData.novVolume === null ? 0 : brandVolumeData.novVolume
            ),
          },
          {
            name: "decTotal",
            total: parseFloat(
              brandVolumeData.decVolume === null ? 0 : brandVolumeData.decVolume
            ),
          },
          {
            name: "janTotal",
            total: parseFloat(
              brandVolumeData.janVolume === null ? 0 : brandVolumeData.janVolume
            ),
          },
          {
            name: "febTotal",
            total: parseFloat(
              brandVolumeData.febVolume === null ? 0 : brandVolumeData.febVolume
            ),
          },
          {
            name: "marTotal",
            total: parseFloat(
              brandVolumeData.marVolume === null ? 0 : brandVolumeData.marVolume
            ),
          },
        ];

        const skusTotal = this.skusVolFunc(
          brandVolumeData.aprVolume == null ? 0 : brandVolumeData.aprVolume,
          brandVolumeData.mayVolume === null ? 0 : brandVolumeData.mayVolume,
          brandVolumeData.junVolume === null ? 0 : brandVolumeData.junVolume,
          brandVolumeData.julVolume === null ? 0 : brandVolumeData.julVolume,
          brandVolumeData.augVolume === null ? 0 : brandVolumeData.augVolume,
          brandVolumeData.sepVolume === null ? 0 : brandVolumeData.sepVolume,
          brandVolumeData.octVolume === null ? 0 : brandVolumeData.octVolume,
          brandVolumeData.novVolume === null ? 0 : brandVolumeData.novVolume,
          brandVolumeData.decVolume === null ? 0 : brandVolumeData.decVolume,
          brandVolumeData.janVolume === null ? 0 : brandVolumeData.janVolume,
          brandVolumeData.febVolume === null ? 0 : brandVolumeData.febVolume,
          brandVolumeData.marVolume === null ? 0 : brandVolumeData.marVolume
        );

        const totalData = {
          name: "total",
          total: skusTotal,
        };

        this.brandVolumeData.unshift(totalData);
        this.editBrandVolumeData = JSON.parse(
          JSON.stringify(this.brandVolumeData)
        );

        this.editCalcTMBudgetData = JSON.parse(
          JSON.stringify(this.brandVolumeData)
        );
      },
      error: () => {
        this.toastr.error(AppConstant.BRAND_SKU_ERROR);
        this.spinner.hide();
      },
    });
  }
  ngAfterViewInit() {
    setTimeout(() => {
      this.events.setChangedContentTopText(
        "Product Details FOR " + AuthenticationHelper.getProfitCenterName()
      );
    }, 0);
  }

  setTableHeader(): void {
    this.getMaterialDetails();
    this.getPreviousYearSales();
    this.brandVolume();
  }

  prevSalesSKUData(): void {
    this.getPreviousYearSales();
    this.getMaterialDetails();
    this.brandVolume();
  }

  changeInputValue(val: any, col: any, index: any): void {
    this.editBrandVolumeData[index][col] = parseFloat(val.target.value);
  }

  addSaleVol(val: any, index: any): void {
    if (index != 0) {
      let calc = 0,
        arrTMBudget = [];
      arrTMBudget = JSON.parse(JSON.stringify(this.editBrandVolumeData));
      arrTMBudget = arrTMBudget.splice(1, 12);
      switch (val) {
        case "total":
          arrTMBudget.forEach((res: any) => {
            calc = parseFloat((calc + res.total).toFixed(2));
          });
          if (
            this.editBrandVolumeData[index].total !==
            this.brandVolumeData[index].total
          ) {
            this.brandVolumeData[index].total = parseFloat(
              this.editBrandVolumeData[index].total.toFixed(2)
            );
            this.brandVolumeData[0].total = calc;
            this.editBrandVolumeData[0].total = calc;
          }
          break;
      }
    } else {
      let calPercentage = 0.0;
      const originalVal = this.brandVolumeData[index].total;
      const updatedVal = this.editBrandVolumeData[index].total;
      calPercentage = parseFloat(
        (((updatedVal - originalVal) / originalVal) * 100).toFixed(2)
      );
      if (calPercentage !== Infinity) {
        for (let j = 1; j < this.brandVolumeData.length; j++) {
          const finalValue = parseFloat(
            (
              this.brandVolumeData[j].total +
              (this.brandVolumeData[j].total * calPercentage) / 100
            ).toFixed(2)
          );
          this.brandVolumeData[j].total = finalValue;
          this.editBrandVolumeData[j].total = finalValue;
        }
      }
    }
  }

  editSKUData(val: any, index?: any): void {
    this.isEditable = false;
    switch (val) {
      case "edit":
        this.isSKUDataEditable = [];
        this.isSKUDataEditable.push(false);
        this.isSKUDataEditable[index] = true;
        break;
      case "cancel":
        this.isSKUDataEditable[index] = false;
        break;
    }
    this.brandVolumeData = JSON.parse(
      JSON.stringify(this.editCalcTMBudgetData)
    );
    this.productDetailsData = JSON.parse(
      JSON.stringify(this.calcProductDetailsData)
    );
    this.editProductDetailsData = JSON.parse(
      JSON.stringify(this.calcProductDetailsData)
    );
  }

  changeInputValueSKUData(val: any, id: any, index: any, key: any): void {
    this.editProductDetailsData[index][key] = parseFloat(val.target.value);
  }

  updateSKUData(index: any, month: any): void {
    let calc = 0,
      arrTMBudget = [];
    arrTMBudget = JSON.parse(JSON.stringify(this.editProductDetailsData));
    switch (month) {
      case "tm_budget_volume":
        this.percentageCalculation(arrTMBudget, index, month, "total");
        break;
      default:
        this.percentageCalculation(arrTMBudget, index, month, "others");
    }
  }

  percentageCalculation(newArray: any, i: any, mon: any, col: any): void {
    let calVal = 0;
    const updatedVal = this.editProductDetailsData[i];
    const originalVal = this.productDetailsData[i];
    let calPercentage;
    if (calPercentage != 0.0) {
      switch (col) {
        case "total":
          if (originalVal[mon] !== 0) {
            calPercentage = parseFloat(
              (
                ((updatedVal[mon] - originalVal[mon]) / originalVal[mon]) *
                100
              ).toFixed(2)
            );
            let keys = [];
            keys = Object.keys(newArray[i]);
            for (let j = 2; j < keys.length; j++) {
              let skuRow = this.productDetailsData[i];
              let editSkuRow = this.editProductDetailsData[i];
              const finalValue =
                skuRow[keys[j]] + (skuRow[keys[j]] * calPercentage) / 100;
              skuRow[keys[j]] = finalValue;
              editSkuRow[keys[j]] = finalValue;
            }
          }
          break;
        default:
          calVal = updatedVal[mon] - originalVal[mon];
          originalVal[mon] = originalVal[mon] + calVal;
          originalVal["tm_budget_volume"] =
            originalVal["tm_budget_volume"] + calVal;
          updatedVal["tm_budget_volume"] =
            updatedVal["tm_budget_volume"] + calVal;
          break;
      }
    }
  }
  onWindowResizeTMBudget(): void {
    this.ismobileViewPD = window.innerWidth <= 1023 ? true : false;
  }

  getFixedValue(value: any): any {
    return value ? value.toFixed(2) : 0;
  }

  getFixedValueSale(value: any): any {
    return value ? value.toFixed(4) : 0;
  }
  updateSKUDataSales(_index: number): void {
    this.spinner.show();
    let productDetailsArray: any = [];
    this.productDetailsData.forEach((res: any) => {
      let data = {
        aprVolume: res.apr,
        augVolume: res.aug,
        decVolume: res.dec,
        febVolume: res.feb,
        janVolume: res.jan,
        julVolume: res.jul,
        junVolume: res.jun,
        marVolume: res.mar,
        mayVolume: res.may,
        novVolume: res.nov,
        octVolume: res.oct,
        sepVolume: res.sept,
        skuCode: res.sku_code_desc_uom.split(" -")[0],
      };
      productDetailsArray.push(data);
    });

    let queryParams = {
      customerCode: this.customerDetails["code"],
      brandCode: this.brandDetails["code"],
      territoryCode: this.territoryCode,
      profitCenterCode: this.profitCenter,
      profitCenterFinanceYearsId: this.fyID,
    };

    let updateSKURequest = this.userService.updateSKU(queryParams);

    updateSKURequest.pipe(map((res: any) => res.updatedSkuDTO)).subscribe({
      next: (updatedSkuDTO: any) => {
        this.toastr.success(AppConstant.SKU_UPDATED_SUCCESS);
        this.getPreviousYearSales();
        this.getMaterialDetails();
        this.brandVolume();
      },
      error: () => {
        this.toastr.error(AppConstant.SKU_UPDATE_ERROR);
        this.spinner.hide();
      },
    });
  }

  onWindowResizeProductDetails() {
    this.ismobileViewPD = window.innerWidth <= 1023 ? true : false;
    this.events.setChangedContentTopText(
      "Product Details FOR " + AuthenticationHelper.getProfitCenterName()
    );
  }
}
