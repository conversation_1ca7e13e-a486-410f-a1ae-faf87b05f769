<div class="region-container">
  <div class="region-grid-container">

    <nga-app-filter
            (onSearchValue)="onSearch($event)"
            [configurations]="showOtherFilters"
            [addButton]="userButton"
            (onExport)="onExport()">
    </nga-app-filter>

    <div class="region-grid-data-parent-container">
      <div id="foo" class="region-grid-data-container">
        <div class="region-table">
          <dynamic-table [tableHeads]="tableHead"
                         [tableData]="regionData"
                         [tableConfiguration]="configurationSettings"
                         [tableColName]="tableColName"
                         (pageChange)="getRegionPageData($event)"
                         [showIndex]="showIndex">
          </dynamic-table>
        </div>
      </div>
    </div>
  </div>
</div>
