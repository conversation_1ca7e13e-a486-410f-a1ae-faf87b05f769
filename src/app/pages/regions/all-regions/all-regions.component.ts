import { Component, ViewEncapsulation, ElementRef } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { GlobalEvents } from "../../../helpers/global.events";
import { AppConstant } from "../../../constants/app.constant";
import { RegionsService } from "../../../app-services/regions-service";
import { ngxCsv } from "ngx-csv";
import { DynamicTableComponent } from "../../../shared/data-table/data-table.component";
import { FilterComponent } from "../../../shared/filter/filter.component";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { DashboardService } from "src/app/app-services/dashboard.service";
@Component({
    selector: "nga-all-regions",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["./../regions.component.scss"],
    templateUrl: "all-regions.component.html",
    imports: [FilterComponent, DynamicTableComponent],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class AllRegionsComponent {
  regionData: any = [];
  tableHead: any = [];
  tableColName: any = [];
  showIndex: any = { index: null };
  configurationSettings: any = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    actionsColumnName: "Active Area",
    noDataMessage: "No data found",
    showStatus: true,
    showEdit: false,
    changeStatus: false,
  };
  searchedValue: string = "";
  isSearch: boolean = false;

  showOtherFilters: any = {
    showRadioFilters: false,
    showSearch: true,
    add: false,
    showdropdown1Filters: false,
    showdropdown2Filters: false,
    showSearchiconFilters: false,
    showReport: false,
    export: true,
  };
  userButton: any = [];
  exportData: any = [];

  constructor(
    private routes: ActivatedRoute,
    private eRef: ElementRef,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private events: GlobalEvents,
    private regionsService: RegionsService,
    private router: Router,
    private dashboardService : DashboardService
  ) {
    this.spinner.hide();
  }
  ngOnInit() {
    this.setTableHeader();
    this.commonAddButton();
    this.spinner.hide();
    window.scrollTo(0, 0);
  }
  ngAfterViewInit() {
    setTimeout(() => {
      this.events.setChangedContentTopText("Regions");
    }, 0);
  }

  commonAddButton(): void {
    this.userButton = [
      {
        path: "/regions/add-region",
        title: " Add Region",
      },
    ];
  }
  setTableHeader(): void {
    this.tableHead = ["Region Code", "Region Name", "Zone Name"];
    this.tableColName = ["code", "name", "zone"];
    this.regionData = [];
    this.getAllRegion();
  }

  /**
   * Method to get the all region details
   * @param page
   */
  getAllRegion(page?: number):void{
    let data = {
      pageLimit: AppConstant.PER_PAGE_ITEMS,
      currentPage: page ? page - 1 : 0,
      searchedValue: encodeURIComponent(this.searchedValue),
    };
    this.spinner.show();
    const regions = this.regionsService.getAllRegions(data);
    regions.subscribe({
      next: (regionData: any) => {
        regionData = this.dashboardService.removeSuffix(regionData);
        this.regionData = [];
        if (regionData && regionData.content && regionData.content.length) {
          regionData.content.forEach((regionInfo: any) => {
            const regionInfoObj = {
              code: regionInfo.code ? regionInfo.code : "---",
              id: regionInfo.id ? regionInfo.id : "---",
              name: regionInfo.regionName ? regionInfo.regionName : "---", 
              zone: regionInfo.zoneName,
              is_active: regionInfo.active,
            };
            this.regionData.push(regionInfoObj);
          });
          this.configurationSettings.totalRecordCount =
            regionData.totalElements;
        } else {
          this.configurationSettings.totalRecordCount = 0;
        }
        this.spinner.hide();
        this.events.setChangedContentTopText(
          "Regions (" + this.configurationSettings.totalRecordCount + ")"
        );
      },
      error: (errorResponse : any) => { 
        let errorMsg = errorResponse.status;
        if (+errorMsg ===  401 || +errorMsg ===  404) {  
          localStorage.clear();
          this.router.navigate([""]);
          this.toastr.success("Signed Out Successfully");
        } else{ 
          this.toastr.error(AppConstant.REGION_FETCH_ERROR);
        } 
        this.spinner.hide();
      },
    });
  }

  /**
   * For searching the regions
   * @param searched query
   */
  onSearch(event: any): void {
    if (event.trim()) {
      this.isSearch = true;
      this.searchedValue = event.trim();
      this.getRegionPageData(1);
    } else {
      if (this.isSearch) {
        this.isSearch = false;
        this.searchedValue = "";
        this.getRegionPageData(1);
      }
    }
  }

  /**
   * To handle the page change event
   * @param page
   */
  getRegionPageData(page: number): void {
    this.configurationSettings.currentPage = page;
    this.getAllRegion(this.configurationSettings.currentPage);
  }

  /**
   * Method for exporting the regions data in CSV format
   * @param event
   */
  onExport(): void{
    this.getRegionsExportData();
  }
  getRegionsExportData():void{
    window.scrollTo(0, 0);
    this.spinner.show();
    let data = {
      searchedValue: encodeURIComponent(this.searchedValue),
    };
    const exports = this.regionsService.getAllRegionsExportData(data);
    exports.subscribe({
      next: (exportsData: any) => {
        exportsData = this.dashboardService.removeSuffix(exportsData);
        this.exportData = [];
        if (exportsData && exportsData.content.length) {
          exportsData.content.forEach((exportInfo: any) => {
            let exportObj = { 
              region_code: exportInfo.code ? exportInfo.code : "---",
              region_name: exportInfo.regionName ? exportInfo.regionName : "---", 
              zone_name: exportInfo.zoneName ? exportInfo.zoneName : "---",
              is_active: exportInfo.active ? exportInfo.active : "---",
            };
            this.exportData.push(exportObj);
          });
          let options = {
            fieldSeparator: ",",
            quoteStrings: '"',
            decimalseparator: ".",
            showLabels: true,
            headers: [ 
              "Region Code",
              "Region Name", 
              "Zone Name",
              "Is Active",
            ],
          };
          new ngxCsv(this.exportData, "Region Details", options);
        }else{
          this.toastr.warning("No data available to export!");
        }
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.EXPORT_ERROR);
        this.spinner.hide();
      },
    });
  }
}
