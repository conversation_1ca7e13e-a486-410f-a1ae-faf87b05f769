<div class="region-form-container">
  <div class="region-form-content">
    <form [formGroup]="regionForm" (ngSubmit)="onSubmit()">
      <div class="col-md-12 row region-form">
        <div>
          <h2 class="region-form-info-title">Region Information</h2>
          <div class="region-form-information-section">
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Profit Center<i class="required">&nbsp;*</i></label
                >
                <select
                  class="form-control"
                  formControlName="profit_center"
                  [(ngModel)]="profitCenter"
                >
                  <option value="" hidden>Select Profit Center</option>
                  <option value="af">AF</option>
                  <option value="swal">SWAL</option>
                </select>
              </div>
            </div>
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Select Zone<i class="required">&nbsp;*</i></label
                >
                <select
                  class="form-control"
                  formControlName="zone"
                  [(ngModel)]="zone"
                >
                  <option value="" hidden>Select Zone</option>
                  <option value="east">East Zone</option>
                  <option value="west">West Zone</option>
                  <option value="north">North Zone</option>
                  <option value="south">South Zone</option>
                </select>
              </div>
            </div>
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Region Name<i class="required">&nbsp;*</i></label
                >
                <input
                  formControlName="name"
                  type="text"
                  class="form-control"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !regionForm.get('name')?.valid &&
                    regionForm.get('name')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >Region name is required.</span
                >
              </div>
            </div>
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Region Code<i class="required">&nbsp;*</i></label
                >
                <input
                  formControlName="code"
                  type="text"
                  class="form-control"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !regionForm.get('code')?.valid &&
                    regionForm.get('code')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >Region code is required.</span
                >
              </div>
            </div>
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style">Status</label>
                <div>
                  <span class="region-form-status">
                    <input
                      type="radio"
                      class="btn"
                      value="1"
                      name="status"
                      id="active"
                      formControlName="status"
                    />
                    <label for="active" class="region-form-label">Active</label>
                  </span>

                  <span class="region-form-status">
                    <input
                      type="radio"
                      class="btn"
                      value="0"
                      name="status"
                      id="inactive"
                      formControlName="status"
                    />
                    <label for="inactive" class="region-form-label"
                      >Inactive</label
                    >
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="col-md-12 form-group input-button submit-button padding-left-right"
        >
          <button
            type="button"
            class="btn-cancel-style pull-left"
            (click)="location.back()"
          >
            CANCEL
          </button>
          <button
            [ngClass]="{ 'disable-submit': !regionForm.valid }"
            [disabled]="!regionForm.valid"
            type="submit"
            class="btn-style"
          >
            {{ regionId ? "UPDATE" : "ADD REGION" }}
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
