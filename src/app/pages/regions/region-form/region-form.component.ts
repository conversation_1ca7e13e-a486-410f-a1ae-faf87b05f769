import { Component } from "@angular/core";
import { Location, NgIf, NgClass } from "@angular/common";
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { Router, ActivatedRoute } from "@angular/router";
import { GlobalEvents } from "../../../helpers/global.events";
import { UserService } from "../../../app-services/user-service";
import { AppConstant } from "../../../constants/app.constant";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";

@Component({
    selector: "nga-add-region",
    styleUrls: ["region-form.component.scss"],
    templateUrl: "region-form.component.html",
    imports: [FormsModule, ReactiveFormsModule, NgIf, NgClass],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class RegionFormComponent {
  regionForm!: FormGroup;
  regionId: any;
  regionData: any;
  profitCenter: string = "";
  zone: string = "";
  constructor(
    private fb: FormBuilder,
    public location: Location,
    private router: Router,
    public events: GlobalEvents,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private activeRoute: ActivatedRoute,
    private userService: UserService
  ) {
    this.regionId = this.activeRoute.snapshot.queryParams["id"];
  }

  /**
   *  Called automatically on init
   */
  ngOnInit() {
    this.setUserForm();
    if (this.regionId) {
      this.getRegionByID(this.regionId);
    }
    window.scrollTo(0, 0);
    this.spinner.hide();
  }

  /**
   * Method for getting specific region details
   * @param id
   */
  getRegionByID(id: any) {
    this.regionData = {
      code: "Z-101",
      name: "East Zone",
      profit_center: "swal",
      zone: "east",
      status: "1",
    };
    this.setUserForm(this.regionData);
  }

  /**
   *  Called automatically after view init
   */
  ngAfterViewInit() {
    setTimeout(() => {
      this.events.setChangedContentTopText(
        this.regionId ? "Edit Region" : "Add Region"
      );
    }, 0);
  }

  /**
   *  To set user form values
   */
  setUserForm(values?: any) {
    this.regionForm = this.fb.group({
      code: [
        values ? values.code : "",
        Validators.compose([Validators.required]),
      ],
      name: [
        values ? values.name : "",
        Validators.compose([Validators.required]),
      ],
      profit_center: [
        values ? (this.profitCenter = values.profit_center) : "",
        Validators.compose([Validators.required]),
      ],
      zone: [
        values ? (this.zone = values.zone) : "",
        Validators.compose([Validators.required]),
      ],
      status: [
        values ? values.status : "",
        Validators.compose([Validators.required]),
      ],
    });
  }

  /**
   * submission of user form to add or update user.
   */
  onSubmit(): void {
    if (this.regionId) {
      this.toastr.success(AppConstant.REGION_UPDATE_SUCCESS);
    } else {
      this.toastr.success(AppConstant.REGION_CREATE_SUCCESS);
    }
    this.location.back();
  }
}
