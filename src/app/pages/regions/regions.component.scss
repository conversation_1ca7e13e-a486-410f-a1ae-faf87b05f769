@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

$font-size: 13px;

.region-container {
  width: 96%;
  margin: 45px 2.5% 15px 2.5%;
  overflow-y: hidden;
  .region-grid-container {
    width: 99%;
    float: left;
    border-radius: $border-radius;
    margin-top: 30px;
    position: relative;
    .region-grid-data-parent-container {
      min-height: 485px;
      background: #ffffff;
      overflow-y: auto;
    }
    .region-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      // padding: 10px 15px 25px;

      .region-table {
        font-size: 15px;
        min-width: 100%;
        overflow-y: hidden;
        border-radius: $table-border-radius;
      }
    }
  }
}
