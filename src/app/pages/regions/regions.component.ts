import { Component } from "@angular/core";
import { RouterOutlet } from "@angular/router";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaThemeSpinner } from "src/app/theme/services/baThemeSpinner/baThemeSpinner.service";

@Component({
    selector: "nga-regions",
    templateUrl: "regions.component.html",
    styleUrls: ["regions.component.scss"],
    imports: [RouterOutlet],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class RegionsComponent {
  constructor() {}
}
