<div (window:resize)="onWindowResizeTMBudget()"></div>
<div class="reports-container">
  <div *ngIf="isZM" class="customers-count">
    <h5 class="margin-bottom-0">TERRITORIES</h5>
    <h5 class="margin-bottom-0">{{ territoriesLength }}</h5>
  </div>
  <div class="reports-grid-container">
    <nga-app-filter [configurations]="showOtherFilters"> </nga-app-filter>
    <div
      *ngIf="viewToBePreview == 'Default'"
      class="default-reports reports-grid-action padding-top-10 padding-bottom-10"
    >
      <div class="float-left report-heading">
        <h5 class="margin-bottom-0">Sales Value Report (in Lacs)</h5>
      </div>
      <div class="float-left width-100 overflow-auto">
        <table class="table table-striped table-list table-border">
          <thead>
            <tr>
              <th class="width-14">Months</th>
              <th>Last Year Sales Value(₹)</th>
              <th>Last Year Sales Qty (KG or L)</th>
              <th>Reference Budget Sales Value (₹)</th>
              <th>Reference Budget Sales Qty (KG or L)</th>
              <th>TM Budget Sales Value (₹)</th>
              <th>TM Budget Sales Qty (KG or L)</th>
            </tr>
          </thead>
          <tbody>
            <tr class="background-total">
              <th class="total-font-color">Total</th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueSale(totalDifferentSales['LYSalestotal'])
                }}"
              >
                {{ getFixedValueSale(totalDifferentSales["LYSalestotal"]) }}
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueVolume(totalDifferentSales['LYVolumetotal'])
                }}"
              >
                {{ getFixedValueVolume(totalDifferentSales["LYVolumetotal"]) }}
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueSale(
                    totalDifferentSales['ReferenceBudgetSalestotal']
                  )
                }}"
              >
                {{
                  getFixedValueSale(
                    totalDifferentSales["ReferenceBudgetSalestotal"]
                  )
                }}
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueVolume(
                    totalDifferentSales['ReferenceBudgetVoltotal']
                  )
                }}"
              >
                {{
                  getFixedValueVolume(
                    totalDifferentSales["ReferenceBudgetVoltotal"]
                  )
                }}
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueSale(totalDifferentSales['tmBudgetSalestotal'])
                }}"
              >
                {{
                  getFixedValueSale(totalDifferentSales["tmBudgetSalestotal"])
                }}
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueVolume(totalDifferentSales['tmBudgetVoltotal'])
                }}"
              >
                {{
                  getFixedValueVolume(totalDifferentSales["tmBudgetVoltotal"])
                }}
              </th>
            </tr>
            <tr
              *ngFor="let lastYearSale of lastYearSales; let i = index"
              [ngClass]="{
                'row-color': i == 3 || i == 7 || i == 11 || i == 15
              }"
            >
              <th>{{ defaultArr[i] }}</th>
              <td title="{{ getFixedValueSale(lastYearSale) }}">
                {{ getFixedValueSale(lastYearSale) }}
              </td>
              <td title="{{ getFixedValueVolume(lastYearVolume[i]) }}">
                {{ getFixedValueVolume(lastYearVolume[i]) }}
              </td>
              <td title="{{ getFixedValueSale(referenceBudgetSales[i]) }}">
                {{ getFixedValueSale(referenceBudgetSales[i]) }}
              </td>
              <td title="{{ getFixedValueVolume(referenceBudgetVolume[i]) }}">
                {{ getFixedValueVolume(referenceBudgetVolume[i]) }}
              </td>
              <td title="{{ getFixedValueSale(tmBudgetSales[i]) }}">
                {{ getFixedValueSale(tmBudgetSales[i]) }}
              </td>
              <td title="{{ getFixedValueVolume(tmBudgetVol[i]) }}">
                {{ getFixedValueVolume(tmBudgetVol[i]) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div
      *ngIf="viewToBePreview == 'Region'"
      class="reports-grid-action padding-top-10 padding-bottom-10"
    >
      <div class="float-left report-heading">
        <h5 class="margin-bottom-0">
          Region Wise - Sales Value Report (in Lacs)
        </h5>
      </div>
      <div class="float-left width-100 overflow-auto">
        <table class="table table-striped table-list table-border">
          <thead>
            <tr>
              <th>Regions</th>
              <th class="width-25">Last Year Sales (₹)</th>
              <th class="width-25">Reference Budget Value (₹)</th>
              <th class="width-25">TM Budget Value (₹)</th>
            </tr>
          </thead>
          <tbody>
            <tr class="background-total">
              <th
                class="total-font-color cursor-pointer region-name"
                (click)="viewMonthWise('All Regions')"
              >
                Total
              </th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value padding-right-16">
                0
              </th>
            </tr>
            <tr>
              <th
                class="total-font-color cursor-pointer region-name"
                (click)="viewMonthWise('Agra Region')"
              >
                Agra Region
              </th>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right padding-right-16">0</td>
            </tr>
            <tr>
              <th
                class="total-font-color cursor-pointer region-name"
                (click)="viewMonthWise('Ahemdabad Region')"
              >
                Ahemdabad Region
              </th>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right padding-right-16">0</td>
            </tr>
            <tr>
              <th
                class="total-font-color cursor-pointer region-name"
                (click)="viewMonthWise('Bhopal Region')"
              >
                Bhopal Region
              </th>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right padding-right-16">0</td>
            </tr>
            <tr>
              <th
                class="total-font-color cursor-pointer region-name"
                (click)="viewMonthWise('Mumbai Region')"
              >
                Mumbai Region
              </th>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right padding-right-16">0</td>
            </tr>
            <tr>
              <th
                class="total-font-color cursor-pointer region-name"
                (click)="viewMonthWise('Pune Region')"
              >
                Pune Region
              </th>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right padding-right-16">0</td>
            </tr>
            <tr>
              <th
                class="total-font-color cursor-pointer region-name"
                (click)="viewMonthWise('Agra Region')"
              >
                Satara Region
              </th>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right">0</td>
              <td class="total-font-color text-right padding-right-16">0</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!--Customer Wise View-->
    <div
      *ngIf="
        viewToBePreview == 'Customer' ||
        viewToBePreview == 'Brand' ||
        viewToBePreview == 'Territory'
      "
      class="reports-grid-action padding-top-10 padding-bottom-10"
    >
      <div class="float-left report-heading">
        <h5 *ngIf="viewToBePreview === 'Customer'" class="margin-bottom-0">
          Customer Wise - Sales Value Report (in Lacs)
        </h5>
        <h5 *ngIf="viewToBePreview === 'Brand'" class="margin-bottom-0">
          Brand Wise - Sales Value Report (in Lacs)
        </h5>
        <h5 *ngIf="viewToBePreview === 'Territory'" class="margin-bottom-0">
          Territory Wise - Sales Value Report (in Lacs)
        </h5>
      </div>
      <div class="float-left width-100 overflow-auto">
        <table class="table table-striped table-list table-border">
          <thead>
            <tr class="brands-header">
              <th *ngIf="viewToBePreview === 'Customer'">Customer Name</th>
              <th *ngIf="viewToBePreview === 'Brand'">Brand Name</th>
              <th *ngIf="viewToBePreview === 'Territory'">Territory Name</th>
              <th>Last Year Sales Value (₹)</th>
              <th>Last Year Sales Volume (KG or L)</th>
              <th>Reference Budget Value (₹)</th>
              <th>Reference Budget Volume (KG or L)</th>
              <th>TM Budget Value (₹)</th>
              <th>TM Budget Volume (KG or L)</th>
              <th>Budget Gap Value (₹)</th>
              <th>Budget Gap Volume (KG or L)</th>
            </tr>
          </thead>
          <tbody>
            <tr class="background-total">
              <th
                class="total-font-color cursor-pointer region-name"
                (click)="viewMonthWise('All Customers')"
              >
                Total
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueSale(totalDifferentSales['LYSalestotal'])
                }}"
              >
                {{ getFixedValueSale(totalDifferentSales["LYSalestotal"]) }}
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueVolume(totalDifferentSales['LYVolumetotal'])
                }}"
              >
                {{ getFixedValueVolume(totalDifferentSales["LYVolumetotal"]) }}
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueSale(
                    totalDifferentSales['ReferenceBudgetSalestotal']
                  )
                }}"
              >
                {{
                  getFixedValueSale(
                    totalDifferentSales["ReferenceBudgetSalestotal"]
                  )
                }}
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueVolume(
                    totalDifferentSales['ReferenceBudgetVoltotal']
                  )
                }}"
              >
                {{
                  getFixedValueVolume(
                    totalDifferentSales["ReferenceBudgetVoltotal"]
                  )
                }}
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueSale(totalDifferentSales['tmBudgetSalestotal'])
                }}"
              >
                {{
                  getFixedValueSale(totalDifferentSales["tmBudgetSalestotal"])
                }}
              </th>
              <th
                class="total-font-color text-right-value"
                title="{{
                  getFixedValueVolume(totalDifferentSales['tmBudgetVoltotal'])
                }}"
              >
                {{
                  getFixedValueVolume(totalDifferentSales["tmBudgetVoltotal"])
                }}
              </th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value padding-right-16">
                0
              </th>
            </tr>
            <tr
              *ngFor="
                let lastYearCustSale of lastYearTotalCustSale;
                let i = index
              "
            >
              <th
                *ngIf="viewToBePreview === 'Customer'"
                class="total-font-color cursor-pointer region-name"
              >
                Krishi Vikas Kendra - Bilha
              </th>
              <th
                *ngIf="viewToBePreview === 'Brand'"
                class="total-font-color cursor-pointer region-name"
              >
                PATELA
              </th>
              <th
                *ngIf="viewToBePreview === 'Territory'"
                class="total-font-color cursor-pointer region-name"
              >
                Indore
              </th>
              <td
                class="total-font-color text-right"
                title="{{ getFixedValueSale(lastYearCustSale.total) }}"
              >
                {{ getFixedValueSale(lastYearCustSale?.total) }}
              </td>
              <td
                class="total-font-color text-right"
                title="{{ getFixedValueVolume(lastYearTotalCustVol[i].total) }}"
              >
                {{ getFixedValueVolume(lastYearTotalCustVol[i]?.total) }}
              </td>
              <td
                class="total-font-color text-right"
                title="{{
                  getFixedValueSale(reffBudgetTotalCustSale[i].total)
                }}"
              >
                {{ getFixedValueSale(reffBudgetTotalCustSale[i]?.total) }}
              </td>
              <td
                class="total-font-color text-right"
                title="{{
                  getFixedValueVolume(reffBudgetTotalCustVol[i].total)
                }}"
              >
                {{ getFixedValueVolume(reffBudgetTotalCustVol[i]?.total) }}
              </td>
              <td
                class="total-font-color text-right"
                title="{{ getFixedValueSale(tmBudgetTotalCustSales[i].total) }}"
              >
                {{ getFixedValueSale(tmBudgetTotalCustSales[i]?.total) }}
              </td>
              <td
                class="total-font-color text-right"
                title="{{ getFixedValueVolume(tmBudgetTotalCustVol[i].total) }}"
              >
                {{ getFixedValueVolume(tmBudgetTotalCustVol[i]?.total) }}
              </td>
              <td
                class="total-font-color text-right"
                title="{{ getFixedValueSale(budgetGapSalesArr[i].total) }}"
              >
                {{ getFixedValueSale(budgetGapSalesArr[i]?.total) }}
              </td>
              <td
                class="total-font-color text-right padding-right-16"
                title="{{ getFixedValueVolume(budgetGapVolArr[i].total) }}"
              >
                {{ getFixedValueVolume(budgetGapVolArr[i]?.total) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div
    *ngIf="
      viewToBePreview == 'Customer' ||
      viewToBePreview == 'Brand' ||
      viewToBePreview == 'Territory'
    "
    class="width-100 float-left"
  >
    <div
      *ngFor="
        let data of lastYearTotalCustSale
          | paginate
            : {
                itemsPerPage: perPage,
                currentPage: currentPage,
                id: 'pagination',
                totalItems: 10
              }
      "
    ></div>
    <pagination-controls
      class="addQnhand"
      id="pagination"
      [maxSize]="5"
      [autoHide]="true"
      [directionLinks]="true"
      [autoHide]="true"
      previousLabel="Previous"
      nextLabel="Next"
      (pageChange)="getPageData($event)"
    >
    </pagination-controls>
  </div>
</div>

<div class="confirmUserActiveContainer machine-preview">
  <ng-container #monthWiseData class="month-wise-data">
    <div class="previewModal">
      <div class="modal-header">
        <div>
          <h4 class="text-center">{{ modalHeading }}</h4>
        </div>
      </div>
      <div class="float-left width-100 overflow-auto">
        <table class="table table-striped table-list table-border">
          <thead>
            <tr>
              <th>Months</th>
              <th class="width-30">Last Year Sales</th>
              <th class="width-30">Reference Budget Value</th>
              <th class="width-30">TM Budget Value</th>
            </tr>
          </thead>
          <tbody>
            <tr class="background-total">
              <th class="total-font-color">Total</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value padding-right-16">
                0
              </th>
            </tr>
            <tr>
              <th>Apr</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>May</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Jun</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Jul</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Aug</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Sep</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Oct</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Nov</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Dec</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Jan</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Feb</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Mar</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ng-container>
</div>

<div class="confirmUserActiveContainer machine-preview">
  <ng-container #brandsMonthWiseData class="month-wise-data">
    <div class="previewModal">
      <div class="modal-header">
        <div>
          <h4 class="text-center">{{ modalHeading }}</h4>
        </div>
      </div>
      <div class="float-left width-100 overflow-auto">
        <table class="table table-striped table-list table-border">
          <thead>
            <tr>
              <th>Months</th>
              <th>Last Year Sales Qty</th>
              <th>Reference Budget Sales Qty</th>
              <th>TM Budget Sales Qty</th>
              <th>Last Year Sales Value</th>
              <th>Reference Budget Value</th>
              <th>TM Budget Value</th>
              <th>Budget Gap Qty</th>
              <th>Budget Gap Value</th>
            </tr>
          </thead>
          <tbody>
            <tr class="background-total">
              <th class="total-font-color">Total</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value padding-right-16">
                0
              </th>
            </tr>
            <tr>
              <th>Apr</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>May</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Jun</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Jul</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Aug</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Sep</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Oct</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Nov</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Dec</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Jan</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Feb</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Mar</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ng-container>
</div>

<div class="confirmUserActiveContainer machine-preview">
  <ng-container #territoryMonthWiseData class="month-wise-data">
    <div class="previewModal">
      <div class="modal-header">
        <div>
          <h4 class="text-center">{{ modalHeading }}</h4>
        </div>
      </div>
      <div class="float-left width-100 overflow-auto">
        <table class="table table-striped table-list table-border">
          <thead>
            <tr>
              <th>Months</th>
              <th>Last Year Quantity</th>
              <th>Reference Budget Quantity</th>
              <th>TM Budget Quantity</th>
            </tr>
          </thead>
          <tbody>
            <tr class="background-total">
              <th class="total-font-color">Total</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value">0</th>
              <th class="total-font-color text-right-value padding-right-16">
                0
              </th>
            </tr>
            <tr>
              <th>Apr</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>May</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Jun</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Jul</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Aug</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Sep</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Oct</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Nov</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Dec</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Jan</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Feb</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
            <tr>
              <th>Mar</th>
              <td>0</td>
              <td>0</td>
              <td>0</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ng-container>
</div>
