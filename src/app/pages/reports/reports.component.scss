@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

$font-size: 13px;

select[disabled] {
  color: grey !important;
  border-color: lightgrey !important;
}

.reports-container {
  width: 96%;
  margin: 55px 2.5% 15px 2.5%;
  float: left;
  .customers-count {
    position: relative;
    right: 10px;
    float: right;
    width: 15%;
    border: 2px solid rgba(0, 0, 0, 0.15);
    border-radius: 5px;
    text-align: center;
    padding: 10px 0;
    @media only screen and (max-width: 1023px) {
      width: 100%;
    }
  }
  .view-adjustment {
    padding-right: 15px;
    padding-top: 9px;
  }
  .margin-right-5 {
    margin-right: 5px;
  }
  .region-dd {
    width: 20%;
  }
  .reports-grid-container {
    width: 99%;
    float: left;
    border-radius: $border-radius;
    margin-top: 30px;
    position: relative;
    .width-20 {
      width: 20%;
    }
    .width-50 {
      width: 50%;
    }
    .width-80 {
      width: 80%;
    }
    .width-15 {
      width: 15%;
    }
    .width-14 {
      width: 14%;
    }
    .padding-right-0 {
      padding-right: 0 !important;
    }
    .padding-left-dd {
      padding-left: 5px !important;
    }
    .padding-right-dd {
      padding-right: 5px !important;
    }
    .padding-left-0 {
      padding-left: 0 !important;
    }
    .reports-grid-action {
      float: left;
      padding: 0 15px;
      width: 100%;
      position: relative;
      margin-bottom: 10px;
      overflow-y: visible;
      background: #fff;
      border-radius: $border-radius;
      box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.1);
      @media only screen and (max-width: 1023px) {
        padding: 15px;
      }
      .reports-grid-search-container {
        width: 20% !important;
        float: left;
        padding: 1% 0;
        @include inputRightBorder($border-radius);
        @media only screen and (max-width: 1023px) {
          width: 100% !important;
        }
        .input-group {
          margin-bottom: 0;
          @include inputAddOnBorder($border-radius);
        }
        @include placeholderColor(#666, 0.7);
        .fy-input-div ::-webkit-input-placeholder {
          /* Chrome/Opera/Safari */
          color: rgba(102, 102, 102, 0.7);
        }
        .fy-input-div ::-moz-placeholder {
          /* Firefox 19+ */
          color: rgba(102, 102, 102, 0.7);
        }
        .fy-input-div :-ms-input-placeholder {
          /* IE 10+ */
          color: rgba(102, 102, 102, 0.7);
        }
        .fy-input-div :-moz-placeholder {
          /* Firefox 18- */
          color: rgba(102, 102, 102, 0.7);
        }
        .fy-input {
          height: 38px;
          color: #666666;
          &:disabled {
            border-color: rgba(0, 0, 0, 0.15);
          }
        }
        .ngx-dropdown-container .ngx-dropdown-list-container {
          z-index: 4;
          box-shadow: none;
          position: relative;
          top: 2px;
          ul.selected-items li {
            background-color: #a22e2a !important;
          }
          ul li:hover {
            background-color: #a22e2a !important;
            color: #ffffff !important;
          }
        }
        .ngx-dropdown-container {
          height: 38px;
          border: 1px solid rgba(0, 0, 0, 0.15);
          border-radius: 5px;
          .ngx-dropdown-button {
            border: none;
            height: 36px;
            &:focus {
              outline: none;
            }
          }
          button {
            span {
              color: #464a4c;
            }
          }
          &:focus {
            outline: none;
          }
        }
        .label-center {
          display: flex;
          align-items: center;
        }
        .customer-dd {
          .dropdown-list {
            z-index: 1;
            .list-area {
              width: 300px;
            }
            .hihi {
              width: auto !important;
            }
            .pure-checkbox input[type="checkbox"] + label {
              font-size: 12px;
            }
            .pure-checkbox input[type="checkbox"] + label:before {
              width: 10px;
              height: 10px;
            }
            .pure-checkbox input[type="checkbox"] + label:after {
              left: 2px;
              height: 2px;
              margin-top: -5px;
            }
          }
        }
        .customer-dd-PD {
          .dropdown-list {
            z-index: 1;
            .list-area {
              width: 300px;
            }
            .pure-checkbox input[type="checkbox"] + label {
              font-size: 12px;
            }
            .pure-checkbox input[type="checkbox"] + label:before {
              width: 10px;
              height: 10px;
            }
            .pure-checkbox input[type="checkbox"] + label:after {
              left: 2px;
              height: 2px;
              margin-top: -5px;
            }
          }
          .cuppa-dropdown {
            .selected-list {
              overflow: hidden;
              .c-btn {
                height: 38px !important;
                border: 1px solid rgba(0, 0, 0, 0.15) !important;
                border-radius: 5px !important;
                white-space: nowrap;
                span {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-all;
                }
              }
              .c-angle-down,
              .c-angle-up {
                width: 10px;
                height: 10px;
                top: 40%;
              }
            }
          }
        }
      }
      .width-16 {
        width: 16% !important;
      }
      .reports-grid-action-container {
        width: 20%;
        float: right;
        padding: 1% 0;
        padding-top: 15px;
        display: block;
        position: relative;
        @media only screen and (max-width: 1023px) {
          width: 100%;
        }
        .reports-grid-action-add {
          @include addButtonContainer();
          .disable-search {
            opacity: 0.8;
            cursor: not-allowed;
            &:hover {
              opacity: 0.8;
              cursor: not-allowed;
            }
          }
          @media only screen and (max-width: 1023px) {
            width: 100%;
          }
          width: 100%;
          padding: 17px 0 0;
        }
      }

      .cursor-pointer {
        cursor: pointer;
      }
      .region-name:hover {
        text-decoration: underline;
      }
    }
    .padding-top-10 {
      padding-top: 10px;
    }
    .padding-bottom-10 {
      padding-bottom: 10px;
    }
  }
  .margin-bottom-0 {
    margin-bottom: 0;
  }
}

@media screen and (min-width: 200px) and (max-width: 575px) {
  .reports-container {
    .reports-grid-container {
      .reports-grid-action {
        .reports-grid-search-container {
          width: 100%;
        }
      }
    }
  }
}

@media screen and (min-width: 576px) and (max-width: 767px) {
  .reports-container {
    .reports-grid-container {
      .reports-grid-action {
        .reports-grid-search-container {
          width: 45%;
        }
        .reports-grid-search-container {
          width: 55%;
        }
      }
    }
  }
}

@media only screen and (max-width: 768px) {
  .reports-container {
    .reports-grid-container {
      .reports-grid-action {
        .reports-grid-search-container {
          width: 100%;
        }
      }
    }
  }
}

.padding-right-16 {
  padding-right: 16px !important;
}

.report-heading {
  width: 100%;
  background: lightslategray;
  color: #fff;
  text-align: center;
  border-radius: 30px;
  padding: 15px 0;
  margin-bottom: 10px;
}

.default-reports {
  .table > thead > tr > th {
    white-space: unset;
  }
}

.table-border {
  border: 1px solid #ddd !important;
  font-size: 13px !important;
  th {
    border: 1px solid #ddd !important;
    color: #0059b3 !important;
    font-weight: bold;
    text-align: left !important;
    padding: 8px;
    td {
      border: 1px solid #ddd !important;
      vertical-align: middle !important;
    }
  }
  tr {
    border: 1px solid #ddd !important;
    background: #ffffff !important;
    td {
      border: 1px solid #ddd !important;
      text-align: right !important;
      vertical-align: middle !important;
      color: #0059b3 !important;
    }
  }
  .line-height-10 {
    line-height: 10px !important;
  }
  .prev-year-tm-budget-sales-vol {
    vertical-align: middle !important;
  }
  thead {
    tr {
      background: rgba(0, 0, 0, 0.1) !important;
    }
  }
  .pencil-square-icon {
    font-size: 19px !important;
    cursor: pointer !important;
    display: block !important;
    text-align: center !important;
  }
  .update-color {
    position: relative !important;
    color: #00cc3a !important;
  }
  .cancel-icon {
    color: red !important;
    position: relative !important;
  }
  .left-15 {
    left: 15px !important;
  }
  .left-25 {
    left: 25px !important;
  }
  .width-25 {
    width: 25% !important;
  }
  .width-30 {
    width: 30% !important;
  }
  .width-20 {
    width: 20%;
  }
  .background-total {
    background-color: #a22e2a !important;
    .total-font-color {
      color: #fff !important;
    }
    .text-right-value {
      text-align: right !important;
    }
  }
  .brands-header {
    th {
      white-space: unset;
    }
  }
  .row-color {
    font-weight: bold;
    background: lightslategray !important;
    th,
    td {
      color: #ffffff !important;
    }
  }
}

.overflow-auto {
  overflow: auto !important;
}

.confirmUserActiveContainer {
  @include confirmDialogueActiveInActive();
}

.modal-backdrop.fade {
  opacity: 0.6;
}

.confirmUserActiveContainer .fade {
  opacity: 1 !important;
}

.width-100 {
  width: 100%;
}

.disable-submit {
  opacity: 0.8;
  cursor: not-allowed;
  &:hover {
    opacity: 0.8;
    cursor: not-allowed;
  }
}
