import {
  Component,
  ElementRef,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
} from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { GlobalEvents } from "../../helpers/global.events";
import { AuthenticationHelper } from "../../helpers/authentication";
import { UserService } from "../../app-services/user-service";
import { AppConstant } from "../../constants/app.constant";
import { ZonesService } from "../../app-services/zones-service";
import { ActivatedRoute } from "@angular/router";
import { MatDialog } from "@angular/material/dialog";
import { NgxPaginationModule } from "ngx-pagination";
import { FilterComponent } from "../../shared/filter/filter.component";
import { NgIf, NgFor, NgClass } from "@angular/common";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";

@Component({
    selector: "nga-reports",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["reports.component.scss"],
    templateUrl: "reports.component.html",
    imports: [NgIf, FilterComponent, NgFor, NgClass, NgxPaginationModule],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class ReportsComponent {
  ismobileReports: boolean = false;
  isRegion: boolean = false;
  finYear: any = [];
  territoriesData: any = [];
  brandListPD: any = [];
  regions: any = [];
  zoneDetails: any = [];
  customersList: any = [];
  selectedBrandListReports: any = [];
  selectedCustomerList: any = [];
  lastYearSales: any = [];
  lastYearVolume: any = [];
  referenceBudgetSales: any = [];
  referenceBudgetVolume: any = [];
  custNamea: any = [];
  lastYearTotalCustSale: any = [];
  lastYearTotalCustVol: any = [];
  reffBudgetTotalCustSale: any = [];
  reffBudgetTotalCustVol: any = [];
  tmBudgetTotalCustSales: any = [];
  tmBudgetTotalCustVol: any = [];
  budgetGapSalesArr: any = [];
  budgetGapVolArr: any = [];
  tmBudgetSales: any = [];
  tmBudgetVol: any = [];
  isAdmin: boolean = false;
  isTM: boolean = false;
  isZM: boolean = false;
  showZone: boolean = true;
  showTerr: boolean = true;
  showCust: boolean = true;
  totalDifferentSales: any = {};
  defaultArr = [
    "Apr",
    "May",
    "Jun",
    "Quarter 1 Total",
    "Jul",
    "Aug",
    "Sep",
    "Quarter 2 Total",
    "Oct",
    "Nov",
    "Dec",
    "Quarter 3 Total",
    "Jan",
    "Feb",
    "Mar",
    "Quarter 4 Total",
  ];
  fyData: string = "";
  pCCode: string = "";
  pcName: any;
  zoneCode: any = "";
  viewToBePreview: string = "Brand";
  zoneName: any;
  regionCode: string = "";
  brandCode: string = "";
  customerCode: string = "";
  selectedterritory: any = "";
  modalHeading: string = "";
  nextYear: number;
  territoriesLength: any;
  customerCount: any;
  perPage: number = 10;
  currentPage: number = 1;
  territoryCode: any = "";
  custDropdownPDSettings: any = {
    text: "Select Customer",
    enableSearchFilter: true,
    classes: "myclass customer-dd-PD",
    labelKey: "name_code",
    singleSelection: true,
    enableFilterSelectAll: false,
    badgeShowLimit: 1,
    maxHeight: 230,
  };

  brandDropdownPDSettings: any = {
    text: "Select Brand",
    enableSearchFilter: true,
    classes: "myclass customer-dd-PD",
    labelKey: "name",
    singleSelection: true,
    enableFilterSelectAll: false,
    badgeShowLimit: 1,
    maxHeight: 230,
  };

  showOtherFilters: any = {
    showRadioFilters: false,
    showSearch: false,
    add: false,
    showReport: true,
    showdropdown1Filters: false,
    showdropdown2Filters: false,
    showSearchiconFilters: false,
  };

  @ViewChild("monthWiseData")
  monthWiseData!: TemplateRef<any>;
  @ViewChild("brandsMonthWiseData") brandsMonthWiseData!: TemplateRef<any>;
  @ViewChild("territoryMonthWiseData")
  territoryMonthWiseData!: TemplateRef<any>;
  constructor(
    public dialog: MatDialog,
    private routes: ActivatedRoute,
    private eRef: ElementRef,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private events: GlobalEvents,
    private userService: UserService,
    private zonesService: ZonesService
  ) {
    this.ismobileReports = window.innerWidth <= 1023 ? true : false;
    const date = new Date();
    const month = date.getMonth() + 1;
    if (month >= 4 && month <= 12) {
      this.nextYear = date.getFullYear() + 1;
    } else {
      this.nextYear = date.getFullYear();
    }

    this.isTM = AuthenticationHelper.getRoleID() === "2" ? true : false;
    this.isAdmin = AuthenticationHelper.getRoleID() === "1" ? true : false;
    this.isZM = AuthenticationHelper.getRoleID() === "4" ? true : false;
  }

  ngOnInit() {
    if (this.isZM || this.isTM) {
      this.profitCenter();
    }
    if (this.isTM) {
      this.selectedterritory = localStorage.getItem("territory_code");
    }
    if (this.isAdmin) {
      this.getFinancialYear();
      this.getCustCount();
    }
    this.getAllProduct();
  }

  profitCenter(): void {
    this.spinner.show();
    let loggedInUserID = AuthenticationHelper.getUserID();
    const data = {
      params: "byuserid",
      id: loggedInUserID,
    };
    this.userService.getProfitCenter(data).subscribe({
      next: (profitCenterData: any) => {
        this.pcName = profitCenterData.code === "DO1301" ? "AF" : "SWAL";
        this.pCCode = profitCenterData.code;
        if (this.pcName === "SWAL") {
          this.isRegion = true;
        }
        this.getUserZone();
      },
      error: () => {
        this.toastr.error(AppConstant.FINANCIAL_YEAR_FETCHING_ERROR);
        this.spinner.hide();
      },
    });
  }

  getUserZone(): void {
    let loggedInUserID = AuthenticationHelper.getUserID();
    const data = {
      params: "byuserid",
      id: loggedInUserID,
    };
    this.zonesService.getLoggedInUserZone(data).subscribe({
      next: (zoneData: any) => {
        this.zoneCode = zoneData.code;
        this.zoneName = zoneData.name;
        if (this.isZM) {
          this.events.setChangedContentTopText(
            "Reports for " + this.pcName + " of " + this.zoneName
          );
          this.showTerr = false;
        }
        if (this.isTM) {
          this.events.setChangedContentTopText("Reports for TM");
          this.showTerr = false;
        }

        this.getCustCount();
        this.getCustomers();
        this.getFinancialYear();
      },
      error: () => {
        this.toastr.error(AppConstant.FINANCIAL_YEAR_FETCHING_ERROR);
        this.spinner.hide();
      },
    });
  }

  zonalManagerSales(): void {
    this.spinner.show();
    const data = {
      view: this.viewToBePreview,
      profitCenter: this.pCCode,
      zone: this.zoneCode,
      customerCode: this.customerCode,
      brandCode: this.brandCode,
      territroyCode: this.selectedterritory,
      profitFinanceYearsId: this.fyData,
      pageLimit: this.perPage,
      currentPage: this.currentPage,
    };
    this.userService.getSales(data).subscribe({
      next: (response: any) => {
        this.lastYearSales = [];
        this.lastYearVolume = [];
        this.referenceBudgetSales = [];
        this.referenceBudgetVolume = [];
        this.tmBudgetSales = [];
        this.tmBudgetVol = [];

        this.lastYearTotalCustSale = [];
        this.lastYearTotalCustVol = [];
        this.reffBudgetTotalCustSale = [];
        this.reffBudgetTotalCustVol = [];
        this.tmBudgetTotalCustSales = [];
        this.tmBudgetTotalCustVol = [];
        this.budgetGapSalesArr = [];
        this.budgetGapVolArr = [];

        let totalValue = 0;
        if (this.viewToBePreview === "Default") {
          response.forEach((res: any) => {
            if (res && res.lastYearSales) {
              let prevYearSales: any = {
                apr: res.lastYearSales[0].aprSale / 100000,
                may: res.lastYearSales[0].maySale / 100000,
                jun: res.lastYearSales[0].junSale / 100000,
                jul: res.lastYearSales[0].julSale / 100000,
                aug: res.lastYearSales[0].augSale / 100000,
                sept: res.lastYearSales[0].sepSale / 100000,
                oct: res.lastYearSales[0].octSale / 100000,
                nov: res.lastYearSales[0].novSale / 100000,
                dec: res.lastYearSales[0].decSale / 100000,
                jan: res.lastYearSales[0].janSale / 100000,
                feb: res.lastYearSales[0].febSale / 100000,
                mar: res.lastYearSales[0].marSale / 100000,
              };
              Object.keys(prevYearSales).forEach((key) => {
                this.lastYearSales.push(prevYearSales[key]);
              });
              totalValue = this.totalSales(prevYearSales);
              this.totalDifferentSales["LYSalestotal"] = totalValue;
              totalValue = this.totalValue(
                prevYearSales["apr"],
                prevYearSales["may"],
                prevYearSales["jun"]
              );
              this.lastYearSales.splice(3, 0, totalValue);
              totalValue = this.totalValue(
                prevYearSales["jul"],
                prevYearSales["aug"],
                prevYearSales["sept"]
              );
              this.lastYearSales.splice(7, 0, totalValue);
              totalValue = this.totalValue(
                prevYearSales["oct"],
                prevYearSales["nov"],
                prevYearSales["dec"]
              );
              this.lastYearSales.splice(11, 0, totalValue);
              totalValue = this.totalValue(
                prevYearSales["jan"],
                prevYearSales["feb"],
                prevYearSales["mar"]
              );
              this.lastYearSales.splice(15, 0, totalValue);
            }

            if (res && res.lastYearVolume) {
              let prevYearVolume: any = {
                apr: res.lastYearVolume[0].aprVolume,
                may: res.lastYearVolume[0].mayVolume,
                jun: res.lastYearVolume[0].junVolume,
                jul: res.lastYearVolume[0].julVolume,
                aug: res.lastYearVolume[0].augVolume,
                sept: res.lastYearVolume[0].sepVolume,
                oct: res.lastYearVolume[0].octVolume,
                nov: res.lastYearVolume[0].novVolume,
                dec: res.lastYearVolume[0].decVolume,
                jan: res.lastYearVolume[0].janVolume,
                feb: res.lastYearVolume[0].febVolume,
                mar: res.lastYearVolume[0].marVolume,
              };
              Object.keys(prevYearVolume).forEach((key) => {
                this.lastYearVolume.push(prevYearVolume[key]);
              });
              totalValue = this.totalSales(prevYearVolume);
              this.totalDifferentSales["LYVolumetotal"] = totalValue;
              totalValue = this.totalValue(
                prevYearVolume["apr"],
                prevYearVolume["may"],
                prevYearVolume["jun"]
              );
              this.lastYearVolume.splice(3, 0, totalValue);
              totalValue = this.totalValue(
                prevYearVolume["jul"],
                prevYearVolume["aug"],
                prevYearVolume["sept"]
              );
              this.lastYearVolume.splice(7, 0, totalValue);
              totalValue = this.totalValue(
                prevYearVolume["oct"],
                prevYearVolume["nov"],
                prevYearVolume["dec"]
              );
              this.lastYearVolume.splice(11, 0, totalValue);
              totalValue = this.totalValue(
                prevYearVolume["jan"],
                prevYearVolume["feb"],
                prevYearVolume["mar"]
              );
              this.lastYearVolume.splice(15, 0, totalValue);
            }

            if (res && res.referencedBudgetValue) {
              let referenceBudget: any = {
                apr: res.referencedBudgetValue[0].aprSale / 100000,
                may: res.referencedBudgetValue[0].maySale / 100000,
                jun: res.referencedBudgetValue[0].junSale / 100000,
                jul: res.referencedBudgetValue[0].julSale / 100000,
                aug: res.referencedBudgetValue[0].augSale / 100000,
                sept: res.referencedBudgetValue[0].sepSale / 100000,
                oct: res.referencedBudgetValue[0].octSale / 100000,
                nov: res.referencedBudgetValue[0].novSale / 100000,
                dec: res.referencedBudgetValue[0].decSale / 100000,
                jan: res.referencedBudgetValue[0].janSale / 100000,
                feb: res.referencedBudgetValue[0].febSale / 100000,
                mar: res.referencedBudgetValue[0].marSale / 100000,
              };
              Object.keys(referenceBudget).forEach((key) => {
                this.referenceBudgetSales.push(referenceBudget[key]);
              });
              totalValue = this.totalSales(referenceBudget);
              this.totalDifferentSales["ReferenceBudgetSalestotal"] =
                totalValue;
              totalValue = this.totalValue(
                referenceBudget["apr"],
                referenceBudget["may"],
                referenceBudget["jun"]
              );
              this.referenceBudgetSales.splice(3, 0, totalValue);
              totalValue = this.totalValue(
                referenceBudget["jul"],
                referenceBudget["aug"],
                referenceBudget["sept"]
              );
              this.referenceBudgetSales.splice(7, 0, totalValue);
              totalValue = this.totalValue(
                referenceBudget["oct"],
                referenceBudget["nov"],
                referenceBudget["dec"]
              );
              this.referenceBudgetSales.splice(11, 0, totalValue);
              totalValue = this.totalValue(
                referenceBudget["jan"],
                referenceBudget["feb"],
                referenceBudget["mar"]
              );
              this.referenceBudgetSales.splice(15, 0, totalValue);
            }

            if (res && res.referencedBudgetVolume) {
              let referenceBudgetVol: any = {
                apr: res.referencedBudgetVolume[0].aprVolume,
                may: res.referencedBudgetVolume[0].mayVolume,
                jun: res.referencedBudgetVolume[0].junVolume,
                jul: res.referencedBudgetVolume[0].julVolume,
                aug: res.referencedBudgetVolume[0].augVolume,
                sept: res.referencedBudgetVolume[0].sepVolume,
                oct: res.referencedBudgetVolume[0].octVolume,
                nov: res.referencedBudgetVolume[0].novVolume,
                dec: res.referencedBudgetVolume[0].decVolume,
                jan: res.referencedBudgetVolume[0].janVolume,
                feb: res.referencedBudgetVolume[0].febVolume,
                mar: res.referencedBudgetVolume[0].marVolume,
              };
              Object.keys(referenceBudgetVol).forEach((key) => {
                this.referenceBudgetVolume.push(referenceBudgetVol[key]);
              });
              totalValue = this.totalSales(referenceBudgetVol);
              this.totalDifferentSales["ReferenceBudgetVoltotal"] = totalValue;
              totalValue = this.totalValue(
                referenceBudgetVol["apr"],
                referenceBudgetVol["may"],
                referenceBudgetVol["jun"]
              );
              this.referenceBudgetVolume.splice(3, 0, totalValue);
              totalValue = this.totalValue(
                referenceBudgetVol["jul"],
                referenceBudgetVol["aug"],
                referenceBudgetVol["sept"]
              );
              this.referenceBudgetVolume.splice(7, 0, totalValue);
              totalValue = this.totalValue(
                referenceBudgetVol["oct"],
                referenceBudgetVol["nov"],
                referenceBudgetVol["dec"]
              );
              this.referenceBudgetVolume.splice(11, 0, totalValue);
              totalValue = this.totalValue(
                referenceBudgetVol["jan"],
                referenceBudgetVol["feb"],
                referenceBudgetVol["mar"]
              );
              this.referenceBudgetVolume.splice(15, 0, totalValue);
            }

            if (res && res.tmBudgetValue) {
              let tmBudget: any = {
                apr: res.tmBudgetValue[0].aprSale / 100000,
                may: res.tmBudgetValue[0].maySale / 100000,
                jun: res.tmBudgetValue[0].junSale / 100000,
                jul: res.tmBudgetValue[0].julSale / 100000,
                aug: res.tmBudgetValue[0].augSale / 100000,
                sept: res.tmBudgetValue[0].sepSale / 100000,
                oct: res.tmBudgetValue[0].octSale / 100000,
                nov: res.tmBudgetValue[0].novSale / 100000,
                dec: res.tmBudgetValue[0].decSale / 100000,
                jan: res.tmBudgetValue[0].janSale / 100000,
                feb: res.tmBudgetValue[0].febSale / 100000,
                mar: res.tmBudgetValue[0].marSale / 100000,
              };
              Object.keys(tmBudget).forEach((key) => {
                this.tmBudgetSales.push(tmBudget[key]);
              });
              totalValue = this.totalSales(tmBudget);
              this.totalDifferentSales["tmBudgetSalestotal"] = totalValue;
              totalValue = this.totalValue(
                tmBudget["apr"],
                tmBudget["may"],
                tmBudget["jun"]
              );
              this.tmBudgetSales.splice(3, 0, totalValue);
              totalValue = this.totalValue(
                tmBudget["jul"],
                tmBudget["aug"],
                tmBudget["sept"]
              );
              this.tmBudgetSales.splice(7, 0, totalValue);
              totalValue = this.totalValue(
                tmBudget["oct"],
                tmBudget["nov"],
                tmBudget["dec"]
              );
              this.tmBudgetSales.splice(11, 0, totalValue);
              totalValue = this.totalValue(
                tmBudget["jan"],
                tmBudget["feb"],
                tmBudget["mar"]
              );
              this.tmBudgetSales.splice(15, 0, totalValue);
            }

            if (res && res.tmBudgetVolume) {
              let tmBudgetVol: any = {
                apr: res.tmBudgetVolume[0].aprVolume,
                may: res.tmBudgetVolume[0].mayVolume,
                jun: res.tmBudgetVolume[0].junVolume,
                jul: res.tmBudgetVolume[0].julVolume,
                aug: res.tmBudgetVolume[0].augVolume,
                sept: res.tmBudgetVolume[0].sepVolume,
                oct: res.tmBudgetVolume[0].octVolume,
                nov: res.tmBudgetVolume[0].novVolume,
                dec: res.tmBudgetVolume[0].decVolume,
                jan: res.tmBudgetVolume[0].janVolume,
                feb: res.tmBudgetVolume[0].febVolume,
                mar: res.tmBudgetVolume[0].marVolume,
              };
              Object.keys(tmBudgetVol).forEach((key) => {
                this.tmBudgetVol.push(tmBudgetVol[key]);
              });
              totalValue = this.totalSales(tmBudgetVol);
              this.totalDifferentSales["tmBudgetVoltotal"] = totalValue;
              totalValue = this.totalValue(
                tmBudgetVol["apr"],
                tmBudgetVol["may"],
                tmBudgetVol["jun"]
              );
              this.tmBudgetVol.splice(3, 0, totalValue);
              totalValue = this.totalValue(
                tmBudgetVol["jul"],
                tmBudgetVol["aug"],
                tmBudgetVol["sept"]
              );
              this.tmBudgetVol.splice(7, 0, totalValue);
              totalValue = this.totalValue(
                tmBudgetVol["oct"],
                tmBudgetVol["nov"],
                tmBudgetVol["dec"]
              );
              this.tmBudgetVol.splice(11, 0, totalValue);
              totalValue = this.totalValue(
                tmBudgetVol["jan"],
                tmBudgetVol["feb"],
                tmBudgetVol["mar"]
              );
              this.tmBudgetVol.splice(15, 0, totalValue);
            }
          });
        }
        if (
          this.viewToBePreview === "Customer" ||
          this.viewToBePreview === "Brand" ||
          this.viewToBePreview === "Territory"
        ) {
          response.forEach((res: any) => {
            this.lastYearTotalCustSale = [];
            this.lastYearTotalCustVol = [];
            this.reffBudgetTotalCustSale = [];
            this.reffBudgetTotalCustVol = [];
            this.tmBudgetTotalCustSales = [];
            this.tmBudgetTotalCustVol = [];
            this.budgetGapSalesArr = [];
            this.budgetGapVolArr = [];
            this.custNamea = [];

            if (res && res.lastYearSales) {
              let prevYearSales: any = {
                apr: res.lastYearSales[0].aprSale / 100000,
                may: res.lastYearSales[0].maySale / 100000,
                jun: res.lastYearSales[0].junSale / 100000,
                jul: res.lastYearSales[0].julSale / 100000,
                aug: res.lastYearSales[0].augSale / 100000,
                sept: res.lastYearSales[0].sepSale / 100000,
                oct: res.lastYearSales[0].octSale / 100000,
                nov: res.lastYearSales[0].novSale / 100000,
                dec: res.lastYearSales[0].decSale / 100000,
                jan: res.lastYearSales[0].janSale / 100000,
                feb: res.lastYearSales[0].febSale / 100000,
                mar: res.lastYearSales[0].marSale / 100000,
              };
              this.lastYearSales.push(prevYearSales);
              this.lastYearSales.forEach((lastYearData: any) => {
                let salesData = {
                  total:
                    lastYearData["apr"] +
                    lastYearData["may"] +
                    lastYearData["jun"] +
                    lastYearData["jul"] +
                    lastYearData["aug"] +
                    lastYearData["sept"] +
                    lastYearData["oct"] +
                    lastYearData["nov"] +
                    lastYearData["dec"] +
                    lastYearData["jan"] +
                    lastYearData["feb"] +
                    lastYearData["mar"],
                };
                this.lastYearTotalCustSale.push(salesData);
              });
            }
            if (res && res.lastYearVolume) {
              let prevYearVolume = {
                apr: res.lastYearVolume[0].aprVolume,
                may: res.lastYearVolume[0].mayVolume,
                jun: res.lastYearVolume[0].junVolume,
                jul: res.lastYearVolume[0].julVolume,
                aug: res.lastYearVolume[0].augVolume,
                sept: res.lastYearVolume[0].sepVolume,
                oct: res.lastYearVolume[0].octVolume,
                nov: res.lastYearVolume[0].novVolume,
                dec: res.lastYearVolume[0].decVolume,
                jan: res.lastYearVolume[0].janVolume,
                feb: res.lastYearVolume[0].febVolume,
                mar: res.lastYearVolume[0].marVolume,
              };
              this.lastYearVolume.push(prevYearVolume);
              this.lastYearVolume.forEach((lastYearVol: any) => {
                let salesData = {
                  total:
                    lastYearVol["apr"] +
                    lastYearVol["may"] +
                    lastYearVol["jun"] +
                    lastYearVol["jul"] +
                    lastYearVol["aug"] +
                    lastYearVol["sept"] +
                    lastYearVol["oct"] +
                    lastYearVol["nov"] +
                    lastYearVol["dec"] +
                    lastYearVol["jan"] +
                    lastYearVol["feb"] +
                    lastYearVol["mar"],
                };
                this.lastYearTotalCustVol.push(salesData);
              });
            }
            if (res && res.referencedBudgetValue) {
              let referenceBudget = {
                apr: res.referencedBudgetValue[0].aprSale / 100000,
                may: res.referencedBudgetValue[0].maySale / 100000,
                jun: res.referencedBudgetValue[0].junSale / 100000,
                jul: res.referencedBudgetValue[0].julSale / 100000,
                aug: res.referencedBudgetValue[0].augSale / 100000,
                sept: res.referencedBudgetValue[0].sepSale / 100000,
                oct: res.referencedBudgetValue[0].octSale / 100000,
                nov: res.referencedBudgetValue[0].novSale / 100000,
                dec: res.referencedBudgetValue[0].decSale / 100000,
                jan: res.referencedBudgetValue[0].janSale / 100000,
                feb: res.referencedBudgetValue[0].febSale / 100000,
                mar: res.referencedBudgetValue[0].marSale / 100000,
              };

              this.referenceBudgetSales.push(referenceBudget);
              this.referenceBudgetSales.forEach((reffBudgetSales: any) => {
                let salesData = {
                  total:
                    reffBudgetSales["apr"] +
                    reffBudgetSales["may"] +
                    reffBudgetSales["jun"] +
                    reffBudgetSales["jul"] +
                    reffBudgetSales["aug"] +
                    reffBudgetSales["sept"] +
                    reffBudgetSales["oct"] +
                    reffBudgetSales["nov"] +
                    reffBudgetSales["dec"] +
                    reffBudgetSales["jan"] +
                    reffBudgetSales["feb"] +
                    reffBudgetSales["mar"],
                };
                this.reffBudgetTotalCustSale.push(salesData);
              });
            }
            if (res && res.referencedBudgetVolume) {
              let referenceBudgetVol = {
                apr: res.referencedBudgetVolume[0].aprVolume,
                may: res.referencedBudgetVolume[0].mayVolume,
                jun: res.referencedBudgetVolume[0].junVolume,
                jul: res.referencedBudgetVolume[0].julVolume,
                aug: res.referencedBudgetVolume[0].augVolume,
                sept: res.referencedBudgetVolume[0].sepVolume,
                oct: res.referencedBudgetVolume[0].octVolume,
                nov: res.referencedBudgetVolume[0].novVolume,
                dec: res.referencedBudgetVolume[0].decVolume,
                jan: res.referencedBudgetVolume[0].janVolume,
                feb: res.referencedBudgetVolume[0].febVolume,
                mar: res.referencedBudgetVolume[0].marVolume,
              };

              this.referenceBudgetVolume.push(referenceBudgetVol);
              this.referenceBudgetVolume.forEach((reffBudgetVol: any) => {
                let salesData = {
                  total:
                    reffBudgetVol["apr"] +
                    reffBudgetVol["may"] +
                    reffBudgetVol["jun"] +
                    reffBudgetVol["jul"] +
                    reffBudgetVol["aug"] +
                    reffBudgetVol["sept"] +
                    reffBudgetVol["oct"] +
                    reffBudgetVol["nov"] +
                    reffBudgetVol["dec"] +
                    reffBudgetVol["jan"] +
                    reffBudgetVol["feb"] +
                    reffBudgetVol["mar"],
                };
                this.reffBudgetTotalCustVol.push(salesData);
              });
            }

            if (res && res.tmBudgetValue) {
              let tmBudget = {
                apr: res.tmBudgetValue[0].aprSale / 100000,
                may: res.tmBudgetValue[0].maySale / 100000,
                jun: res.tmBudgetValue[0].junSale / 100000,
                jul: res.tmBudgetValue[0].julSale / 100000,
                aug: res.tmBudgetValue[0].augSale / 100000,
                sept: res.tmBudgetValue[0].sepSale / 100000,
                oct: res.tmBudgetValue[0].octSale / 100000,
                nov: res.tmBudgetValue[0].novSale / 100000,
                dec: res.tmBudgetValue[0].decSale / 100000,
                jan: res.tmBudgetValue[0].janSale / 100000,
                feb: res.tmBudgetValue[0].febSale / 100000,
                mar: res.tmBudgetValue[0].marSale / 100000,
              };

              this.tmBudgetSales.push(tmBudget);
              this.tmBudgetSales.forEach((tmBudgetSale: any) => {
                let salesData = {
                  total:
                    tmBudgetSale["apr"] +
                    tmBudgetSale["may"] +
                    tmBudgetSale["jun"] +
                    tmBudgetSale["jul"] +
                    tmBudgetSale["aug"] +
                    tmBudgetSale["sept"] +
                    tmBudgetSale["oct"] +
                    tmBudgetSale["nov"] +
                    tmBudgetSale["dec"] +
                    tmBudgetSale["jan"] +
                    tmBudgetSale["feb"] +
                    tmBudgetSale["mar"],
                };
                this.tmBudgetTotalCustSales.push(salesData);
              });
            }

            if (res && res.tmBudgetVolume) {
              let tmBudgetVol = {
                apr: res.tmBudgetVolume[0].aprVolume,
                may: res.tmBudgetVolume[0].mayVolume,
                jun: res.tmBudgetVolume[0].junVolume,
                jul: res.tmBudgetVolume[0].julVolume,
                aug: res.tmBudgetVolume[0].augVolume,
                sept: res.tmBudgetVolume[0].sepVolume,
                oct: res.tmBudgetVolume[0].octVolume,
                nov: res.tmBudgetVolume[0].novVolume,
                dec: res.tmBudgetVolume[0].decVolume,
                jan: res.tmBudgetVolume[0].janVolume,
                feb: res.tmBudgetVolume[0].febVolume,
                mar: res.tmBudgetVolume[0].marVolume,
              };
              this.tmBudgetVol.push(tmBudgetVol);

              this.tmBudgetVol.forEach((tmBudgetVolData: any) => {
                let salesData = {
                  total:
                    tmBudgetVolData["apr"] +
                    tmBudgetVolData["may"] +
                    tmBudgetVolData["jun"] +
                    tmBudgetVolData["jul"] +
                    tmBudgetVolData["aug"] +
                    tmBudgetVolData["sept"] +
                    tmBudgetVolData["oct"] +
                    tmBudgetVolData["nov"] +
                    tmBudgetVolData["dec"] +
                    tmBudgetVolData["jan"] +
                    tmBudgetVolData["feb"] +
                    tmBudgetVolData["mar"],
                };
                this.tmBudgetTotalCustVol.push(salesData);
              });
            }
          });
          this.lastYearTotalCustSale.forEach((custTotalSale: any) => {
            totalValue += custTotalSale.total;
          });
          this.totalDifferentSales["LYSalestotal"] = totalValue;
          totalValue = 0;
          this.lastYearTotalCustVol.forEach((custTotalVol: any) => {
            totalValue += custTotalVol.total;
          });
          this.totalDifferentSales["LYVolumetotal"] = totalValue;
          totalValue = 0;
          this.reffBudgetTotalCustSale.forEach(
            (reffBudgetcustTotalSale: any) => {
              totalValue += reffBudgetcustTotalSale.total;
            }
          );
          this.totalDifferentSales["ReferenceBudgetSalestotal"] = totalValue;
          totalValue = 0;
          this.reffBudgetTotalCustVol.forEach((reffBudgetcustTotalVol: any) => {
            totalValue += reffBudgetcustTotalVol.total;
          });
          this.totalDifferentSales["ReferenceBudgetVoltotal"] = totalValue;
          totalValue = 0;
          this.tmBudgetTotalCustSales.forEach((tmBudgetcustTotalSale: any) => {
            totalValue += tmBudgetcustTotalSale.total;
          });
          this.totalDifferentSales["tmBudgetSalestotal"] = totalValue;

          totalValue = 0;
          this.tmBudgetTotalCustVol.forEach((tmBudgetcustTotalVol: any) => {
            totalValue += tmBudgetcustTotalVol.total;
          });
          this.totalDifferentSales["tmBudgetVoltotal"] = totalValue;
          for (let i = 0; i < this.tmBudgetTotalCustSales.length; i++) {
            let budgetGapSale = {
              total:
                this.tmBudgetTotalCustSales[i].total -
                this.reffBudgetTotalCustSale[i].total,
            };
            let budgetGapVol = {
              total:
                this.tmBudgetTotalCustVol[i].total -
                this.reffBudgetTotalCustVol[i].total,
            };
            this.budgetGapSalesArr.push(budgetGapSale);
            this.budgetGapVolArr.push(budgetGapVol);
          }
        }
        this.spinner.hide();
      },

      error: () => {
        this.spinner.hide();
      },
    });
  }

  totalSales(valueObj: any): number {
    let total =
      valueObj.apr +
      valueObj.may +
      valueObj.jun +
      valueObj.jul +
      valueObj.aug +
      valueObj.sept +
      valueObj.oct +
      valueObj.nov +
      valueObj.dec +
      valueObj.jan +
      valueObj.feb +
      valueObj.mar;
    return total;
  }

  totalValue(mon1: any, mon2: any, mon3: any): number {
    let total = mon1 + mon2 + mon3;
    return total;
  }

  selectedProfitCenter(event: any): void {
    this.pCCode = event.target.value;
    if (this.pCCode != "") {
      this.showZone = false;
    }
    this.zoneCode = "";
    this.territoryCode = "";
    this.getAllZones();
    this.getAllProduct();
  }

  selectedZone(event: any): void {
    this.zoneCode = event.target.value;
    this.territoryCode = "";
    if (this.zoneCode != "") {
      this.showTerr = false;
    }
    const territoryData = {
      profitCenter: this.pCCode,
      zoneCode: this.zoneCode,
    };
    this.getTerritories(territoryData);
  }

  getAllZones(): void {
    this.spinner.show();
    this.zonesService.getAllZonesByPC().subscribe({
      next: (zonesDetails: any) => {
        this.zoneDetails = [];
        zonesDetails.forEach((zoneInfo: any) => {
          const zoneInfoObj = {
            code: zoneInfo.code,
            id: zoneInfo.id,
            name: zoneInfo.name,
          };
          this.zoneDetails.push(zoneInfoObj);
        });
        this.spinner.hide();
      },

      error: () => {
        this.toastr.error(AppConstant.ZONE_FETCH_ERROR);
        this.spinner.hide();
      },
    });
  }

  getCustCount() {
    let data = {
      zone: this.zoneCode,
      profitCenter: this.pCCode,
    };
    this.userService.customerCount(data).subscribe((count: any) => {
      this.customerCount = count.customerCount;
    });
  }

  getCustomers(): void {
    this.userService
      .getAllCustomersByPCCodeandZone(
        this.territoryCode,
        this.zoneCode,
        this.pCCode
      )
      .subscribe((customers: any) => {
        this.customersList = [];
        customers.forEach((res: any) => {
          let custObj = {
            id: res.customerId,
            code: res.customerCode,
            name: res.customerName,
            name_code: res.customerName + res.customerCode,
          };
          this.customersList.push(custObj);
        });
        this.showCust = false;
      });
  }

  reportView(event: any): void {
    this.viewToBePreview = event.target.value;
    this.zonalManagerSales();
  }

  getAllProduct(): any {
    let data = {};
    data = {
      profitCenter: this.pCCode,
    };
    if (!this.isAdmin) {
      data = {
        profitCenter: AuthenticationHelper.getProfitCenter(),
      };
    }
    this.userService.getBrands().subscribe({
      next: (brandsData: any) => {
        this.brandListPD = [];
        brandsData.forEach((brandsInfo: any) => {
          const brandInfoObj = {
            id: brandsInfo.id,
            name: brandsInfo.name,
            code: brandsInfo.code,
          };
          this.brandListPD.push(brandInfoObj);
        });
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.BRANDS_FETCHING_ERROR);
        this.spinner.hide();
      },
    });
  }

  getFinancialYear(): void {
    let loggedInUserID = AuthenticationHelper.getUserID();
    const data = {
      params: "byUserId",
      id: loggedInUserID,
    };
    const territoryData = {
      profitCenter: this.pCCode,
      zoneCode: this.zoneCode,
    };
    this.userService.getFinancialYear(data).subscribe({
      next: (financialYearData: any) => {
        this.finYear = [];
        financialYearData.forEach((financialYearInfo: any) => {
          let tmBudgetInfoObj = {
            id: financialYearInfo.startDate.slice(0, 4),
            FY:
              financialYearInfo.startDate.slice(0, 4) +
              "-" +
              financialYearInfo.endDate.slice(2, 4),
            show: this.isShowFY(
              financialYearInfo.startDate.slice(0, 4) +
                "-" +
                financialYearInfo.endDate.slice(2, 4)
            ),
          };
          if (tmBudgetInfoObj.show) {
            this.fyData = tmBudgetInfoObj.id;
          }
          this.finYear.push(tmBudgetInfoObj);
        });
        if (this.isZM) {
          this.getTerritories(territoryData);
        }
        if (this.isAdmin) {
          this.events.setChangedContentTopText("Reports");
        }
        this.zonalManagerSales();
      },
      error: () => {
        this.toastr.error(AppConstant.BRAND_SKU_ERROR);
        this.spinner.hide();
      },
    });
  }

  selectedTerritory(event: any): void {
    this.selectedterritory = event.target.value;
    this.selectedBrandListReports = [];
    this.selectedCustomerList = [];
    this.customerCode = "";
    this.brandCode = "";
    this.zonalManagerSales();
    this.getCustomers();
  }

  isShowFY(FY: any): boolean {
    const FYear = FY.slice(0, 4);
    if (FYear == this.nextYear) {
      return true;
    } else {
      return false;
    }
  }

  getTerritories(res: any): void {
    this.userService.getTerritoryZonalManager(res).subscribe({
      next: (territoriesData: any) => {
        this.territoriesData = [];
        territoriesData.forEach((territoryInfo: any) => {
          const territoryInfoObj = {
            id: territoryInfo.id,
            name: territoryInfo.name,
            code: territoryInfo.code,
          };
          this.territoriesData.push(territoryInfoObj);
        });
        this.territoriesLength = this.territoriesData.length;
        this.showTerr = false;
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.TERRITORY_FETCHING_ERROR);
        this.spinner.hide();
      },
    });
  }

  viewMonthWise(val: any): void {
    this.modalHeading = val;
    this.dialog.open(this.monthWiseData, {
      width: "500px",
      height: "250px",
      disableClose: false,
      panelClass: "confirm-dialog-container",
      data: "",
      hasBackdrop: false,
    });
  }

  brandsViewMonthWise(val: any): void {
    this.modalHeading = val;
    this.dialog.open(this.brandsMonthWiseData, {
      width: "500px",
      height: "250px",
      disableClose: false,
      panelClass: "confirm-dialog-container",
      data: "",
      hasBackdrop: false,
    });
  }

  territoryViewMonthWise(val: any): void {
    this.modalHeading = val;
    this.dialog.open(this.territoryMonthWiseData, {
      width: "500px",
      height: "250px",
      disableClose: false,
      panelClass: "confirm-dialog-container",
      data: "",
      hasBackdrop: false,
    });
  }

  getPageData(event: any): void {
    this.currentPage = event;
    this.zonalManagerSales();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      window.scrollTo(0, 0);
    }, 0);
  }

  getFixedValueSale(value: any): any {
    return value ? value.toFixed(4) : 0;
  }

  getFixedValueVolume(vol: any): any {
    return vol ? vol.toFixed(2) : 0;
  }

  onWindowResizeTMBudget(): void {
    this.ismobileReports = window.innerWidth <= 1023 ? true : false;
  }
}
