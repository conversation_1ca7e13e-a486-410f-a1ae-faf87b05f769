import { waitForAsync, ComponentFixture, TestBed} from '@angular/core/testing';
import {ResetPassword} from './reset-password.component';

describe('ResetPassword', () => {
    let component: ResetPassword;
    let fixture: ComponentFixture<ResetPassword>;

    beforeEach( waitForAsync(() => {
        TestBed.configureTestingModule({
    imports: [ResetPassword]
}).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(ResetPassword);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});