import { Component, ViewEncapsulation } from "@angular/core";
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from "@angular/forms";
import { Router } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import {
  BaMenuService,
  BaThemePreloader,
  BaThemeSpinner,
} from "../../theme/services";
import { EqualPasswordsValidator } from "../../theme/validators";
import { AppConstant } from "../../constants/app.constant";
import { NgClass, NgIf } from "@angular/common";

@Component({
    selector: "reset-password",
    encapsulation: ViewEncapsulation.None,
    templateUrl: "reset-password.html",
    styleUrls: ["reset-password.scss"],
    imports: [FormsModule, ReactiveFormsModule, NgClass, NgIf],
    providers: [BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class ResetPassword {
  form: FormGroup;
  loyaltyImage: any = AppConstant.LOYALTY_IMAGE;
  submitted: boolean = false;

  constructor(
    fb: FormBuilder,
    private router: Router,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner
  ) {
    this.form = fb.group({
      confirmResetCode: ["", Validators.compose([Validators.required])],
      passwords: fb.group(
        {
          password: [
            "",
            Validators.compose([Validators.required, Validators.minLength(8)]),
          ],
          confirmPassword: [
            "",
            Validators.compose([Validators.required, Validators.minLength(8)]),
          ],
        },
        {
          validator: EqualPasswordsValidator.validate(
            "password",
            "confirmPassword"
          ),
        }
      ),
    });
  }
  ngAfterViewInit() {
    this.spinner.hide();
  }

  navigateToHome() {
    this.router.navigate([""]);
  }
  navigateToSignIn() {
    this.router.navigate(["/login"]);
  }
}
