<div class="reset-password-container">
  <div class="row reset-password-segment">
    <div class="col-md-12 reset-div-style">
      <div class="reset-title reset-div-style">
        <img [src]="loyaltyImage" (click)="navigateToHome()" />
      </div>
    </div>
    <div class="col-md-12 signup">
      <div class="sign-up-text">Reset Password</div>
      <form [formGroup]="form" class="form-horizontal">
        <div
          class="form-group row col-md-12 input-button email-div reset-div-style"
          [ngClass]="{'has-error': (!form.get('confirmResetCode')?.valid && form.get('confirmResetCode')?.touched), 'has-success': (form.get('confirmResetCode')?.valid && form.get('confirmResetCode')?.touched)}"
        >
          <div class="input-group sign-up-input">
            <span class="input-group-addon" id="basic-addon"
              ><i class="fa fa-star" aria-hidden="true"></i
            ></span>
            <input
              #confirmationCode
              formControlName="confirmResetCode"
              type="text"
              class="form-control inputbox right-border"
              id="inputFirstName"
              placeholder="Enter Code"
              autofocus
              tabindex="1"
            />
          </div>
          <div class="sign-up-error">
            <span
              *ngIf="!form.get('confirmResetCode')?.valid && form.get('confirmResetCode')?.touched"
              class="help-block sub-little-error confpass"
              >Confirmation code is required.</span
            >
          </div>
        </div>

        <div class="reset-div-style" formGroupName="passwords">
          <div
            class="form-group row col-md-12 input-button passwordtext reset-div-style"
            [ngClass]="{'has-error': (!form.get('passwords')?.get('password')?.valid && form.get('passwords')?.get('password')?.touched), 'has-success': (form.get('passwords')?.get('password')?.valid && form.get('passwords')?.get('password')?.touched)}"
          >
            <div class="input-group sign-up-input">
              <span class="input-group-addon" id="basic-addon5"
                ><i class="fa fa-key" aria-hidden="true"></i
              ></span>
              <input
                formControlName="password"
                type="password"
                class="form-control inputbox right-border"
                id="inputPassword3"
                placeholder="New password"
                autocomplete="off"
                maxlength="16"
                tabindex="2"
              />
            </div>
            <div class="sign-up-error">
              <span
                *ngIf="!form.get('passwords')?.get('password')?.valid && form.get('passwords')?.get('password')?.touched"
                class="help-block sub-little-error confpass"
                >Password must be 8 characters long.</span
              >
            </div>
          </div>
          <div
            class="form-group row col-md-12 input-button passwordtext reset-div-style"
            [ngClass]="{'has-error': !form.get('passwords')?.valid && (form.get('passwords')?.get('password')?.touched && form.get('passwords')?.get('confirmPassword')?.touched), 'has-success': (form.get('passwords')?.get('confirmPassword')?.valid && form.get('passwords')?.get('confirmPassword')?.touched)}"
          >
            <div class="input-group sign-up-input">
              <span class="input-group-addon" id="basic-addon6"
                ><i class="fa fa-key" aria-hidden="true"></i
              ></span>
              <input
                formControlName="confirmPassword"
                type="password"
                class="form-control inputbox right-border"
                id="inputPassword4"
                placeholder="Confirm Password"
                autocomplete="off"
                maxlength="16"
                tabindex="3"
              />
            </div>
            <div class="password-error-message">
              <span
                *ngIf="!form.get('passwords')?.valid && (form.get('passwords')?.get('password')?.touched && form.get('passwords')?.get('confirmPassword')?.touched)"
                class="help-block sub-little-text confpass"
                >Password doesn't match.</span
              >
            </div>
          </div>
        </div>

        <div
          class="form-group row col-md-12 input-button submit-button reset-div-style"
        >
          <input
            [ngClass]="{'disable-submit' : (!form.valid || submitted) }"
            [disabled]="!form.valid || submitted"
            type="submit"
            class="btn-style signUpButton"
            value="RESET"
            tabindex="4"
          />
        </div>
      </form>
      <div class="col-md-12 already-have-account reset-div-style">
        <div class="col-md-12 reset-div-style sign-up-footer">
          <span>Already have an account? </span>
          <a
            (keyup.enter)="navigateToSignIn()"
            (click)="navigateToSignIn()"
            tabindex="5"
            ><span class="forgot-password">Sign In</span></a
          >
        </div>
      </div>
    </div>
  </div>
</div>
