<div class="app-container">
  <div class="app-grid-container">
    <div class="app-grid-data-parent-container">
      <div id="foo" class="app-grid-data-container">
        <div class="history-filter-container">
          <div class="left-column second-left-column">
            <div class="main-campaign">
              <div class="panel">
                <div class="panel-body">
                  <div class="wizard">
                    <span class="table-heading"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="right-column">
            <div class="input-group">
              <!-- Improved search input with better cross-browser support -->
              <div class="search-input">
                <div class="input-group-add">
                  <i class="fa fa-search" aria-hidden="true"></i>
                  <input
                    #searchBox
                    type="text"
                    class="search-field"
                    placeholder="Type to search"
                    [(ngModel)]="model"
                    (ngModelChange)="onSearch($event)"
                    (keypress)="funRestSearchPrevent($event)"
                  />
                  <span *ngIf="model && model.length > 0" (click)="clearSearch()">
                    <img
                      title="Clear"
                      src="../../../assets/img/icons8-cancel-50.png"
                      alt="Clear search"
                    />
                  </span>
                </div>
              </div>
              <div class="filter-button">
                <button class="add custom-button" title="Filter" (click)="filterDropdown($event)">
                  <i class="fa fa-filter export-icon"></i>
                </button>
              </div>
              <div class="export-button">
                <button class="add" (click)="exportAllRedeemedAmountHistoryData()" title="Export">
                  <i class="fa fa-share-square-o export-icon"></i>
                </button>
              </div>
              <div class="add-btn-container" *ngIf="isAdmin">
                <button class="addSchemeButton" (click)="openAddSlabDialog($event)" >
                  <img width="18px" src="../../../assets/img/uil_invoice.svg" alt="Upload invoices" />
                  {{ "Upload Invoice" }}
                </button>
              </div>
            </div>
          </div>

        </div>
        <div class="app-table">
          <dynamic-table [tableHeads]="tableHead" [tableData]="rewardsHistoryData"
          [tableConfiguration]="configurationSettings" [tableColName]="tableColName"
          (viewProduct)="viewProduct($event)" (onViewInvoice)="onViewInvoice($event)"
          (onDownloadInvioce)="onDownloadInvoice($event)"
          (pageChange)="getPageData($event)" [showIndex]="showIndex" (emitInvoiceEdit)="emitInvoiceEdit($event)" (rejectInvoice)="rejectInvoice($event)">
        </dynamic-table>
        </div>
        <div class="no-result" *ngIf="isMap && !rewardsHistoryData.length">
          No data found
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #filterMenuDailog>
  <div class="filter-main-menu-container">
    <div class="filter-menu-heading">
      <span class="filter-title-size">Filter By</span>
    </div>
    <div class="history-filter-menu-container">
       <div class="area-filter-container">
        <label> Region </label>
         <angular2-multiselect [data]="regionDataList" [(ngModel)]="regionValue"
                  [settings]="regionDropdownSettings" (onSelect)="selectRegion($event)"
                  (onDeSelect)="deselectionRegion($event)" (onDeSelectAll)="deselectionAllRegion($event)"
                  [ngModelOptions]="{ standalone: true }">
                </angular2-multiselect>
      </div>
       <div class="area-filter-container">
        <label> Zone </label>
         <angular2-multiselect [data]="zoneDataList" [(ngModel)]="zoneValue" [settings]="zoneDropdownSettings"
                  (onSelect)="selectZone($event)" (onDeSelect)="deselectionZone($event)"
                  (onDeSelectAll)="deselectionAllZone($event)" [ngModelOptions]="{ standalone: true }">
                </angular2-multiselect>
      </div>
      <div class="area-filter-container">
        <label> Invoice Status</label>
        <angular2-multiselect [data]="redeemedMethodDetails"
        class="agreement-status"
        [(ngModel)]="redeemedMethod"
        [settings]="redeemedMethodSeasonDropdownSettings"
        (onSelect)="onRedeemedMethodHistorySelected($event)"
        (onDeSelect)="onRedeemedMethodHistoryDeSelected($event)"
        [ngModelOptions]="{ standalone: true }">
        </angular2-multiselect>
      </div>
    </div>
    <div class="button-container">
      <button type="button" class="btn-cancel" (click)="clearFilter()">
        {{ "Clear" }}
      </button>
      <button type="button" class="btn-submit" (click)="filterApply()">
        {{ "Apply" }}
      </button>
    </div>
  </div>
</ng-template>

<!-- <ng-template #filterMenuDailog>
  <div class="filter-main-menu-container">
    <div class="filter-menu-heading">
      <span class="filter-title-size">Filter By</span>
    </div>
    <div class="history-filter-menu-container">
      <div class="filter-menu-input">
        <label>Date Range</label>
        <div class="date-picker-container">
          <mat-form-field appearance="outline">
            <mat-date-range-input [rangePicker]="rangePicker">
              <input readonly matStartDate placeholder="Start Date" [(ngModel)]="startDate"
                [ngModelOptions]="{ standalone: true }" />
              <input readonly matEndDate placeholder="End Date" [(ngModel)]="endDate"
                (dateChange)="endDateChanged($event)" [ngModelOptions]="{ standalone: true }" />
            </mat-date-range-input>
            <mat-datepicker-toggle matIconSuffix [for]="rangePicker"></mat-datepicker-toggle>
            <mat-date-range-picker #rangePicker> </mat-date-range-picker>
          </mat-form-field>
        </div>
      </div>

      <div class="area-filter-container">
        <label> Redeemed Method</label>
        <angular2-multiselect class="dropdown" [data]="redeemedMethodDetails" [(ngModel)]="redeemedMethod"
          [settings]="redeemedMethodSeasonDropdownSettings" (onSelect)="onRedeemedMethodHistorySelected($event)"
          (onDeSelect)="onRedeemedMethodHistoryDeSelected($event)"
          (onDeSelectAll)="onRedeemedMethodHistoryDeSelectedAll($event)" [ngModelOptions]="{ standalone: true }">
        </angular2-multiselect>
      </div>
    </div>
    <div class="button-container">
      <button type="button" class="btn-cancel" (click)="clearFilter()">
        {{ "Clear" }}
      </button>
      <button type="button" class="btn-submit" (click)="filterApply()">
        {{ "Apply" }}
      </button>
    </div>
  </div>
</ng-template> -->

<ng-template #addEditSlabDailog>
  <div class="popup-overlay">
    <div class="popup-container">
      <div class="popup-header">
        <h3>Upload Invoice</h3>
      </div>
       <!-- Region, Zone, Leader Name -->
       <div class="form-group-row">
        <span>
          <label>Leader name<i class="required">*</i></label>
          <angular2-multiselect [data]="statusDataList" [(ngModel)]="statusLeaderName"
            [settings]="statusDropdownLeaderName" (onSelect)="onLeaderSelect($event)"
            (onDeSelect)="onLeaderDeselect($event)" (onDeSelectAll)="onLeaderDeselectAll($event)"
            [ngModelOptions]="{ standalone: true }"></angular2-multiselect>
        </span>
      </div>  
      <div class="popup-body">
        <form [formGroup]="dateForm">
          <div class="form-group upload-row">
            <!-- Upload PDF -->
            <div class="upload-container pdf-box">
              <span class="delete-icon" 
                    [style.opacity]="getFilesByType('pdf').length > 0 ? '1' : '0.6'"
                    [style.cursor]="getFilesByType('pdf').length > 0 ? 'pointer' : 'not-allowed'"
                    (click)="getFilesByType('pdf').length > 0 && deleteFile('pdf')" 
                    title="Delete">
                <i class="fa fa-trash" aria-hidden="true"></i>
              </span> 
              <div class="upload-box">
                <img src="../../../assets/img/uploadpdf.svg" alt="PDF Icon" />
                <p class="upload-header">Upload PDF here</p>
                <button *ngIf="getFilesByType('pdf').length === 0" type="button" class="btn-upload" (click)="pdfInput.click()">Select PDF File</button>
                <button *ngFor="let file of getFilesByType('pdf')" type="button" class="btn-upload filename-text" (click)="pdfInput.click()">{{ file.name }}</button>
                <input #pdfInput type="file" accept=".pdf" (change)="onFileSelected($event, 'pdf')" hidden />
              </div>
            </div>

            <!-- Upload XML -->
            <div class="upload-container xml-box">
              <span class="delete-icon"
                    [style.opacity]="getFilesByType('xml').length > 0 ? '1' : '0.6'"
                    [style.cursor]="getFilesByType('xml').length > 0 ? 'pointer' : 'not-allowed'"
                    (click)="getFilesByType('xml').length > 0 && deleteFile('xml')" 
                    title="Delete">
                <i class="fa fa-trash" aria-hidden="true"></i>
              </span> 
              <div class="upload-box">
                <img src="../../../assets/img/uploadxml.svg" alt="XML Icon" />
                <p class="upload-header">Upload XML here</p>
                <button *ngIf="getFilesByType('xml').length === 0" type="button" class="btn-upload" (click)="xmlInput.click()">Select XML File</button>
                <button *ngFor="let file of getFilesByType('xml')" type="button" class="btn-upload filename-text" (click)="xmlInput.click()">{{ file.name }}</button>
                <input #xmlInput type="file" accept=".xml" (change)="onFileSelected($event, 'xml')" hidden />
              </div>
            </div>

            <!-- <div class="upload-container uploaded-files">
              <div *ngFor="let file of getFilesByType('pdf')">
                <div class="file-display pdf-file">
                  <img src="../../../assets/img/uploadpdf.svg" alt="PDF Icon" class="file-icon" />
                  <div class="file-info">
                    <span class="file-name" [title]="file.name">{{ file.name }}</span>
                  </div>
                </div>
              </div>

              <div *ngFor="let file of getFilesByType('xml')">
                <div class="file-display xml-file">
                  <img src="../../../assets/img/uploadxml.svg" alt="XML Icon" class="file-icon" />
                  <div class="file-info">
                    <span class="file-name" [title]="file.name">{{ file.name }}</span>
                  </div>
                </div>
              </div>

              <div *ngIf="uploadedFiles.length === 0" class="empty-state">
                No files uploaded yet
              </div>
            </div> -->
          </div>
                 

        </form>
      </div>

      <div class="popup-footer">
        <button type="button" class="btn-cancel" (click)="uploadClose()">Cancel</button>
        <button type="button" class="btn-submit" (click)="submitInvoiceDetails()" [disabled]="!isFormValid()" [ngStyle]="{
          opacity: !isFormValid() ? 0.6 : 1,
          cursor: !isFormValid() ? 'not-allowed' : 'pointer'
        }">Preview</button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #previewDailog>
  <div class="invoice-container invoice-preview-popup">
    <form>
      <div class="form-input1-container">
        <div class="row" style="width: 100%">
          <div class="col-md-4">
            <span>
              <label>Invoice number</label>
              <input
                type="text"
                class="input-field"
                [(ngModel)]="previewData.invoiceNumber"
                name="invoiceNumber"
                [disabled]="true"
              />
            </span>
          </div>
          <div class="col-md-4">
            <span>
              <label>Invoice date</label>
              <input
                type="text"
                class="input-field"
                [(ngModel)]="previewData.invoiceDate"
                name="invoiceDate"
                [disabled]="true"
              />
            </span>
          </div>
        </div>
      </div>
      <div class="form-input1-container">
        <div class="row" style="width: 100%">
          <div class="col-md-4">
            <span>
              <label>Retailer name</label>
              <input
                type="text"
                class="input-field"
                [(ngModel)]="previewData.customerName"
                name="customerName"
                [disabled]="true"
              />
            </span>
          </div>
          <div class="col-md-4">
            <span>
              <label>Distributor name</label>
              <input
                type="text"
                class="input-field"
                [(ngModel)]="previewData.DistributorName"
                name="DistributorName"
                [disabled]="true"
              />
            </span>
          </div>
        </div>
      </div>
      
      <!-- Product Selection Table -->
      <h3>Product List</h3>
      <div class="product-selection-table">
        <mat-table [dataSource]="dataSource" class="mat-elevation-z8">
          <!-- Product Name Column with Checkbox -->
          <ng-container matColumnDef="productName">
          <mat-header-cell *matHeaderCellDef>  Product Name </mat-header-cell>
            <!-- <mat-header-cell *matHeaderCellDef> -->
              <!-- <div class="header-container">
                Product Name
              </div> -->
            <!-- </mat-header-cell> -->
            <mat-cell *matCellDef="let row">
              <div class="cell-container">
                <mat-checkbox
                  [checked]="row.selected"
                  (change)="onCheckboxChange(row)"
                  [disabled]="row.isMatched">
                </mat-checkbox>
                <span [title]="row.productName">{{row.productName}}</span>
              </div>
            </mat-cell>
          </ng-container>

          <!-- Quantity Column -->
          <ng-container matColumnDef="quantity">
            <mat-header-cell *matHeaderCellDef> Quantity </mat-header-cell>
            <mat-cell *matCellDef="let element"> {{element.quantity}} </mat-cell>
          </ng-container>

          <!-- Unit Cost Column -->
          <ng-container matColumnDef="unitCost">
            <mat-header-cell *matHeaderCellDef> Unit Cost (Mex$) </mat-header-cell>
            <mat-cell *matCellDef="let element"> ${{element.unitCost.toFixed(2)}} </mat-cell>
          </ng-container>

          <!-- Amount Column -->
          <ng-container matColumnDef="amount">
            <mat-header-cell *matHeaderCellDef> Total cost (Mex$) </mat-header-cell>
            <mat-cell *matCellDef="let element"> ${{element.amount.toFixed(2)}} </mat-cell>
          </ng-container>

          <!-- Search By Column -->
          <ng-container matColumnDef="searchBy">
            <mat-header-cell *matHeaderCellDef> UPL Product Select </mat-header-cell>
            <mat-cell *matCellDef="let element">
              <div style="display: flex; flex-direction: column; width: 100%;">
                <angular2-multiselect class="search-field" [data]="DataList" [(ngModel)]="element.statusName"
                  [settings]="element.dropdownSettings" (onSelect)="onDataSelect($event, element)"
                  (onDeSelect)="onDataDeselect($event, element)" (onDeSelectAll)="onDataDeselectAll($event, element)"
                  [ngModelOptions]="{ standalone: true }"
                  [ngStyle]="{
                    'margin-top': element.selected && (!element.statusName || element.statusName.length === 0) ? '6px' : '0px'
                  }"></angular2-multiselect>
                <div *ngIf="element.selected && (!element.statusName || element.statusName.length === 0) && !element.isMatched" class="error-message" style="color: red; font-size: 12px; margin-top: 4px;">
                  Please select a product
                </div>
              </div>
            </mat-cell>
          </ng-container>
          

          <mat-header-row
          *matHeaderRowDef="displayedColumns; sticky: true"
          [style.backgroundColor]="'#FBE3D4'"
        ></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        </mat-table>
      </div>
      
      <div class="buttons">
        <button type="button" class="cancel" (click)="onCancelPreview()">Cancel</button>
        <button type="submit" class="submit" (click)="submitUploadData()">Submit</button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #approveInvoice>
  <div class="popup-overlay">
    <div class="popup-container-appover">
      <div class="popup-header">
        <!-- Header content -->
      </div>
      
      <div class="popup-content">
        <!-- Wrap table or wide content in horizontal-scroll-container -->
        <div class="horizontal-scroll-container">
          <mat-table [dataSource]="dataSource" style="width: 100%; height: auto; box-shadow: none;" class="mat-elevation-z8">
            <!-- Product Name Column with Checkbox -->
            <ng-container matColumnDef="productName">
              <mat-header-cell *matHeaderCellDef>
                <div class="header-container">
                  Product Name
                </div>
              </mat-header-cell>
              <mat-cell *matCellDef="let row">
                <div class="cell-container">
                  <mat-checkbox
                    [checked]="row.selected"
                    (change)="onCheckboxChange(row)"
                    [disabled]="row.isMatched"
                    >
                  </mat-checkbox>
                  <span [title]="row.productName">{{row.productName}}</span>
                </div>
              </mat-cell>
            </ng-container>

            <!-- Quantity Column -->
            <ng-container matColumnDef="quantity">
              <mat-header-cell *matHeaderCellDef> Quantity </mat-header-cell>
              <mat-cell *matCellDef="let element"> {{element.quantity}} </mat-cell>
            </ng-container>

            <!-- Unit Cost Column -->
            <ng-container matColumnDef="unitCost">
              <mat-header-cell *matHeaderCellDef> Unit Cost (Mex$) </mat-header-cell>
              <mat-cell *matCellDef="let element"> ${{element.unitCost.toFixed(2)}} </mat-cell>
            </ng-container>

            <!-- Amount Column -->
            <ng-container matColumnDef="amount">
              <mat-header-cell *matHeaderCellDef> Total cost (Mex$) </mat-header-cell>
              <mat-cell *matCellDef="let element"> ${{element.amount.toFixed(2)}} </mat-cell>
            </ng-container>

            <!-- Category Column - enabled for unselected rows, disabled for selected rows -->
            <!-- <ng-container matColumnDef="category">
              <mat-header-cell *matHeaderCellDef> Category </mat-header-cell>
              <mat-cell *matCellDef="let element">
                <mat-select [(ngModel)]="element.category" 
                            [disabled]="element.selected" 
                            placeholder="Select Category"
                            [ngClass]="{'enabled-dropdown': !element.selected, 'disabled-dropdown': element.selected}">
                  <mat-option value="CP">CP</mat-option>
                  <mat-option value="NPP">NPP</mat-option>
                </mat-select>
              </mat-cell>
            </ng-container> -->

            <!-- Search By Column -->
            <ng-container matColumnDef="searchBy">
              <mat-header-cell *matHeaderCellDef> UPL Product Select </mat-header-cell>
              <mat-cell *matCellDef="let element">
                <div style="display: flex; flex-direction: column; width: 100%;">
                  <angular2-multiselect 
                    class="search-field" 
                    [data]="DataList" 
                    [(ngModel)]="element.statusName"
                    [settings]="element.dropdownSettings" 
                    (onSelect)="onDataSelect($event, element)"
                    (onDeSelect)="onDataDeselect($event, element)" 
                    (onDeSelectAll)="onDataDeselectAll($event, element)"
                    [disabled]="element.isMatched"
                    [ngModelOptions]="{ standalone: true }"
                    [ngStyle]="{
                      'margin-top': element.selected && (!element.statusName || element.statusName.length === 0) ? '6px' : '0px'
                    }">
                  </angular2-multiselect>
                  <div *ngIf="element.selected && (!element.statusName || element.statusName.length === 0) && !element.isMatched" class="error-message" style="color: red; font-size: 12px; margin-top: 5px;">
                    Please select a product
                  </div>
                </div>
              </mat-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="displayedColumns"  [style.backgroundColor]="'#FBE3D4'"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
          </mat-table>
        </div>
        
        <!-- Error message below the product selection table -->
        <div *ngIf="roductSelectionError" class="error-message" style="color: red; font-weight: bold; margin-top: 6px; text-align: center;">
          {{ roductSelectionError }}
        </div>
        
        <!-- No results message -->
        <div class="no-result" *ngIf="dataSource.data != null && dataSource.data.length === 0" style="width: 100%; text-align: center; padding: 20px; background-color: white; font-size: 14px; color: #333; border: 1px solid #ddd; margin-top: 10px; border-radius: 4px;">
          No data found
        </div>
        
        <div class="button-group">
          <button type="button" class="cancel-btn" (click)="close()">Cancel</button>
          <button type="submit" class="create-btn" [disabled]="!isFormValid" (click)="submit()">Approve Invoice</button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #rejectedInvoice>
  <div class="reject-dialog">
    <h2 class="reject-title">Add Comment</h2>
    <div class="reject-content">
      <textarea [(ngModel)]="rejectRemark" placeholder="Enter your comment here..." class="reject-textarea" rows="5">
      </textarea>
    </div>
    <div class="reject-actions">
      <button class="btn-cancel" (click)="cancelReject()">Cancel</button>
      <button class="btn-submit"
      [disabled]="!rejectRemark || rejectRemark.length === 0"
      (click)="submitReject()">Submit</button>
    </div>
  </div>
</ng-template>

<ng-template #viewProducts>
  <div class="common-popup-container">
    <div class="popup-backdrop" (click)="closePopup()">
      <div class="popup-content" (click)="$event.stopPropagation()">
        <div class="popup-header">
          <h1 class="al-title">Invoice Product Details</h1>
        </div>
        <div class="table-container">
          <dynamic-table 
            [tableHeads]="viewProductHead" 
            [tableData]="viewProductData"
            [tableConfiguration]="previewConfigurationSettings" 
            [tableColName]="viewProductColName"
            (pageChange)="getPageData($event)" 
            [showIndex]="showIndex">
          </dynamic-table>
        </div>
        <div class="popup-footer-product">
          <button class="product-cancel-btn" (click)="closePopup()">Close</button>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<!-- Document Viewer Template -->
<ng-template #documentViewerTemplate>
  <div class="document-viewer-container">
    <div class="document-viewer-header">
      <h2>{{ currentDocumentTitle }}</h2>
      <button class="close-button" (click)="closeDocumentViewer()">&times;</button>
    </div>
    <div class="document-viewer-content">
      <iframe [src]="getSafeUrl(iframeUrl)" class="document-frame"></iframe>
    </div>
  </div>
</ng-template>
