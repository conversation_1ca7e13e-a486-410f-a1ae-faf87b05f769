<mat-card class="mat-elevation-z8 parent-container" id="mat1">
  <mat-card-title
    >Scheme Details  
  </mat-card-title>
  <mat-card-actions>
    <div fxLayout="row" fxLayoutAlign="center start">
      <div fxLayoutGap="20px">
        <div fxLayout="row" fxLayoutAlign="start center">
          <form [formGroup]="userForm">
             <div class="container" style="width: 100%;">
              <div class="row">
                <div class="col-md-4 crop-container">
                  <div class="label">
                    <mat-label>Crop<span class="star">*</span></mat-label>
                  </div>
                  <!-- <angular2-multiselect
                    [data]="cropData"
                    [(ngModel)]="selectedCrops"
                    [settings]="cropDropdownSettings"
                    (onSelect)="selectedCrop($event)"
                    (onDeSelect)="deSelectedCrop($event)"
                    (onSelectAll)="selectedAllCrop($event)"
                    (onDeSelectAll)="deSelectedAllCrop($event)"
                    [ngModelOptions]="{ standalone: true }"
                  >
                  </angular2-multiselect> -->
                  <span
                    *ngIf="!selectedCrops.length && submitClicked"
                    class="help-block sub-little-error confpass"
                    >Please select Crop</span
                  >
                </div>
                <div class="col-md-4">
                  <div class="label">
                    <mat-label>Brand<span class="star">*</span></mat-label>
                  </div>
                  <!-- <angular2-multiselect
                    [data]="brandData"
                    [(ngModel)]="selectedBrands"
                    [settings]="brandDropdownSettings"
                    (onSelect)="selectedBrand($event)"
                    (onDeSelect)="deSelectedBrand($event)"
                    (onSelectAll)="selectedAllBrands($event)"
                    (onDeSelectAll)="deSelectedAllBrands($event)"
                    [ngModelOptions]="{ standalone: true }"
                  >
                  </angular2-multiselect> -->
                  <span
                    *ngIf="!selectedBrands.length && submitClicked"
                    class="help-block sub-little-error confpass"
                    >Please select brand</span
                  >
                </div>
                <div class="col-md-4">
                  <div class="label">
                    <mat-label>SKU<span class="star">*</span></mat-label>
                  </div>
                  <div>
                    <!-- <angular2-multiselect
                      [data]="skuData"
                      [(ngModel)]="product"
                      [settings]="skuDropdownSettings"
                      (onSelect)="selectedSKU($event)"
                      (onDeSelect)="deSelectedSKU($event)"
                      (onSelectAll)="selectedAllSKUS($event)"
                      (onDeSelectAll)="deSelectedAllSKUS($event)"
                      [ngModelOptions]="{ standalone: true }"
                    >
                      <input matInput disabled required />
                    </angular2-multiselect> -->
                  </div>
                </div>
              </div>
              <div class="row packshotField">
                <!-- <div class="col-md-4">
                  <div class="label">
                    <mat-label>Packshot<span class="star">*</span></mat-label>
                  </div> 
                    <div class="input-container" > 
                      <i class="fa fa-times" aria-hidden="true" (click)="clearPackshot()" *ngIf="imageName != 'Upload Packshot'"></i>
                      <div (click)="fileInput.click()" style="width: 325px;">
                        <input
                        required
                        readonly
                        placeholder="{{ imageName }}"
                        class="input-field"
                        style="    width: 88%;"
                        />
                        <mat-icon class="upload-icon" >upload</mat-icon>
                        <input
                        readonly
                          hidden
                          type="file"
                          (change)="uploadedFiles($event)"
                          #fileInput
                          id="file"
                        />
                      </div>
                    </div>
                </div> -->

                <div class="col-md-4">
                  <div class="label">
                    <mat-label>Reward Point Per Kg<span class="star">*</span></mat-label>
                  </div>
                  <!-- <mat-form-field
                    class="Reward-full-width"
                    appearance="outline"
                  > -->
                    <input
                      matInput
                      placeholder="Reward Points"
                      formControlName="point"
                      id="points"
                      autocomplete="off"
                      class="inputmargin1"
                      [(ngModel)]="rewardPoint"
                      (keypress)="funRestNumber($event)"
                    />
                    <span class="reward-point-error" *ngIf="userForm.get('point')!.invalid && userForm.get('point')!.touched">
                      Field is required*
                    </span>
                    <!-- </mat-form-field> -->
                  </div>
                <div class="col-md-4">
                  <div class="label">
                    <mat-label>Select Start Date<span class="star">*</span></mat-label>
                  </div>
                  <div class="dropdown-container">
                    <div class="start-date-container">
                      <mat-form-field
                        (click)="picker.open()"
                        class="custom-datepicker-container reward-date-range"
                        appearance="outline"
                      >
                        <input
                          [(ngModel)]="startDateModel"
                          (click)="picker.open()"
                          placeholder="Start Date"
                          autocomplete="off"
                          formControlName="startDate"
                          [disabled]="rewardId"
                          [ngClass]="{ 'disable-dropdown': rewardId }"
                          (dateChange)="onStartDateChanged($event)"
                          class="custom-datepicker reward-date-range date-input"
                          matInput
                          [matDatepicker]="picker"
                          [min]="minDate"
                        />  
                            <!-- <mat-icon (click)="clearDateSelection()">clear</mat-icon>  -->
                        <mat-datepicker-toggle
                        matIconSuffix
                        [for]="picker"
                      ></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker> 
                      </mat-form-field>
                      <div class="error-message">
                        <span
                          *ngIf="!startDateModel && !startDate && submitClicked"
                          class="help-block sub-little-error confpass"
                          >Start date is required</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="label">
                    <mat-label>Select End Date<span class="star">*</span></mat-label>
                  </div>
                  <div class="end-date-container">
                    <mat-form-field
                      (click)="picker1.open()"
                      class="custom-datepicker-container"
                      appearance="outline"
                    >
                      <input
                        [(ngModel)]="endDateModel"
                        (click)="picker1.open()"
                        placeholder="End Date"
                        formControlName="endDate"
                        autocomplete="off"
                        (dateChange)="onEndDateChanged($event)"
                        class="custom-datepicker reward-date-range date-input"
                        [min]="startDateModel" 
                        matInput
                        [matDatepicker]="picker1"
                      />
                      <mat-datepicker-toggle
                        matIconSuffix
                        [for]="picker1"
                      ></mat-datepicker-toggle>
                      <mat-datepicker #picker1></mat-datepicker>
                    </mat-form-field>
                    <div class="error-message">
                      <span
                        *ngIf="!endDateModel && !endDate && submitClicked"
                        class="help-block sub-little-error confpass"
                        >End date is required</span
                      >
                    </div>
                  </div>
                </div>
              </div>
              <div class="row startDateField">
               
                <div class="col-md-4"><div class="label">
                  <mat-label>Zone<span class="star">*</span></mat-label>
                </div>
                <!-- <angular2-multiselect
                  [data]="zoneDetails"
                  [(ngModel)]="zone"
                  [settings]="zoneDropdownSettings"
                  (onSelect)="selectedZone($event)"
                  (onDeSelect)="deSelectedZone($event)"
                  (onSelectAll)="selectedAllZones($event)"
                  (onDeSelectAll)="deSelectedAllZones($event)"
                  [ngModelOptions]="{ standalone: true }"
                  class="zone-width"
                >
                </angular2-multiselect> -->
              </div>
                <div class="col-md-4">
                  <div class="label">
                    <mat-label>Region<span class="star">*</span></mat-label>
                  </div>
                  <div class="region-full-width">
                    <!-- <angular2-multiselect
                      [data]="regionsDetails"
                      [(ngModel)]="region"
                      [settings]="regionDropdownSettings"
                      (onSelect)="selectedRegion($event)"
                      (onDeSelect)="deSelectedRegion($event)"
                      (onSelectAll)="selectedAllRegions($event)"
                      (onDeSelectAll)="deSelectedAllRegions($event)"
                      [ngModelOptions]="{ standalone: true }"
                    >
                    </angular2-multiselect> -->
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </mat-card-actions>
</mat-card> 
  <mat-card class="mat-elevation-z8 card2">
      <div class="form-row org-desc-parent-margin">
          <mat-card-title
          >Slabs
        </mat-card-title>   
        <button
          class="btn add-button add-slab-button"
          color="primary"
          (click)="addRowSlabs()"
          [disabled]="disabledAddButton()"
          >  
            <mat-icon class="icons">add</mat-icon><span>Add Slab
         </span>
         </button>
    </div> 
  <div class="container" style="    width: 100%;">  
    <form class="example-form" [formGroup]="slabForm">
      <div formArrayName="sectionFormArray">
        <div *ngFor="let item of sectionFormArray.controls; let i = index" [formGroupName]="i">
          <div id="card3" class="col-md-20">
            <mat-card class="mat-elevation-z8 slab-card" fxLayout="row wrap" fxLayoutGap="16px">
              <div class="form-row2">
                <button *ngIf="i > 0" class="btn float-end mb-3 deleteBtn" (click)="deleteRowSlabs(i)" >
                  <mat-icon class="delIcons">delete
                  </mat-icon>
                </button>
              </div>

              <div class="container">
                <div class="row">
                  <div class="col-md-3">
                    <div>
                      <div>
                        <mat-label class="label2">Slab<span class="star">*</span></mat-label>
                      </div>
                      <mat-form-field appearance="outline">
                        <input matInput formControlName="slab" readonly class="form-control" autocomplete="off"
                          [placeholder]="'Slab ' + (i + 1)" style="color:black" [disabled]="true">
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div>
                      <div>
                        <mat-label class="label2">Min<span class="star">*</span></mat-label>
                      </div>
                      <mat-form-field appearance="outline">
                        <input matInput type="number" formControlName="minQuantity" placeholder="Min Quantity (Kg)*" (keypress)="funRestNumber($event)"
                          (ngModelChange)="minValueOnChange1($event, i)"
                          [min]="i > 0 ? +sectionFormArray.at(i - 1).get('maxQuantity').value : 0" 
                          class="form-control" required (keyup)="minKeypress(i)" autocomplete="off">
                        <mat-hint class="mat-error" *ngIf="invalidMinNumber && minFieldIndex == i">
                          Can not be less than {{ maxQuant }}
                        </mat-hint>
                        <mat-error *ngIf="item.get('minQuantity').hasError('required')">
                          Field is required*
                        </mat-error>
                        <mat-error *ngIf="sectionFormArray.hasError('duplicate')">
                          Can not be less than
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div>
                      <div>
                        <mat-label class="label2">Max<span class="star">*</span></mat-label>
                      </div> 
                      <mat-form-field appearance="outline">
                        <input matInput type="number" formControlName="maxQuantity" (keypress)="funRestNumber($event)"placeholder="Max Quantity (Kg)*"
                          class="form-control" required [min]="+sectionFormArray.at(i).get('minQuantity').value"  
                         (keyup)="maxKeypress(i)" autocomplete="off">
                        <mat-hint class="mat-error star" *ngIf="!maxQuantityFlag && minFieldIndex == i">
                          Can not be less than {{ minQuant }}
                        </mat-hint>
                        <mat-error class="star" *ngIf="item.get('maxQuantity').hasError('required')">
                          Field is required*
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div>
                      <div>
                        <mat-label class="label2">Bonus<span class="star">*</span></mat-label>
                      </div>
                      <mat-form-field appearance="outline">
                        <input type="number" placeholder="Bonus" formControlName="bonus" matInput class="form-control"(keypress)="funRestNumber($event)"
                          (ngModelChange)="bonusMethod($event)" required autocomplete="off">
                        <mat-error class="star" *ngIf="item.get('bonus').invalid">
                          Field is required
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>
            </mat-card>
          </div>
        </div>
      </div>
    </form>
    
  </div>
  <div class="parent-button"> 
      <button class="cancel-button" (click)="cancelButton()"> Cancel </button> 
      <button class="submit-button" type="submit" (click)="addScheme(userForm.value)" [disabled]="diableSubmitButton()"> Submit </button>
  </div>
</mat-card>
