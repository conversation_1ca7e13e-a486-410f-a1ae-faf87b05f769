.parent-container {
  .mat-form-field-flex {
    height: 49px;
    width: 115%;
    margin-left: 0px;
  }
  .mat-form-field-appearance-outline .mat-form-field-outline {
    border-color: #1919a4;
  }
 
}
.region-full-width {
  .cdk-text-field-autofill-monitored[_ngcontent-c8]:not(:-webkit-autofill)
    .dropdown-container {
    margin-left: 10px;
  }
}

.container {
  display: grid;
  grid-template-columns: repeat(minmax(320px, 1fr));
  grid-column-gap: 1rem; // optional
  grid-row-gap: 1rem;
}
mat-card {
  margin-left: 30px;
  margin-right: 10px;
  margin-top: 0.5%;
  text-align: center;
  height: 45%;
  width: 95%;
  margin-bottom: 10px;
  padding: 24px;
}

.label {
  text-align: left;
  margin-left: 5px;
  font-size: 15px;
}
.label2 {
  text-align: left;
  float: left;
  font-size: 15px;
  position: relative;
  margin-left: 20px;
}
#mat1 {
  margin-top: 7%;
}
#card3 mat-card mat-form-field {
  height: 500%;
  margin-bottom: 25px;
}
#span[_ngcontent-c8] {
  position: relative;
  top: -55px;
  display: block;
  font-size: 10px;
  font-family: sans-serif;
  color: red;
  float: left;
  margin-left: 25px;
}

#card3 {
  border-bottom: 50px;
}
.card2 {
  margin-bottom: 30px;
  .container {
    width: 100%!important;
  }
}
.form-row {
  margin-bottom: 15px;
}
.form-row2 {
  float: right;
  margin-right: 30px;
}
h1 {
  text-align: left;
  font-size: 24px;
  color: rgb(7, 7, 111);;
}
form {
  background: none;
  margin-left: 10px;
}
table {
  position: relative;
  width: 100%;
}
td {
  padding-right: 8px;
  width: 25%;
}
.btn-container button {
  border: 1px solid black;
}

.btn-primary {
  width: 12.6%;
  height: 35.5px;
  margin-top: 50px;
  margin-right: 350px;
  font-size: 15px;
  font-weight: 600;
  text-align: center;
  padding-top: 8px;
  // padding: 8px 6px;
}
#submit-button {
  margin-left: -110px;
}
#cancel-button {
  margin-left: 300px;
}

.org-desc-parent-margin button {
  background-color: #FF8033;
  font-size: 14px;
  padding-top: 6px;
  font-weight: 600;
  text-align: center;
  color: #ffffff;
  height: 35px;
  display: flex;
  align-items: center;
}
.icons {
  transform: scale(1.5);
  font-size: 14px;
  padding-right: 8px;
  text-align: center;
  // padding-top: 3px;
  font-weight: bold;
  display: flex;
  align-items: center;
}
.delIcons {
  transform: scale(1.5);
  font-size: 16px;
  text-align: center;    
  vertical-align: middle;
  top: 6px;
  right: 0px;
  position: relative;
}

.form-row {
  display: flex;
  justify-content: space-between;
  width: 96%;
}
.brand-width {
  margin-right: 25px;
  flex-direction: row;
  flex-wrap: wrap;
  position: relative;
  border-radius: 5px;
  top: 0px;
  margin-bottom: 30px;
  height: 43px;
  background: none;
  border: 1px solid #ccc;
}
.example-full-width {
  margin-right: 25px;
  flex-direction: row;
  flex-wrap: wrap;
  position: relative;
  top: -20px;
  margin-bottom: 30px;
  height: 50px;
}

:host
  .mat-form-field:not(.mat-form-field-appearance-legacy)
  .mat-form-field-suffix
  .mat-icon-button {
  display: block;
  font-size: inherit;
  width: 2.5em;
  height: 2.5em;
  position: relative;
  top: -5px;
}

.packshot-full-width {
  .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
    -webkit-animation-name: cdk-text-field-autofill-end;
    animation-name: cdk-text-field-autofill-end;
    position: relative;
    //  top: -7px;
  }
  padding-right: 7px;
  flex-direction: row;
  flex-wrap: wrap;
  height: 40px;
  //  width: 88%;
  width: 100%;
  margin: -4px 40px 54px 0px !important;
  matInput {
    height: 20px;
  }

  .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 0.75em 0 0.75em;
    margin-top: -7px;
    position: relative;
    width: 50%;
  }
}

.Reward-full-width {
  .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
    -webkit-animation-name: cdk-text-field-autofill-end;
    animation-name: cdk-text-field-autofill-end;
    position: relative;
    font-size: 0.85rem;
    top: -10px !important;
  }
  :host
    .cdk-text-field-autofill-monitored[_ngcontent-c8]:not(:-webkit-autofill)
    .dropdown-container {
    text-align: left;
    position: relative;
    top: -7px;
  }
  padding-right: 7px;
  flex-direction: row;
  flex-wrap: wrap;
  height: 10px;
  width: 100%;
  margin: -5px 40px 54px 0px !important;
  matInput {
    height: 20px; // example
  }
}
.container {
  .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
    -webkit-animation-name: cdk-text-field-autofill-end;
    animation-name: cdk-text-field-autofill-end;
    position: relative;
    top: -18px;
    font-size: 0.85rem;
    border: 1px solid #ccc;
    height: 42px !important;
  }
}
.slab-field {
  display: inline-flex;
  top: 10px;
  width: 100%;
}
.slab-field {
  mat-form-field {
    position: relative;
    width: 100%;
    margin-left: 0px;
    padding: 10px;
  }
}
element.style {
  margin-left: 90%;
}
#cancel-button {
  float: right;
  width: 70%;
  position: fixed;
}

@media screen and (min-width: 600px) {
  .column {
    height: auto;
    overflow: hidden;
  }
}

@media screen and (min-width: 1500px) {
  .column {
    height: auto;
    overflow: hidden;
    float: left;

    .submit-btn {
      float: left !important;
      left: 5%;
      position: relative;
    }
  }
}
.submit-btn {
  float: left !important;
  left: 2%;
  position: relative;
}

* {
  box-sizing: border-box;
}
.column { 
  width: 50%;
  padding: 0;
  height: 100px;

  padding: 0 0;
  display: flex;
  justify-content: flex-end;
  // float: right;
}

.row:after {
  content: "";
  display: table;
  clear: both;
}

#mat-error {
  display: block;
  font-size: smaller;
  float: left;
  margin: -30px 0px 0px 15px;
  padding: 0px;
  top: -30px;
  z-index: 0;
}

@media screen and (max-width: 600px) {
  .column {
    width: 100%;
  }
}

:host::ng-deep .table > :not(caption) > * > * {
  padding: 0px;
}
.label2 {
  margin-left: unset;
}
.mdc-card__actions {
  display: inline !important;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  min-height: 52px;
  padding: 8px;
}
.inputmargin {
  display: block;
  position: relative;
  top: -5 !important;
  width: 100%;
  height: 42px;
  border: 1px solid #ccc;
  border-radius: 5px;
}
.inputmargin1 {
  display: block;
  position: relative;
  top: 0px !important;
  width: 100%;
  height: 40px !important;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding-left: 13px;
  // background-color: red;
}
.Reward-full-width .packshot-full-width {
  width: 100% !important;
  // background-color: green;
}

:host::ng-deep .packshot-full-width .mdc-notched-outline {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding-bottom: 20px;
  // padding: 18px;
}
:host::ng-deep .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper {
  //  padding: 4px 0 37px 6px;
  padding: 2px 0 40px 6px;
  outline: unset;
  font-size: 0.85rem;
  //  color: #333;
  top: 10%;
}

.mdc-notched-outline__notch {
  border-right: none;
}
.inputmarginBottem {
  height: 35px;
  margin-bottom: 6px;
}
:host::ng-deep .mat-mdc-form-field-flex {
  margin: 3px;
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: none;
}


.add-slab-button{
  margin: 0; 
  display: inline; 
  float: right; 
  display: flex; 
  background-color: #FF8033;
}

 .mat-icon-button{
  // position: relative;
  // bottom: 38px;
  // left: 160px;
  margin-bottom: 10px;
 }
 .mat-mdc-card-title, .mat-mdc-card-subtitle {
  text-align: left;
  margin: -10px 0 0px 10px;
  font-size: 18px;
  color: #374767;
  font-weight: 700;
}
.packshotField{
  // background-color: aqua !important;
  height: 87px !important;
 
}
.startDateField{
  // background-color: #1919a4 !important;
  height: 87px !important;
  position: relative;
  bottom: 10px;
}
.star{
  color: red !important;
}


.input-container {
  height: 42px !important;
  border: 1px solid #ccc;
  border-radius: 5px;
  display: flex;
  align-items: center;
  position: relative;
  background-color: #ffffff;

  i{
    margin-left: 10px;
    cursor: pointer;
  }
}

.input-field {
  flex: 1;
  // padding: 8px 35px 8px 5px;
  float: left;
  padding: 10px;
  border: none;
  // border: 1px solid #ccc;
  border-radius: 5px;
  outline: none;
  height: 38px ;
  cursor: pointer;
}

.input-field::placeholder {
  color: #999;
}

.upload-icon {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  cursor: pointer;
  color: #999; /* Icon color */
  font-size: 24px; /* Icon size */
}

/* Media Query for Responsive Icon */
@media screen and (max-width: 768px) {
  .upload-icon {
    font-size: 20px; /* Adjust the icon size for smaller screens */
  }
}

.form-control{
  width: 100% !important;
}
.reward-point-error{
  float: left;
}

.crop-container{
  padding-bottom: 10px;

  ::ng-deep.clear-all {
    width: 8px;
    position: absolute;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    visibility: hidden;
}
}

.date-input{
  position: relative !important;
  top: -21px !important; 
}

#one{
  width: 20%; margin: 0px; position: relative; top: 50px;font-weight: 400;
        background-color: #a9abad; border: none; font-size: 15px;
    font-weight: 500;padding-bottom: 10px;
}

.parent-button{
  .cancel-button{
    width: 10%;
    margin-right: 20px;
    background-color: #a9abad;
    border: none; 
    color: white;
    font-weight: bold; 
    border-radius: 3px;
    cursor: pointer; 
    font-size: 14px; 
    height: 35px;
  }
  .submit-button{
    width: 10%;
    background-color: #FF8033;
    color: #ffffff;
    border: none;
    font-weight: bold; 
    border-radius: 3px; 
    font-size: 14px; 
    height: 35px;
  }
  .submit-button:disabled{
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.deleteBtn{
  margin: 0px; 
  width: 50px;
  display: flex;
  background: #FF8033;
  color: #fff
}




// .parent-button[_ngcontent-ncj-c74] .cancel-button[_ngcontent-ncj-c74] {
//   width: 10%;
//   margin-right: 15px;
//   background-color: #a9aba;
//   border: 1px solid #999;
//   height: 35px;
//   border-radius: 5px;
//   border: none;
//   color: #fff;
//   font-weight: 800;
// }



// background-color: #a9abad;
//     color: white;
//     font-weight: bold;
//     border: none;
//     border-radius: 3px;
//     cursor: pointer;
//     width: auto;
//     font-size: 12px;
//     min-width: 150px;
//     height: auto;