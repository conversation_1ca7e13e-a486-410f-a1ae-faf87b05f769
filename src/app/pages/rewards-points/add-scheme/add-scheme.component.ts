import { ChangeDetectionStrategy, Component, Input, OnInit } from "@angular/core";
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from "@angular/forms";
import { UserService } from "src/app/app-services/user-service";
import { ApiServiceService } from "../../api-service.service";
import { TerritoriesService } from "../../../app-services/territories-service";
import { RegionsService } from "../../../app-services/regions-service";
import { ToastrService } from "ngx-toastr";
import { AppConstant } from "../../../constants/app.constant";
import { ZonesService } from "../../../app-services/zones-service";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { IMyOptions } from "mydatepicker";
import * as _ from "lodash";
import { RewardPointsService } from "src/app/app-services/reward-points-service";
import moment from "moment";
import { ActivatedRoute, Router } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatIconModule } from "@angular/material/icon";
import { MatButtonModule } from "@angular/material/button";
import { MatInputModule } from "@angular/material/input";
import { NgIf, NgClass, NgFor, DatePipe, CommonModule } from "@angular/common";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatCardModule } from "@angular/material/card";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { GlobalEvents } from "src/app/helpers/global.events";
import { DashboardService } from "src/app/app-services/dashboard.service";
import { NgxDropzoneModule } from "ngx-dropzone"; 
import { AuthimagevalidationPipe } from "../../../app-services/authimagevalidation.pipe";
import { Utility } from "src/app/shared/utility/utility";
import { firstValueFrom } from "rxjs";

@Component({
    selector: "app-add-scheme",
    templateUrl: "./add-scheme.component.html",
    styleUrls: ["./add-scheme.component.scss"],
    providers: [DatePipe, BaThemePreloader, BaThemeSpinner, BaMenuService],
    imports: [
        MatCardModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        // AngularMultiSelect,
        NgIf,
        MatInputModule,
        MatButtonModule,
        MatIconModule,
        MatDatepickerModule,
        NgClass,
        NgFor,
        NgxDropzoneModule,
        CommonModule,
    ]
})
export class AddSchemeComponent implements OnInit {
  @Input() disabled = true;
  selectedBrands: any = [];
  names!: any[];
  name = "Angular Multi Select Dropdown";
  dropdownList: any = [];
  dropdownSettings: any = {};
  dropDownForm!: FormGroup;
  selectedItems: any = [];
  data: any = [];
  arrId: any = [];
  arrName: any = [];
  regionsList: any = [];
  customersDetails: any = [];
  customer: any = [];
  selectedCustomerTypes: any = [];
  selectedSKUCodes: any = [];
  selectedCropId: any;
  selectedSkuData: any = [];
  cropData: any = [];
  selectedCrops: any = [];
  zoneDetails: any = [];
  zone: any = [];
  dataArry: any;
  dataArray: any = [];
  selectedZoneCodes: any = [];
  selectedZones: any = [];
  selectedRegions: any = [];
  resultTitle!: string;
  region: any = [];
  territory: any = [];
  selectedTerritoryCodes: any = [];
  territoriesDetails: any = [];
  isAF: any;
  isSWAL: boolean = false;
  isCustomer: boolean = false;
  regionsDetails: any = [];
  selectedRegionCodes: any = [];
  anurag: string = "";
  shortLink: string = "";
  loading: boolean = false; 
  file!: File; 
  fileName: any = "";
  selectedFiles: any;
  fileToUpload!: File;
  imageDetails!: any[];
  uploadedImage = '';
  startDate: any;
  endDate: any;
  endDateOptions!: {
    dateFormat: string;
    showTodayBtn: boolean;
    inline: boolean;
    firstDayOfWeek: string;
    editableDateField: boolean;
    showClearDateBtn: boolean;
    openSelectorOnInputClick: boolean;
    disableUntil: { day: number; month: number; year: number };
  };
  endDateModel!: string;
  startDateModel: any = "";
  rewardId: any;
  slab: string = "";
  maxQuantity: string = "";
  minQuantity: string = "";
  minValue !: number;
  maxValue !: number;
  bonus: string = "";
  rewardPoints: any;
  rewardTitle: any;
  selectedSlab: any = [];
  submitClicked: boolean = false;
  brandData: any = [];
  selectedBrandCodes: any = [];
  skuData: any = [];
  resData: any = [];
  product: any = [];
  skuDataItem: any = [];
  points: string = "";
  brandsCodes!: string;
  userButton!: { path: string; title: string }[];
  slabData: any = [];
  invalidNumber!: boolean;
  minFieldIndex!: number;
  cropAllData: any = [];
  public userForm!: FormGroup;
  gallaryImagesDetails!: any[];
  files!: any[];
  packshotFiles: any;
  minQuantityFlag: boolean = false;
  maxQuantityFlag: boolean = false;
  slabForm!: FormGroup;
  minQuant: any;
  maxQuant: any;
  slabDataArray: any = [];
  slabValue: number = 2;
  tempSlabValue: number = 1;
  disableFields = false;
  minDate: any;
  maxDate: any;
  rewardPoint !: number ;
  addSlabFlag !: boolean; 
  imageName: any='Upload Packshot';
  randomNumber : any;

  constructor(
    private userService: UserService,
    private rewardService: RewardPointsService,
    private routes: ActivatedRoute,
    private _fb: FormBuilder,
    private apiService: ApiServiceService,
    private territoriesService: TerritoriesService,
    private regionsService: RegionsService,
    public toastr: ToastrService,
    private zonesService: ZonesService,
    private spinner: BaThemeSpinner,
    private http: HttpClient,
    private router: Router,
    private events: GlobalEvents,
    private fb: FormBuilder,
    private dashboardService  :DashboardService,
    private utility : Utility
  ) {
    this.events.setChangedContentTopText("Add Scheme");
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
    this.randomNumber = Math.floor(1000 + Math.random() * 9000); 
  }

  ngOnInit() {
    this.spinner.hide();
    this.rewardId = localStorage.getItem('reward_id'); 
    if(this.rewardId){
      this.getAllCropsData();
      this.getAllProduct();
      this.getAllZones();
      this.initializeForms(); 
    }else{
      this.getAllCropsData();
      this.getAllZones();
      this.initializeForms();
      this.userForm = new FormGroup({
        point: new FormControl("", Validators.required),
        startDate: new FormControl(),
        endDate: new FormControl(),
      });
    }
  }
 


// Helper method to get the slab form array control
get slabFormArray(): FormArray {
  return this.slabForm.get('slabs') as FormArray;
}

// Method to add a new slab form group
addSlab() {
  const slabGroup = this._fb.group({
    slab: ['', Validators.required],
    min: [null, Validators.required],
    max: [null, Validators.required],
    bonus: [null, Validators.required]
  });

  this.slabFormArray.push(slabGroup);
}

// Method to remove a slab form group at the specified index
removeSlab(index: number) {
  this.slabFormArray.removeAt(index);
}
 
  packshotForm = new FormGroup({
    file: new FormControl("", Validators.required),
  });

  get f() {
    return this.packshotForm.controls;
  }

  
  // uploadedFiles(event: any): void {        
  //   this.uploadedImage = '';
  //   const string = event.addedFiles[0].type; 
  //   const filename = event.addedFiles[0].name;
  //   const fileExtension = this.getImageExtension(filename); 
  //   if (event.addedFiles[0].size <= 10000000 && fileExtension === '.png' || fileExtension === '.jpeg' || fileExtension === '.jpg' ){    
  //     let errorResponse = string;
  //     this.spinner.show();
  //     const file = event.addedFiles[0];
  //     const formData = new FormData();
  //     formData.append("file", file, file.name);  
  //     this.userService.uploadProfileImage(formData).subscribe({
  //       next : (data :any)=>{    
  //         data = this.dashboardService.removeSuffix(data);   
  //         // this.getImage(data.message);
  //         if(data.success==true){  
  //           this.imageName = file.name;
  //           this.uploadedImage = data.message;    
  //           this.spinner.hide();
  //           this.toastr.success('Image uploaded successfully');
  //         }else if(data.success==false){
  //           errorResponse = data.message;
  //           this.spinner.hide();
  //           this.toastr.error(errorResponse);
  //         }
  //       },
  //       error : (error : any)=>{
  //         this.toastr.error(error.message); 
  //         this.spinner.hide();
  //       }
  //     })
  //   }else{
  //     if(event.target.files[0].size <= 10000000){
  //       this.spinner.hide();
  //       this.toastr.error("Invalid image format");
  //     }else{
  //       this.spinner.hide();
  //       this.toastr.error("Image size should be less than 10 MB");
  //     }
  //   }
  // }
 
  uploadedFiles(event: any): void {
    const file = event.target.files[0];
    const formData = new FormData();
    formData.append("file", file); 
    this.spinner.show();
    this.userService.uploadProfileImage(formData).subscribe({
      next :(data) => {
      data = this.dashboardService.removeSuffix(data); 
      this.packshotFiles = data.message; 
      this.imageName = data.message.split("packshot/")
      this.imageName = this.imageName[1]; 
      this.spinner.hide();
    },
    error : (errorResponse  :any)=>{
      this.spinner.hide();
      this.handleImageError();
    }
  });
  }

  handleImageError() {
    this.toastr.error('Failed to upload image please try again'); 
  }
  

 

 async getImage(image:string){   
    const categories$ = this.dashboardService.uploadImageEcho1(image);
    let categories = await firstValueFrom(categories$); 
  }

  getImageExtension(filename: string): string | undefined {  
    const lastDotIndex = filename.lastIndexOf(".");
    if (lastDotIndex !== -1 && lastDotIndex < filename.length - 1) {
      return filename.substring(lastDotIndex); 
    }
    return undefined;
  }

  clearPackshot(){
    this.spinner.show();
    this.packshotFiles = null;
    this.imageName='Upload Packshot';
    const fileInput = document.getElementById('file') as HTMLInputElement;
    fileInput.value = '';
    setTimeout(() => {
      this.spinner.hide(); 
    }, 900);
  }

  resetFileInput() {
    // Clear the file input by resetting its value
    const fileInput = document.getElementById('file') as HTMLInputElement;
    fileInput.value = '';
  }
  
  skuDropdownSettings = {
    text: "Select SKU",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
  };

  zoneDropdownSettings = {
    text: "Select Zone",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
  };

  regionDropdownSettings = {
    text: "Select Region",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
  };

  brandDropdownSettings = {
    text: "Select Brand",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
  };
  cropDropdownSettings = {
    text: "Select Crop",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    autoPosition: false,
    disabled: false,
    badgeShowLimit: 1,
  };

  addRowSlabs() :void{
    this.sectionFormArray.push(this.createItem());
  }

  deleteRowSlabs(i: any):void {
    this.sectionFormArray.removeAt(i);
  }
  selectFile(event: any):void {
    this.selectedFiles = event.target.files[0];
  }

  getAllCropsData():void {
    this.userService
      .getAllCrops(this.selectedCropId)
      .subscribe((cropResponse) => {
        cropResponse = this.dashboardService.removeHashSuffix(cropResponse);
        this.cropData = cropResponse;
        this.cropData.forEach((cropInfo: any) => {
          let cropObj = {
            id: cropInfo.id,
            name: cropInfo.name,
            description: cropInfo.description,
          };
          this.cropAllData.push(cropObj);
        });
      });
  }

  /**
   * Triggered when a brand is selected in multi select dropdown
   * @param event
   */
  selectedCrop(event: any):void {
    this.selectedCropId = event.id; 
    this.selectedBrands = [];
    this.brandData = [];
    this.skuData = [];
    this.product = []; 
    this.selectedBrandCodes = [];
    this.getAllProduct();
  }

  /**
   * Triggered when a brand is deselected in multi select dropdown
   * @param _event
   */
  deSelectedCrop(_event: any):void {
    this.brandData = [];
    this.skuData = [];
    this.product = [];
    this.selectedCropId = null;
    this.selectedBrands = []; 
    this.selectedBrandCodes = [];
  }

  /**
   * Triggered when all brands are selected in multi select dropdown
   * @param event
   */
  selectedAllCrop(event: any) :void{
    if (event && event.length) {
      this.selectedCropId = null;
      this.brandData = [];
    }
  }
  /**
   * Triggered when all brands are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllCrop(_event: any):void {
    this.selectedCropId = null;
  }

  getAllProduct() :void{
    this.spinner.show();
    const brandData = this.apiService.getBrands(this.selectedCropId);
    brandData.subscribe((brandDataInfo: any) => {
      brandDataInfo = this.dashboardService.removeHashSuffix(brandDataInfo);
      this.brandData = [];
      brandDataInfo.forEach((brandInfo: any) => {
        let brandObj = {
          code: brandInfo.code,
          name: brandInfo.name,
          id: brandInfo.id,
        };
        this.brandData.push(brandObj);
      });
      this.spinner.hide();
    });
  }

  /**
   * Triggered when a brand is selected in multi select dropdown
   * @param event
   */
  selectedBrand(event: any):void {
    if (!_.includes(this.selectedBrandCodes, event.code)) {
      this.selectedBrandCodes.push(event.code); 
      this.skuData = [];
      this.product = [];
      this.productsByBrand();
    }
  }

  /**
   * Triggered when a brand is deselected in multi select dropdown
   * @param event
   */
  deSelectedBrand(event: any):void {
    if (_.includes(this.selectedBrandCodes, event.code)) {
      _.remove(this.selectedBrandCodes, (item) => item === event.code);
      if (this.selectedBrandCodes && this.selectedBrandCodes.length) {
        this.skuData = [];
        this.productsByBrand();
      } else {
        this.skuData = [];
      }
      this.product = [];
    }
  }

  /**
   * Triggered when all brands are selected in multi select dropdown
   * @param event
   */
  selectedAllBrands(event: any):void {  
    if (event && event.length) {
      this.selectedBrandCodes = [];
      this.selectedBrandCodes = _.map(event, "code");
      this.productsByBrand();
    }
  }
  /**
   * Triggered when all brands are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllBrands(_event: any):void{
    this.selectedBrandCodes = [];
    this.skuData = [];
    this.product = [];
  }
  productsByBrand():void {
    this.spinner.show();
    this.userService
      .skuDetailsByBrandCode(this.selectedBrandCodes)
      .subscribe((res: any) => { 
        res = this.dashboardService.removeHashSuffix(res);
        this.skuData = [];
        res.forEach(
          (skuDetails: any) => {
            let skuDetailsObj = {
              id: skuDetails.id,
              code: skuDetails.code,
              name: skuDetails.name,
              description: skuDetails.description,
            };
            this.skuData.push(skuDetailsObj);
          },
          (errResponse: any) => {
            this.toastr.error(AppConstant.BRAND_SKU_ERROR);
          }
        );
        this.spinner.hide();
      });
  }

  /**
   * Triggered when a SKU is selected in multi select dropdown
   * @param event
   */
  selectedSKU(event: any):void {
    if (!_.includes(this.selectedSKUCodes, event.code)) {
      this.selectedSKUCodes.push(event.code); 
    }
  }

  /**
   * Triggered when a SKU is deselected in multi select dropdown
   * @param event
   */
  deSelectedSKU(event: any):void {
    if (_.includes(this.selectedSKUCodes, event.code)) {
      _.remove(this.selectedSKUCodes, (item) => item === event.code);
    }
  }

  /**
   * Triggered when all SKUs are selected in multi select dropdown
   * @param event
   */
  selectedAllSKUS(event: any):void {
    if (event && event.length) {
      this.selectedSKUCodes = [];
      this.selectedSKUCodes = _.map(event, "code");
    }
  }

  /**
   * Triggered when all SKUs are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllSKUS(_event: any):void {
    this.selectedSKUCodes = [];
  }

  /**
   * Triggered when a zone is selected in multi select dropdown
   * @param event
   */
  selectedZone(event: any) {  
    if (!_.includes(this.selectedZoneCodes, event.code)) {
      this.selectedZoneCodes.push((event.code)); 
      this.region = [];
      this.getAllRegionsByZone(this.selectedZoneCodes);  
    }
  }

  /**
   * Triggered when a zone is deselected in multi select dropdown
   * @param event
   */
  deSelectedZone(event: any):void {
    if (_.includes(this.selectedZoneCodes, event.code)) {
      _.remove(this.selectedZoneCodes, (item) => item === event.code);
      this.regionsDetails = [];
      this.region = [];
      if (this.selectedZoneCodes && this.selectedZoneCodes.length) {
        this.getAllRegionsByZone(this.selectedZoneCodes.join(","));
      }
    }
  }

  /**
   * Triggered when all zones are selected in multi select dropdown
   * @param event
   */
  selectedAllZones(event: any):void {
    if (event && event.length) {
      this.selectedZoneCodes = [];
      this.selectedTerritoryCodes = [];
      this.territoriesDetails = [];
      this.territory = [];
      event.forEach((zone: any) => {
        this.selectedZoneCodes.push(zone.code);
      });
      this.getAllRegionsByZone(this.selectedZoneCodes.join(","));
    }
  }

  /**
   * Triggered when all zones are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllZones(_event: any):void {
    this.selectedZoneCodes = [];
    this.regionsDetails = [];
    this.region = [];
    this.territoriesDetails = [];
    this.selectedTerritoryCodes = [];
    this.territory = [];
  }
  /**
   * Method to get all the zones list
   * @param code
   */
  getAllZones() :void{
    this.spinner.show();
    this.zonesService.getAllZonesByPC().subscribe({
     next : (zonesDetails: any) => {
      zonesDetails = this.dashboardService.removeHashSuffix(zonesDetails);
      this.zoneDetails = [];
      zonesDetails.forEach((zoneInfo: any) => {
        const zoneInfoObj = {
          code: zoneInfo.code,
          id: zoneInfo.id,
          name: zoneInfo.name,
        };
        this.zoneDetails.push(zoneInfoObj);
      });
      this.spinner.hide();
    },
    error: (errorResponse  : any) => { 
      let errorMsg = errorResponse.status;
      if (+errorMsg ===  401 || +errorMsg ===  404) {  
        localStorage.clear();
        this.router.navigate([""]);
        this.toastr.success("Signed Out Successfully");
      } else{
        this.toastr.error("Error while fetching zone");
      } 
      this.spinner.hide();
    },
  });
  }

  /**
   * Method to get all the territories list by zone
   * @param code
   */
  getAllTerritoriesByZone(code: any):void {
    this.spinner.show();
    this.territoriesService
      .getAllTerritoriesByZoneCode(encodeURIComponent(code))
      .subscribe((territoriesDetails: any) => {
        territoriesDetails = this.dashboardService.removeHashSuffix(territoriesDetails);
        this.territoriesDetails = [];
        if (
          territoriesDetails &&
          territoriesDetails.content &&
          territoriesDetails.content.length
        ) {
          territoriesDetails.content.forEach((territoriesInfo: any) => {
            const territoriesInfoObj = {
              code: territoriesInfo.code,
              id: territoriesInfo.id,
              name: territoriesInfo.name,
            };
            this.territoriesDetails.push(territoriesInfoObj);
          });
        }
        this.spinner.hide();
      });
  }
  /**
   * Method to get all the regions list by zone
   * @param code
   */

  getAllRegionsByZone(code: any):void{
    this.spinner.show();
    const allRegions = this.regionsService.getAllRegionsByZoneCode(code);
    allRegions.subscribe({
      next: (regionsDetails: any) => {
        regionsDetails = this.dashboardService.removeHashSuffix(regionsDetails)
        this.regionsDetails = [];
        regionsDetails.forEach((regionInfo: any) => { 
          const zoneInfoObj = {
            id: regionInfo.id,
            name: regionInfo.regionName,
            code: regionInfo.code,
          };
          this.regionsDetails.push(zoneInfoObj);
        });
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.toastr.error(AppConstant.REGION_FETCH_ERROR);
        this.spinner.hide();
      },
    });
  }
  
  /**
   * Triggered when a region is selected in multi select dropdown
   * @param event
   */
  selectedRegion(event: any):void {  
    if (!_.includes(this.selectedRegionCodes, event.code)) {
      this.selectedRegionCodes.push(event.code);
    }
  }

  /**
   * Triggered when a region is deselected in multi select dropdown
   * @param event
   */
  deSelectedRegion(event: any):void {
    if (_.includes(this.selectedRegionCodes, event.code)) {
      _.remove(this.selectedRegionCodes, (item) => item === event.code);
    }
  }

  /**
   * Triggered when all regions are selected in multi select dropdown
   * @param event
   */
  selectedAllRegions(event: any):void {
    if (event && event.length) {
      this.selectedRegionCodes = [];
      this.selectedRegionCodes = _.map(event, "code");
    }
  }

  /**
   * Triggered when all regions are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllRegions(_event: any) :void{
    this.selectedRegionCodes = [];
  }

  startDateOptions: IMyOptions = {
    dateFormat: "dd-mm-yyyy",
    showTodayBtn: false,
    inline: false,
    firstDayOfWeek: "su",
    editableDateField: false,
    showClearDateBtn: false,
    openSelectorOnInputClick: true,
    disableUntil: { day: 0, month: 0, year: 0 },
  };

  /**
   * called when start date filter is changed
   * @param event
   */
  onStartDateChanged(event: any):void {
   this.endDateModel = '';
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
  }

  /**
   * called when end date filter is changed
   * @param event
   */
  onEndDateChanged(event: any) :void{
    if (event && event.value) {
      this.endDate = "";
      this.endDate = moment(event.value, "DD-MM-YYYY").format("DD-MM-YYYY");
    } else {
      this.endDate = "";
    }
  }

   

  minKeypress(index: number): boolean { 
    this.minQuantityFlag = false;

    if (index > 0) {
      const previousSectionControl = this.sectionFormArray.at(index - 1);
      const currentSectionControl = this.sectionFormArray.at(index);

      if (previousSectionControl && currentSectionControl) {
        const maxQuantityControl = previousSectionControl.get("maxQuantity");
        const minQuantityControl = currentSectionControl.get("minQuantity");

        if (
          maxQuantityControl &&
          minQuantityControl &&
          maxQuantityControl.value <= minQuantityControl.value
        ) {
          this.minQuantityFlag = true;
        }
      }
    } else {
      this.minQuantityFlag = true;
    }

    return true;
  }
  maxKeypress(index: number): boolean { 
    this.maxQuantityFlag = false;

    const sectionControl = this.sectionFormArray.at(index);
    if (sectionControl) {
      const minQuantityControl = sectionControl.get("minQuantity");
      const maxQuantityControl = sectionControl.get("maxQuantity");

      if (
        minQuantityControl &&
        maxQuantityControl &&
        minQuantityControl.value <= maxQuantityControl.value
      ) {
        this.maxQuantityFlag = true;
      }
    }

    return this.maxQuantityFlag;
  }

  disabledButton(): boolean {
    return !(
      this.selectedBrands &&
      this.selectedBrands.length &&
      this.product &&
      this.product.length &&
      this.packshotFiles &&
      this.userForm.valid &&
      this.zoneDetails &&
      this.zoneDetails.length &&
      this.region &&
      this.region.length &&
      this.maxQuantityFlag &&
      this.minQuantityFlag &&
      this.sectionFormArray.valid
    );
  }

  get sectionFormArray() :any{
    return this.slabForm.get("sectionFormArray") as FormArray;
  }

  initializeForms(): void {
    this.slabForm = this._fb.group({
      sectionFormArray: this._fb.array([this.createItem()]),
    });
    this.maxQuant = this.slabForm.value.sectionFormArray.minQuantity;
  }
  initialValue!: number;
  slapIndex:any=1;
  createItem(): FormGroup {
    return this._fb.group({
      slab: "",
      minQuantity: [null, Validators.compose([Validators.required])],
      maxQuantity: [null, Validators.compose([Validators.required])],
      bonus: [null, Validators.compose([Validators.required])],
    });
  }

  invalidMinNumber: boolean = false;
  minValueOnChange1(value: number, i: number) :void{ 
    this.invalidMinNumber = false;
    this.minQuant = value;
    this.minFieldIndex = i;
    if (this.maxQuant >= this.minQuant) {
      this.invalidMinNumber = true;
    }
  }

  maxQuantOnChange!: number;
  maxValueOnChange(value: number):void { 
    this.invalidNumber = false;
    this.maxQuant = value;
    this.maxQuantOnChange = value;
    if (this.maxQuant <= this.minQuant) {
      this.invalidNumber = true;
    }
  }

  minFieldErrorMessage(): string {
    return this.maxQuantOnChange <
      this.sectionFormArray.value[this.minFieldIndex].minQuantity
      ? `Cannot be less than prev${this.maxQuantity}`
      : "";
  }
  
  // disabledAddButton(): boolean {
  //   return !(this.maxQuant > this.minQuant && this.sectionFormArray.valid);
  // }
  disabledAddButton(): boolean {
    const sectionFormArray = this.slabForm.get('sectionFormArray') as FormArray;
   
    for (const item of sectionFormArray.controls) {
      const minQuantity = item.get('minQuantity')?.value;
      const maxQuantity = item.get('maxQuantity')?.value;
      const bonus = item.get('bonus')?.value;
   
      if (minQuantity === null || maxQuantity === null || bonus === null) {
        this.addSlabFlag = true;
        return true;
      }
    } 
    this.addSlabFlag = false;
    return false;
  }
  

  maxFieldError(): boolean {
    if (!this.maxQuant > this.minQuant) {
      return (this.invalidNumber = true);
    }
    return false; 
  }

  cancelButton():void {
    this.router.navigate(["rewards-points"]);
  }

  slabValid = new FormControl("", [Validators.required]);
  getErrorMessage() {
    if (this.slabValid.hasError("required")) {
      return "Field is required";
    }

    return this.minQuant <=
      this.sectionFormArray.value[this.minFieldIndex - 1].maxQuantity
      ? `Can not be less than Min${this.minQuant}`
      : "";
  }
  bonusValue: boolean = false;

  bonusMethod(value: number): void {
    if (value == null) {
      this.bonusValue = true;
    }
  }

  addScheme(_value: any):void{
    this.spinner.show();
    this.submitClicked = true;
    this.sectionFormArray.value.forEach((form: any, i: any) => {
      form.slab = `Slab${i + 1}`;
    });

    let points = "";
    const pointControl = this.userForm.get("point");
    if (pointControl !== null && pointControl !== undefined) {
      const pointValue = pointControl.value;
      if (pointValue !== null && pointValue !== undefined) {
        points = pointValue.toString();
      }
    }
    let body = {
      cropId: this.selectedCropId,
      points: points,
      title: this.selectedBrandCodes ? this.selectedBrandCodes[0] : "",
      brandCode: this.selectedBrandCodes ? this.selectedBrandCodes : [],
      regionCode: this.selectedRegionCodes ? this.selectedRegionCodes : [], 
      skuCode: this.selectedSKUCodes ? this.selectedSKUCodes : [],
      zoneCode: this.selectedZoneCodes ? this.selectedZoneCodes : [], 
      startDate: moment(this.startDateModel).format('DD-MM-YYYY HH:mm:ss'),
      endDate: moment(this.endDateModel).format('DD-MM-YYYY HH:mm:ss'),
      packshot:  this.packshotFiles ?  this.packshotFiles : '',
      rewardBonus: this.sectionFormArray.value,
    };
 
    const bodyDataJSON = JSON.stringify(body);  
    const bodyDataJSONData = bodyDataJSON + "#" + this.randomNumber;
   
    const addSchemeDetails = this.rewardService.addScheme(bodyDataJSONData);
    addSchemeDetails.subscribe({
      next: (res: any) => {
        this.spinner.hide();
        this.toastr.success("Scheme Created Successfully");
        this.router.navigate(["rewards-points"]);
      },
      error: (err: any) => {  
        err = this.dashboardService.removeSuffix(err.error) 
        this.spinner.hide();
        let errorMsg = err.status;
        if(+err.status === 400){ 
          this.toastr.warning(err.message);
        }else if(+errorMsg ===  401 || +errorMsg ===  404){
          localStorage.clear();
          this.router.navigate([""]);
          this.toastr.success("Signed Out Successfully");
        }else{
          this.toastr.error(err.message);
        }
      },
    });
    
  }


  diableSubmitButton():boolean {  
    return ! (this.selectedCropId &&  this.rewardPoint && this.selectedBrandCodes && this.selectedBrandCodes.length && this.selectedSKUCodes && this.selectedSKUCodes.length && this.startDateModel && this.endDateModel && this.selectedZoneCodes && this.selectedZoneCodes.length && this.selectedRegionCodes && this.selectedRegionCodes.length && !this.addSlabFlag);
  }
 

  rewardBonusResponse(): void { 
    for (let i = 0; i < Object.keys(this.resData).length; i++) {
      this.dataArray = this.slabForm.get("sectionFormArray") as FormArray;

      this.dataArry = this.fb.group({
        slab: new FormControl({ value: this.resData[i].slab,disabled: this.disabled, }),

        minQuantity: new FormControl({
          value: this.resData[i].minQuantity,
          disabled: this.disabled,
        }),
        maxQuantity: new FormControl({
          value: this.resData[i].maxQuantity,
          disabled: this.disabled,
        }),
        bonus: new FormControl({
          value: this.resData[i].bonus,
          disabled: this.disabled,
        }),
      });
      this.dataArray.push(this.dataArry);
    } 
  }

  brandsId: any = {};
  skuCodes = [];
  skuProduct(): void { 
    this.userService
      .skuDetailsByBrandCode(this.skuDataItem)
      .subscribe((res: any) => {
        this.skuData = [];
        this.selectedSKUCodes = [];
        res.forEach(
          (skuDetails: any) => {
            let skuDetailsObj = {
              id: skuDetails.id,
              code: skuDetails.code,
              name: skuDetails.name,
            };
            this.skuData.push(skuDetailsObj);
            this.selectedSKUCodes.push(skuDetailsObj.code);
          },
          (errResponse: any) => {
            this.toastr.error(AppConstant.BRAND_SKU_ERROR);
          }
        ); 
      });
  }
  regionDropdown(): void { 
    const allRegions = this.regionsService.updateRegionByZoneId(
      this.selectedZoneCodes
    );
    allRegions.subscribe((res: any) => {
      res.forEach((regionInfo: any) => {
        const zoneInfoObj = {
          id: regionInfo.id,
          regionName: regionInfo.regionName,
          code: regionInfo.regionCode,
          zoneCode: regionInfo.zoneCode,
        };
        this.regionsList.push(zoneInfoObj);
      });
    });
  }
  
   
  sanitizeInput(input: string): string { 
    let sanitizedValue = input.replace(/<[^>]*>/g, ''); 
    sanitizedValue = sanitizedValue.replace(/[&^*#$!@()%]/g, '');
  
    return sanitizedValue;
  }

  // Event handler for input field
onInputChange(event: Event): void {
  const inputElement = event.target as HTMLInputElement;
  const inputValue = inputElement.value;
  const sanitizedValue = this.sanitizeInput(inputValue);

  inputElement.value = sanitizedValue;
}

// Event handler for paste event
onPaste(event: ClipboardEvent): void {
  event.preventDefault();
  const pastedText = event.clipboardData!.getData('text/plain');
  const sanitizedValue = this.sanitizeInput(pastedText);
  document.execCommand('insertText', false, sanitizedValue);
}
funRestNumber(event: any) {
  var k = event.charCode;

  if ((k >= 48 && k <= 57) || k == 8) {
    // Allow numbers (48-57) and backspace (8)
    return true;
  } else {
    event.preventDefault();
    this.toastr.warning('Only numbers are allowed');
    return false;
  }
}

}
 