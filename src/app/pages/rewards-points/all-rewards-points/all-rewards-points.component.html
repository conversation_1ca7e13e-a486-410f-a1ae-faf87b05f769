<div class="reward-points-container">
  <div class="reward-points-grid-container">
    <div class="reward-grid-action">
      <div class="reward-grid-search-container" *ngIf="!showErrorBlockSection">
        <div class="input-group reward-grid-search-input">
          <span class="input-group-addon">
            <i class="fa fa-search" aria-hidden="true"></i>
          </span>
          <input
            #searchBox
            class="search input-fields right-border searchbox"
            placeholder="Search"
            [(ngModel)]="model"
            (ngModelChange)="onSearch($event)"
            (keypress)="funRestCharacterNumber($event)"
          />
        </div>
      </div>

      <div class="reward-grid-action-container">
        <div class="date-filter"> 
          <div class="date-filter-content">
            <div class="start-date">
              <mat-form-field
                (click)="picker.open()"
                class="custom-datepicker-container"
                appearance="outline"
              >
                <input
                  [(ngModel)]="startDateModel"
                  (click)="picker.open()"
                  placeholder="Start Date"
                  autocomplete="off"
                  (dateChange)="onStartDateChanged($event)"
                  class="custom-datepicker reward-date-range"
                  matInput
                  readonly
                  [matDatepicker]="picker"
                  [max]="minDate"
                />
                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="picker"
                ></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
              </mat-form-field>
            </div>
            <div class="end-date">
              <mat-form-field
                (click)="picker1.open()"
                class="custom-datepicker-container"
                appearance="outline"
              >
                <input
                  [(ngModel)]="endDateModel"
                  (click)="picker1.open()"
                  placeholder="End Date"
                  autocomplete="off"
                  (dateChange)="onEndDateChanged($event)"
                  class="custom-datepicker reward-date-range"
                  [min]="startDateModel" 
                   matInput
                   readonly
                  [matDatepicker]="picker1"
                  [disabled]="!startDateModel"
                />
                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="picker1"
                ></mat-datepicker-toggle>
                <mat-datepicker #picker1></mat-datepicker>
              </mat-form-field>
            </div>
          </div>
          <span class="search-date-action">
            <span
              class="filter"
              (click)="filterSchemeByDate()"
              [ngClass]="{ disable: !startDate || !endDate }"
            >
              <i class="fa fa-filter" title="Apply Date Filters"></i>
            </span>
            <span
              *ngIf="startDate || endDate"
              class="filter"
              (click)="clearSchemeFilter()"
            >
              <i class="fa fa-close" title="Clear Date Filters"></i>
            </span>
          </span>
        </div>

        <div class="reward-grid-action-add">
          <button
            class="add export add-btn-width"
            title="Export"
            (click)="export()"
          >
            <i class="fa fa-share-square-o"></i>
          </button>
          <button
            class="add add-btn-width"
            *ngIf="isAdmin"
            title="Add Target"
            (click)="addSchemeCall()"
          >
            <i class="fa fa-plus"></i>&nbsp;Add Target
          </button>
        </div>
      </div>
    </div>
    <div class="main-campaign">
      <div class="panel panel-primary">
        <div class="panel-body">
          <div class="wizard">
            <a
              class="profile-tab reward-tab-width"
              [ngClass]="{ active: isActive }"
              (click)="userTab('isActive')"
            >
              <span>Active</span>
            </a>
            <a
              class="profile-tab reward-tab-width"
              [ngClass]="{ active: isExpired }"
              (click)="userTab('isExpired')"
            >
              <span>Expired</span>
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="no-result" *ngIf="!allRewardsData.length">
      No Scheme found
    </div>
    <div class="rewards-container">
      <div class="reward-cards-container">
        <div *ngIf="isActive" class="card-main-section">
          <div
            class="search-results"
            infinite-scroll
            [infiniteScrollDistance]="scrollDistance"
            [infiniteScrollUpDistance]="scrollUpDistance"
            (scrolled)="onScrollDown()"
          >
            <div *ngFor="let scheme of allRewardsData" class="reward-cards">
              <div class="reward-details">
                <div class="reward" [ngClass]="{ 'active-scheme': isActive }">
                  <i
                    class="fa fa-pencil reward-actions"
                    (click)="editScheme(scheme)"
                    *ngIf="isAdmin"
                    title="Edit Scheme"
                  ></i>
                  <div class="reward-content">
                    <span class="reward-fields">
                      <span class="title"
                        ><b>Scheme Title:</b>&nbsp;{{ scheme?.title }}</span
                      >
                    </span> 
                    <span *ngIf="scheme?.points"
                      ><b>Reward Points:</b> {{ scheme?.points }} pt per
                      Unit</span
                    >
                    <span *ngIf="scheme?.startDate"
                      ><b>Start Date:</b> {{ scheme?.startDate }}</span
                    >
                    <span *ngIf="scheme?.endDate"
                      ><b>End Date:</b> {{ scheme?.endDate }}</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="isExpired" class="card-main-section">
          <div *ngFor="let scheme of allRewardsData" class="reward-cards">
            <div class="reward-details">
              <div class="reward" [ngClass]="{ 'expired-scheme': isExpired }">
                <i
                  class="fa fa-pencil reward-actions"
                  (click)="editScheme(scheme)"
                  *ngIf="isAdmin"
                  title="Edit Scheme"
                ></i>
                <div class="reward-content">
                  <span class="reward-fields">
                    <span class="title"
                      ><b>Scheme Title:</b>&nbsp;{{ scheme?.title }}</span
                    >
                  </span>
                  <span *ngIf="scheme?.points"
                    ><b>Reward Points:</b> {{ scheme?.points }} pt per
                    Unit</span
                  >
                  <span *ngIf="scheme?.startDate"
                    ><b>Start Date:</b> {{ scheme?.startDate }}</span
                  >
                  <span *ngIf="scheme?.endDate"
                    ><b>End Date:</b> {{ scheme?.endDate }}</span
                  >
                </div>
              </div>
            </div>
          </div>

          <div *ngIf="totalRecordCount" class="scheme-pagination">
            <div
              *ngFor="
                let data of allRewardsData
                  | paginate
                    : {
                        itemsPerPage: perPage,
                        currentPage: currentPage,
                        id: 'pagination',
                        totalItems: totalRecordCount
                      }
              "
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
