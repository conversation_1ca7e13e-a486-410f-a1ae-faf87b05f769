import {  waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { AllRewardsPointsComponent } from './all-rewards-points.component';

describe('AllRewardsPointsComponent', () => {
    let component: AllRewardsPointsComponent;
    let fixture: ComponentFixture<AllRewardsPointsComponent>;

    beforeEach( waitForAsync(() => {
        TestBed.configureTestingModule({
    imports: [AllRewardsPointsComponent]
})
            .compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(AllRewardsPointsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
