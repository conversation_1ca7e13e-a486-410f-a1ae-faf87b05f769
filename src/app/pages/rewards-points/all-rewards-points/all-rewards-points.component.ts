import { Component, ViewEncapsulation, ElementRef } from "@angular/core";
import { Router, ActivatedRoute, NavigationEnd } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { GlobalEvents } from "../../../helpers/global.events";
import { AppConstant } from "../../../constants/app.constant";
import moment from "moment";
import { AuthenticationHelper } from "../../../helpers/authentication";
import { RewardPointsService } from "../../../app-services/reward-points-service";
import { IMyOptions } from "mydatepicker";
import { Subject } from "rxjs";
import { ngxCsv } from "ngx-csv";
import * as _ from "lodash";
import { debounceTime, distinctUntilChanged, map } from "rxjs/operators";
import { UserService } from "src/app/app-services/user-service";
import { NgxPaginationModule } from "ngx-pagination";
import { InfiniteScrollModule } from "ngx-infinite-scroll";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatInputModule } from "@angular/material/input";
import { MatFormFieldModule } from "@angular/material/form-field";
import { FormsModule } from "@angular/forms";
import { NgIf, NgClass, NgFor, DatePipe } from "@angular/common";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { DashboardService } from "src/app/app-services/dashboard.service";
import { catchError } from 'rxjs/operators';
import { Observable } from 'rxjs';



@Component({
    selector: "nga-all-rewards-points",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["../rewards-points.component.scss"],
    templateUrl: "all-rewards-points.component.html",
    imports: [
        NgIf,
        FormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatDatepickerModule,
        NgClass,
        InfiniteScrollModule,
        NgFor,
        NgxPaginationModule,
    ],
    providers: [DatePipe, BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class AllRewardsPointsComponent {
  allRewardsData: any = [];
  tableHead: any = [];
  tableColName: any = [];
  profitCenter: any = "";
  configurationSettings: any = {
    showPagination: true,
    perPage: 10,
    totalRecordCount: 0,
    currentPage: 0,
    showActionsColumn: true,
    actionsColumnName: "Actions",
    noDataMessage: "No data found",
  };
  model: any;
  showErrorBlockSection: boolean = false;
  showIndex: any = { index: null };

  showOtherFilters: any = {
    showRadioFilters: false,
    showSearch: true,
    add: true,
    showdropdown1Filters: false,
    showdropdown2Filters: false,
    showSearchiconFilters: false,
    showReport: false,
  };
  userButton: any = [];
  isAdmin: boolean = false;
  startDate: any;
  endDate: any;
  isActive: boolean = true;
  isExpired: boolean = false;
  totalRecordCount: any = "";
  perPage: any = AppConstant.PER_PAGE_ITEMS;
  modelDate: any;
  isSearch: boolean = false;
  searchedValue: string = "";
  currentPage: number = 1;
  scrollDistance = 1;
  scrollUpDistance = 2;
  startDateOptions: IMyOptions = {
    dateFormat: "dd-mm-yyyy",
    showTodayBtn: false,
    inline: false,
    firstDayOfWeek: "su",
    editableDateField: false,
    showClearDateBtn: false,
    openSelectorOnInputClick: true,
  };
  endDateOptions: IMyOptions = {
    dateFormat: "dd-mm-yyyy",
    showTodayBtn: false,
    inline: false,
    firstDayOfWeek: "su",
    editableDateField: false,
    showClearDateBtn: false,
    openSelectorOnInputClick: true,
  };
  endDateModel: any = "";
  startDateModel: any = "";
  isDateFilter: boolean = false;
  modelChanged: Subject<string> = new Subject<string>();
  PCCode: any = "";
  profitCenterCode: any = "all";
  exportData: any = [];
  minDate: any;
  maxDate: any;

  constructor(
    private routes: ActivatedRoute,
    private eRef: ElementRef,
    private router: Router,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private events: GlobalEvents,
    private userService: UserService,
    private rewardService: RewardPointsService,
    private dashboardService : DashboardService
  ) {
    this.events.setChangedContentTopText("Reward Points");
    this.isAdmin = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;
    localStorage.removeItem('reward_id');
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
  }
  ngOnInit() {
    this.setTableHeader();
    window.scrollTo(0, 0);

    this.modelChanged
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe((model: string) => {
        if (model.trim()) {
          this.allRewardsData = [];
          this.currentPage = 1;
          this.isSearch = true;
          this.searchedValue = model.trim();
          this.getPageData(1);
        } else {
          if (this.isSearch) {
            this.allRewardsData = [];
            this.currentPage = 1;
            this.isSearch = false;
            this.searchedValue = "";
            this.getPageData(1);
          }
        }
      });

    this.routes.queryParams.subscribe((item) => {
      if (item["status"]) {
        this.isActive = item["status"] == "active" ? true : false;
        this.isExpired = item["status"] == "expired" ? true : false;
        this.getAllScheme();
      } else {
        this.isActive = true;
        this.isExpired = false;
        this.getAllScheme();
      }
    });
  }

  testCall(): void {
    this.router.navigate(["rewards-points/test"]);
    this.spinner.hide();
  }
  addSchemeCall(): void {
    this.router.navigate(["rewards-points/addscheme"]);
    this.spinner.hide();
  }
  setTableHeader(): void {
    this.tableHead = ["Territory", "Brand", "Rewards & Points"];
    this.tableColName = ["territory", "brand", "rewards_and_points"];
    const pc = String(localStorage.getItem("profitCenter"));
    if (this.isAdmin) {
      this.PCCode = "";
    } else {
      if (pc) {
        this.PCCode = pc;
      }
    }
  }

  /**
   * Called for routing to edit and add rewards page
   * @param event
   */
  editScheme(event: any): void {
    if (event && event.id) {
      this.router.navigate(["rewards-points/editScheme"], {
        queryParams: { reward_id: event.id },
      });
      localStorage.setItem('reward_id', event.id)
    }
  }

  /**
   * Method for changing user tabs
   * @param data
   */
  userTab(data: any): void {
    switch (data) {
      case "isActive":
        if (!this.isActive) {
          this.isActive = true;
          this.isExpired = false;
          this.allRewardsData = [];
          this.currentPage = 1;
          this.modelDate = "";
          this.startDate = "";
          this.endDate = "";
          this.model = "";
          this.searchedValue = "";
          this.endDateModel = "";
          this.startDateModel = "";
          this.profitCenterCode = "all";
          this.PCCode = "";
          this.router.navigate(["/rewards-points"], {
            queryParams: { status: "active" },
          });
          this.setTableHeader();
        }
        break;
      case "isExpired":
        if (this.isActive) {
          this.isActive = false;
          this.isExpired = true;
          this.allRewardsData = [];
          this.currentPage = 1;
          this.modelDate = "";
          this.startDate = "";
          this.endDate = "";
          this.model = "";
          this.searchedValue = "";
          this.endDateModel = "";
          this.startDateModel = "";
          this.profitCenterCode = "all";
          this.PCCode = "";
          this.router.navigate(["/rewards-points"], {
            queryParams: { status: "expired" },
          });
          this.setTableHeader();
        }
        break;
    }
  }

  /**
   * API call to get the all schemes
   * @param page
   */
  getAllScheme(page?: any): void {
    this.spinner.show();
    let data = {
      isActive: this.isActive,
      pageLimit: this.isActive ? 10 : this.perPage,
      currentPage: page ? page - 1 : 0,
      searchedValue: encodeURIComponent(this.searchedValue),
      startDate: this.startDate ? this.startDate : "",
      endDate: this.endDate ? this.endDate : "",
      profitCenter: this.PCCode ? this.PCCode : "",
    }; 
    const schemes = this.rewardService.getAllScheme(data);
    schemes.subscribe({
      next: (schemeData: any) => { 
        schemeData = this.dashboardService.removeSuffix(schemeData);  
        if (schemeData && schemeData.content && schemeData.content.length) {
          schemeData.content.forEach((item: any) => { 
            const schemeDataObj = {
              id: item.id ? item.id : 'NA',
              title: item.title ? item.title : "---",
              profitCenterName: item.profitCenterName
                ? item.profitCenterName
                : "---",
              brands: item.brands ? item.brands : "---",
              skus: item.skus ? item.skus : "---",
              points: item.points ? item.points : "---",
              endDate: item.endDate ? item.endDate.split(' ')[0] : "---",
              startDate: item.startDate ? item.startDate.split(' ')[0] : "---",
              sbus: item.sbus ? item.sbus : "---",
              zones: item.zones ? item.zones : "---",
              regions: item.regions ? item.regions : "---",
              territories: item.territories ? item.territories : "---",
            };
            this.allRewardsData.push(schemeDataObj); 
          });
          this.spinner.hide();
          this.totalRecordCount = schemeData.totalElements
            ? schemeData.totalElements
            : "";
        } else {
          this.totalRecordCount = "0";
          this.spinner.hide();
        }
        this.events.setChangedContentTopText(
          "Reward Points (" + this.totalRecordCount + ")"
        );
      },
      error: (errorResponse) => {
        // this.toastr.error(AppConstant.GET_SCHEME_ERR);
        // this.spinner.hide();

        let errorMsg = errorResponse.status;
        if (+errorMsg ===  401 || +errorMsg ===  404) {  
          localStorage.clear();
          this.router.navigate([""]);
          this.toastr.success("Signed Out Successfully");
        } else{
          this.toastr.error(AppConstant.GET_SCHEME_ERR);
        } 
        this.spinner.hide();
      },
    });
  }

  /**
   * To handle the page change event
   * @param page
   */
  getPageData(page: any): void {
    if (page) {
      this.currentPage = page;
      this.allRewardsData = [];
      this.getAllScheme(this.currentPage);
    }
  }
  onScrollDown(): void {
    if (
      this.allRewardsData &&
      this.allRewardsData.length &&
      this.totalRecordCount &&
      this.allRewardsData.length != this.totalRecordCount
    ) {
      this.currentPage = this.currentPage + 1;
      this.getAllScheme(this.currentPage);
    }
  }

  /**
   * Method to display the data according to the searched query
   * @param event
   */
  onSearch(event: any): void {
    this.modelChanged.next(event);
  }

  /**
   * called when start date filter is changed
   * @param event
   */
  onStartDateChanged(event: any): void {
    if (event && event.value) {
      this.minDate = event.value;
      this.startDate = "";
      this.endDateModel = "";
      this.endDate = "";
      this.startDate = moment(event.value).format("DD-MM-YYYY");
      const momentDate = moment(event.value, "DD-MM-YYYY").subtract(1, "day");
      const today = new Date(event.value);
      this.minDate = today;
      this.endDateOptions = {
        dateFormat: "dd-mm-yyyy",
        showTodayBtn: false,
        inline: false,
        firstDayOfWeek: "su",
        editableDateField: false,
        showClearDateBtn: false,
        openSelectorOnInputClick: true,
        disableUntil: {
          year: parseInt(momentDate.format("YYYY")),
          month: parseInt(momentDate.format("MM")),
          day: parseInt(momentDate.format("DD")),
        },
      };
    } else {
      this.startDate = "";
      this.endDateOptions = {
        dateFormat: "dd-mm-yyyy",
        showTodayBtn: false,
        inline: false,
        firstDayOfWeek: "su",
        editableDateField: false,
        showClearDateBtn: false,
        openSelectorOnInputClick: true,
        disableUntil: {
          day: 0,
          month: 0,
          year: 0,
        },
      };
    }

    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
  }

  /**
   * called when end date filter is changed
   * @param event
   */
  onEndDateChanged(event: any): void {
    if (event && event.value) {
      this.endDate = "";
      this.endDate = moment(event.value, "DD-MM-YYYY").format("DD-MM-YYYY");
    } else {
      this.endDate = "";
    }
  }

  filterSchemeByDate(): void {
    if (this.startDate && this.endDate) {
      this.allRewardsData = [];
      this.isDateFilter = true;
      this.currentPage = 1;
      this.getAllScheme();
    }
  }

  clearSchemeFilter(): void {
    this.startDate = "";
    this.endDate = "";
    this.minDate = "";
    this.startDateModel = "";
    this.endDateModel = "";
    this.endDateOptions = {
      dateFormat: "dd-mm-yyyy",
      showTodayBtn: false,
      inline: false,
      firstDayOfWeek: "su",
      editableDateField: false,
      showClearDateBtn: false,
      openSelectorOnInputClick: true,
      disableUntil: {
        day: 0,
        month: 0,
        year: 0,
      },
    };
    if (this.isDateFilter) {
      this.isDateFilter = false;
      this.allRewardsData = [];
      this.currentPage = 1;
      this.getAllScheme();
    }
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
  }

  /**
   * Called when the profit center dropdown is changed
   * @param event
   * @constructor
   */
  PCDropDownChange(event: any): void {
    this.allRewardsData = [];
    this.currentPage = 1;
    if (event && event == "all") {
      this.PCCode = "";
    } else {
      this.PCCode = event;
    }
    this.getAllScheme();
  }

  export(): void {
    this.getAllSchemeExportData();
  }


  getAllSchemeExportData(): void {
    window.scrollTo(0, 0);
    this.spinner.show();
    let data = {
      isActive: this.isActive,
      startDate: this.startDate ? this.startDate : "",
      endDate: this.endDate ? this.endDate : "",
      profitCenter: this.PCCode,
      currentPage  : this.currentPage ? this.currentPage : "",
      searchedValue: encodeURIComponent(this.searchedValue),
      pageLimit: this.isActive ? 1 : 1,
    };
    const exports: any = this.rewardService.getAllExportSchemes(data);
    exports.subscribe(
      (exportsData: any) => { 
        exportsData = this.dashboardService.removeSuffix(exportsData);
        this.exportData = [];
        if (exportsData && exportsData.content.length) {
          exportsData.content.forEach((exportInfo: any) => {   
            let exportObj = {
              scheme_title: exportInfo.title ? exportInfo.title : "",
              profit_center_name: exportInfo.profitCenter ? exportInfo.profitCenter.name : "",
              profit_center_code: exportInfo.profitCenter ? exportInfo.profitCenter.code : "",
              points: exportInfo.points ? exportInfo.points : "",
              zone_name: exportInfo.zones
                ? _.map(exportInfo.zones, "name").join(", ")
                : "",
              zone_code: exportInfo.zones
                ? _.map(exportInfo.zones, "code").join(", ")
                : "",
              region_name: exportInfo.regions
                ? _.map(exportInfo.regions, "regionName").join(", ")
                : "",
              region_code: exportInfo.regions
                ? _.map(exportInfo.regions, "code").join(", ")
                : "", 
              brand_name: exportInfo.brands
                ? _.map(exportInfo.brands, "name").join(", ")
                : "",
              brand_code: exportInfo.brands
                ? _.map(exportInfo.brands, "code").join(", ")
                : "",
              sku_name: exportInfo.skus
                ? _.map(exportInfo.skus, "name").join(", ")
                : "",
              sku_code: exportInfo.skus
                ? _.map(exportInfo.skus, "code").join(", ")
                : "", 
                start_date: exportInfo.startDate ? exportInfo.startDate : "",
              end_date: exportInfo.endDate ? exportInfo.endDate : "", 
            };
            this.exportData.push(exportObj);
          });
          let options = {
            fieldSeparator: ",",
            quoteStrings: '"',
            decimalseparator: ".",
            showLabels: true,
            headers: [
              "Scheme Title",
              "Profit Center Name",
              "Profit Center Code",
              "Points",
              "Zone Name",
              "Zone Code",
              "Region Name",
              "Region Code", 
              "Brand Name",
              "Brand Code",
              "SKU Name",
              "SKU Code", 
              "Start Date",
              "End Date", 
            ],
          };
          new ngxCsv(this.exportData, "Scheme Details", options);
        }else{
          this.toastr.warning("No data available to export!");
        }
        this.spinner.hide();
      },
      () => {
        this.toastr.error(AppConstant.EXPORT_ERROR);
        this.spinner.hide();
      }
    );
  }

  funRestCharacterNumber(event: any) {
    const inputValue = event.target.value;
    const key = event.key;
  
    const isNumberOrCharacter = /^[0-9a-zA-Z]+$/.test(key);
  
    if (inputValue.length === 0 && key === ' ') { 
      event.preventDefault();
    } else if (!isNumberOrCharacter && key !== ' ') { 
      event.preventDefault();
      this.toastr.warning('Only numbers and characters are allowed');
    }
  }
}
