import { Component, OnChanges, OnInit, SimpleChanges, TemplateRef, ViewChild, ViewEncapsulation, HostListener } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import * as _ from 'lodash';
import moment from 'moment';
import { debounceTime, Subject, tap, catchError, of } from 'rxjs';
import { ngxCsv } from 'ngx-csv';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Utility } from "src/app/shared/utility/utility";
import { SidebarServiceService } from 'src/app/app-services/sidebar-service.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { DynamicTableComponent } from 'src/app/shared/data-table/data-table.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatNativeDateModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatPaginatorModule } from '@angular/material/paginator';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { GlobalEvents } from 'src/app/helpers/global.events';
import {  RewardPointsService } from 'src/app/app-services/reward-points-service';
import { AppConstant } from 'src/app/constants/app.constant';
import { BaThemeSpinner } from 'src/app/theme/services/baThemeSpinner/baThemeSpinner.service';
import { AuthenticationHelper } from 'src/app/helpers/authentication';
// import { FinanceReviewModalComponent } from "../../../modals/finance-review-modal/finance-review-modal.component";
import { ToastFixService } from 'src/app/shared/services/toast-fix.service';
import { UserService } from 'src/app/app-services/user-service';

import { FinanceReviewService } from 'src/app/app-services/financial-review.service';
import { TargetModalComponent } from "../../../modals/target-modal/target-modal.component";

interface ConfigurationSettings {
  showPagination: boolean;
  perPage: number;
  totalRecordCount: number;
  currentPage: number;
  showActionsColumn: boolean;
  actionsColumnName: string;
  noDataMessage: string;
  showEdit: boolean;
  isApprove: boolean,
  isRejected: boolean,
  showStatus?: boolean,
  changeStatus?: boolean
}

@Component({
  selector: 'app-finance-review',
  templateUrl: './finance-review.component.html',
  styleUrls: ['./finance-review.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatInputModule,
    MatButtonToggleModule,
    NgxPaginationModule,
    MatNativeDateModule,
    DynamicTableComponent,
    MatDatepickerModule,
    MatFormFieldModule,
    MatButtonModule,
    MatPaginatorModule,
    AngularMultiSelectModule,
    TargetModalComponent
],
})
export class FinanceReviewComponent implements OnInit, OnChanges {
  @ViewChild('filterMenuDailog') filterMenuDailog!: TemplateRef<any>;
  @ViewChild('addEditSlabDailog') addEditSlabDailog!: TemplateRef<any>;
  @ViewChild('filterMenuDailogProgress') filterMenuDailogProgress!: TemplateRef<any>;
  @ViewChild('applyConfigurationsDialog') applyConfigurationsDialog!: TemplateRef<any>;
  @ViewChild('historyDialog') historyDialog!: TemplateRef<any>;
  modalOpen: boolean = false;
  modalMode: 'edit' | 'add' = 'edit';
  financeReviewDefaults: any = {};

  filterMenuDailogRef!: MatDialogRef<any>;
  filterMenuDailogProgressRef!: MatDialogRef<any>;
  applyConfigurationsDialogRef!: MatDialogRef<any>;
  historyDialogRef!: MatDialogRef<any>;
  financeReviewHistoryData: any = [];
  isActiveFinanceReview: boolean = true;
  isFinanceReviewProgress: boolean = false;
  isForagesDistributor: boolean = true;
  isFcDistributor: boolean = false;
  isForagesRetailer: boolean = false;
  isFcRetailer: boolean = false;
  redeemedHistoryFcSeasonDropdownData: any = [];
  redeemedHistoryFCSeason: any = [];
  redeemedHistoryForagesSeasonDropdownData: any = [];
  redeemedHistoryForagesSeason: any = [];
  tableHead: any = [];
  tableColName: any = [];
  totalRecordCount = 0;
  perPage = AppConstant.PER_PAGE_ITEMS;
  showIndex: any = { index: null };
  redeemedMethod: any = [];
  redeemedMethodDetails: any = [];
  selectedSeasonDropdownData: number = 0;
  selectedRedeemedMethod: string = '';
  regionDataList: any = [];
  zoneDataList: any = [];
  selectedRegion: any = [];
  selectedZone: any = [];
  regionValue: any;
  zoneValue: any;
  redeemedHistorySeasonDropdownSettings = {
    singleSelection: true,
    clearOnSelection: false,
    showUncheckAll: false,
    text: 'Select Season-Year',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    idField: 'id',
    enableFilterSelectAll: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  redeemedHistoryForagesSeasonDropdownSettings = {
    singleSelection: true,
    clearOnSelection: false,
    showUncheckAll: false,
    text: 'Select Year',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    idField: 'id',
    enableFilterSelectAll: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  redeemedMethodSeasonDropdownSettings = {
    singleSelection: true,
    clearOnSelection: false,
    showUncheckAll: false,
    text: 'Select Finance Review Status',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    idField: 'id',
    enableFilterSelectAll: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  dateForm: FormGroup;
  configurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 0,
    showActionsColumn: true,
    actionsColumnName: 'Actions',
    noDataMessage: 'No data found',
    showEdit: false,
    isApprove: false,
    isRejected: false
  };
  model: any;
  showErrorBlockSection: boolean = false;
  showOtherFilters: any = {
    showRadioFilters: false,
    showSearch: true,
    add: false,
    showdropdown1Filters: false,
    showdropdown2Filters: false,
    showSearchiconFilters: false,
    showReport: false,
    export: true,
  }
  historyData: any = [];
  configData: any = {};
  userButton: any = [];
  custId: any = '1';
  startDate: any;
  endDate: any;
  modelDate: any;
  endDateModel: any = '';
  startDateModel: any = '';
  isDateFilter: boolean = false;
  currentPage: number = 0;
  searchedString: string = '';
  isSearch: boolean = false;
  isAdmin: boolean = false;
  modelChanged: Subject<string> = new Subject<string>();
  exportData: any = [];
  financeReviewFilters: any = '';
  date: any;
  currentDate: any;
  cropTab = '';
  firstDay: any;
  formattedFirstDay: any;
  isTable: boolean = true;
  isMap: boolean = false;
  dateData: any;
  toDateAPI!: string;
  fromDateAPI!: string;
  startDateAPI!: string;
  endDateAPI!: string;
  minDate: any;
  maxDate: any;
  dateRange!: FormGroup;
  isFieldCrop: boolean = true;
  isForages: boolean = false;
  userInfo: any; 
  userBusinessUnit: any;
  roleId: any;
  totalCount: number = 0;
  addEditSlabDailogRef: MatDialogRef<unknown, any> | undefined;

  containers = [
    { icon: '📄', text: 'Upload PDF here', buttonText: 'Upload PDF' },
    { icon: '📄', text: 'Upload XML here', buttonText: 'Upload XML' },
    { icon: '📄', text: 'Upload File here', buttonText: 'Upload File' }
  ];
  userRole: string = '';
  isActionsDropdownOpen: boolean = false;
  activeTabTitle: string = 'Finance Review'; // For dialog messages

  // Configuration data for Apply/Edit Configurations popup
  configurationData: any = {
    leaders: {
      npp: null,
      cp: null,
      total: null
    },
    grower: {
      npp: 'NA',
      cp: 'NA',
      total: null
    }
  };

  // Configuration dialog state
  isEditMode: boolean = false;
  configurationDialogTitle: string = 'Apply Configurations';
  hasExistingConfiguration: boolean = false;

  

  // History data for History popup
  // historyData = [
  //   {
  //     type: 'Leaders',
  //     npp: '25%',
  //     cp: '35%',
  //     total: '95%',
  //     updatedBy: 'Lucky',
  //     updatedOn: '24-03-2024'
  //   },
  //   {
  //     type: 'Grower',
  //     npp: 'NA',
  //     cp: 'NA',
  //     total: '100%',
  //     updatedBy: 'Lucky',
  //     updatedOn: '24-03-2024'
  //   },
  //   {
  //     type: 'Leaders',
  //     npp: '25%',
  //     cp: '35%',
  //     total: '95%',
  //     updatedBy: 'Lucky',
  //     updatedOn: '28-03-2025'
  //   },
  //   {
  //     type: 'Grower',
  //     npp: 'NA',
  //     cp: 'NA',
  //     total: '100%',
  //     updatedBy: 'Lucky',
  //     updatedOn: '28-03-2025'
  //   },
  //   {
  //     type: 'Leaders',
  //     npp: '25%',
  //     cp: '35%',
  //     total: '95%',
  //     updatedBy: 'Lucky',
  //     updatedOn: '21-07-2025'
  //   },
  //   {
  //     type: 'Grower',
  //     npp: 'NA',
  //     cp: 'NA',
  //     total: '100%',
  //     updatedBy: 'Lucky',
  //     updatedOn: '21-07-2025'
  //   }
  // ];

  constructor(
    private routes: ActivatedRoute,
    private userService: UserService,
    private router: Router,
    private fb: FormBuilder,
    private financeExceptionService: FinanceReviewService,
    public toastr: ToastrService,
    private _spinner: BaThemeSpinner,
    private events: GlobalEvents,
    // private rewardService: RewardPointsService,
    private utility: Utility,
    private rewardPointService: RewardPointsService,
    private sidebarService: SidebarServiceService,
    public dialog: MatDialog,
    private toastFixService: ToastFixService
  ) {
    this.roleId = localStorage.getItem('roleID');
    this.userInfo = localStorage.getItem('userInfo');
    this.userBusinessUnit = JSON.parse(this.userInfo)?.scanCategory?.id;
    this.events.setChangedContentTopText('Finance Review Management');
    this._spinner.hide();
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
    let isValue: any = AuthenticationHelper.getRoleID();
    this.dateData = moment(new Date()).add(1, 'days');
    this.dateForm = this.fb.group({
      startDatePicker: [this.fromDateAPI],
      endDatePicker: [this.endDateAPI],
    });
    // this.rewardService._tableDialogBox.emit('hide');
  }

  ngOnInit() {
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    if (localRole) {
      this.setRoleBasedConfig(localRole);
      this._spinner.hide();
    }
    this.userService.userRole$.subscribe(apiRole => {
      const apiRoleFormatted = apiRole?.trim().toUpperCase();
      if (apiRoleFormatted && apiRoleFormatted !== localRole) {
        this.setRoleBasedConfig(apiRoleFormatted);
        this._spinner.hide();
      }
    });

    this._spinner.hide();
    this.setDateRange();
    this.financeReviewFilters = 'redeemed';
    this.cropTab = 'FINANCE_REVIEW';
    window.scrollTo(0, 0);
    this._spinner.hide();
    this.modelChanged.pipe(
      debounceTime(600),
    ).subscribe((model: any) => {
      if (model?.trim()) {
        this.isSearch = true;
        this.searchedString = model.trim();
        if (this.isFinanceReviewProgress) {
          this.getAllFinanceReviewProgressData(1);
        } else {
          this.getPageData(1);
        }
      } else {
        this.isSearch = false;
        this.searchedString = '';
        if (this.isFinanceReviewProgress) {
          this.getAllFinanceReviewProgressData(1);
        } else {
          this.getPageData(1);
        }
      }
    });
    this.getFinanceConfigData()
    this.activeTabRoleBased();
    this.checkExistingConfiguration();
  }

  ngOnChanges(changes: SimpleChanges): void {
  }

  private setRoleBasedConfig(currentRole: string) {
    this.isAdmin = currentRole === 'ADMIN';
    this.userRole = currentRole;
    this.configurationSettings.showEdit = this.isAdmin;
  }

  editFinanceReview(data: any) {
    // if (data?.leaderId) {
    //   this.financeExceptionService.editFinanceReview(data).subscribe({
    //     next: (response: any) => {
    //       try {
    //         if (typeof response === 'string' && response) {
    //           try {
    //             const decrypted = this.utility.decrypt(response);
    //             response = JSON.parse(decrypted);
    //           } catch (e) {
    //           }
    //         }
            
    //         this.toastr.success("Finance review edited successfully");
    //         this.getAllFinanceReviewData();
    //       } catch (error) {
    //         console.error('Error processing response:', error);
    //         this.toastr.error("Failed to edit finance review");
    //       }
    //     },
    //     error: (error: any) => {
    //       try {
    //         if (typeof error.error === 'string') {
    //           const decrypted = this.utility.decrypt(error.error);
    //           error = JSON.parse(decrypted);
    //         }
    //         this.toastr.error(error.message || "Failed to edit finance review");
    //       } catch (e) {
    //         this.toastr.error("Failed to edit finance review");
    //       }
    //     }
    //   });
    // }
  }

  openAddSlabDialog(data?: any) {
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);

    this.addEditSlabDailogRef = this.dialog.open(this.addEditSlabDailog, {
      width: '600px',
      disableClose: false,
      panelClass: 'custom-popup',
      data: data,
      hasBackdrop: true,
    });

    this.addEditSlabDailogRef.afterClosed().subscribe(() => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  handleOpenFinanceReviewPopUp() {
    this.modalOpen = true;
    this.modalMode = 'add';
  }

  toggleActionsDropdown() {
    this.isActionsDropdownOpen = !this.isActionsDropdownOpen;
  }

  handleApplyConfigurations() {
    this.isActionsDropdownOpen = false;
    this.isEditMode = false;
    this.configurationDialogTitle = 'Apply Configurations';
    this.resetConfigurationData();
    this.openApplyConfigurationsDialog();
  }

  handleEditConfigurations() {
    this.getFinanceConfigData()
    this.isActionsDropdownOpen = false;
    this.isEditMode = true;
    this.configurationDialogTitle = 'Edit Configurations';
    this.loadExistingConfiguration()
    this.openApplyConfigurationsDialog();
  }

  resetConfigurationData() {
    this.configurationData = {
      leaders: {
        npp: null,
        cp: null,
        total: null
      },
      grower: {
        npp: 'NA',
        cp: 'NA',
        total: null
      }
    };
  }

  loadExistingConfiguration() {
    // Map API response data to form structure
    if (this.configData) {
    // Set configuration data with a delay
        setTimeout(() => {
          this.configurationData = {
            leaders: {
              npp: this.configData.nppPercentage || null,
              cp: this.configData.cpPercentage || null,
              total: this.configData.totalPercentage || null
            },
            grower: {
              npp: 'NA',
              cp: 'NA',  
              total: this.configData.totalGrowerPercentage || null
            }
          };
          
    }, 500); 
    } else {
      // Fallback to default values if no API data
      this.resetConfigurationData();
    }
  }

  checkExistingConfiguration() {
    // Check if configuration data exists from API
    setTimeout(() => {
      this.hasExistingConfiguration = this.configData && Object.keys(this.configData).length > 0 && this.configData.id;
    }, 1000);
  }

  handleHistory() {
    this.isActionsDropdownOpen = false;
    this.getHistoryData();
    this.openHistoryDialog();
  }

  openApplyConfigurationsDialog() {
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);
    this.loadExistingConfiguration();
    this.applyConfigurationsDialogRef = this.dialog.open(this.applyConfigurationsDialog, {
      width: '800px',
      maxHeight: '80vh',
      disableClose: false,
      panelClass: 'apply-configurations-dialog-container',
      hasBackdrop: true,
    });

    this.applyConfigurationsDialogRef.afterClosed().subscribe(() => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  closeApplyConfigurationsDialog() {
    this.applyConfigurationsDialogRef.close();
    // Automatically reinitialize form data when dialog is closed
    this.resetConfigurationData();
  }

submitConfigurations() {
    // Validate all required fields exist and are valid
    const errors = [];
    
    if (typeof this.configurationData.leaders?.npp === 'undefined' || this.configurationData.leaders.npp === null) {
        errors.push('NPP percentage is required');
    }
    
    if (typeof this.configurationData.leaders?.cp === 'undefined' || this.configurationData.leaders.cp === null) {
        errors.push('CP percentage is required');
    }
    
    if (typeof this.configurationData.leaders?.total === 'undefined' || this.configurationData.leaders.total === null) {
        errors.push('Total percentage is required');
    }
    
    if (typeof this.configurationData.grower?.total === 'undefined' || this.configurationData.grower.total === null) {
        errors.push('Grower total percentage is required');
    }

    // If any errors, show them and return
    if (errors.length > 0) {
        this.toastr.error(`Validation errors: ${errors.join(', ')}`);
        return;
    }

    // Prepare data for API
    const apiData = {
        nppPercentage: this.configurationData.leaders.npp,
        cpPercentage: this.configurationData.leaders.cp,
        totalPercentage: this.configurationData.leaders.total,
        totalGrowerPercentage:this.configurationData.grower.total
    };

    // Additional validation for numeric values
    if (isNaN(apiData.nppPercentage) || isNaN(apiData.cpPercentage) || 
        isNaN(apiData.totalPercentage) || isNaN(apiData.totalGrowerPercentage)) {
        this.toastr.error('All percentages must be numeric values');
        return;
    }

    const apiEditData = {
        id: this.configData?.id,
        nppPercentage: this.configurationData.leaders.npp,
        cpPercentage: this.configurationData.leaders.cp,
        totalPercentage: this.configurationData.leaders.total,
        totalGrowerPercentage:this.configurationData.grower.total
    };
    // Make API call
    this.configData?.id ? this.updateFinanceReviewConfigDataAPI(apiEditData) : this.addConfigurationAPI(apiData);
    this.getFinanceConfigData()

}

updateFinanceReviewConfigDataAPI(data: any){
  this._spinner.show();

  // Use different APIs based on active tab
  const apiCall = this.isActiveFinanceReview
    ? this.financeExceptionService.updateFinanceReviewConfigData(data)
    : this.financeExceptionService.updateExceptionConfigData(data);

  apiCall.subscribe({
    next: (res: any) => {
      try {
        const result = this.utility.decryptString(res);
        this.toastr.success(result);
        this.closeApplyConfigurationsDialog();
        this._spinner.hide()
      } catch (e) {
        console.error('Process Error:', e);
        this.toastr.error('Response processing failed');
        this._spinner.hide()
      }
    },
    error: (err: any) => {
      try {
        const error = this.parseError(err.error);
        error?.message === 'Full authentication required'
          ? (localStorage.clear(), this.toastr.success('Signed Out'))
          : this.toastr.error(error?.message || 'Failed');
      } catch (e) {
        console.error('Error parse failed:', e);
        this.toastr.error('Configuration failed');
      }
    }
  });
}

addConfigurationAPI(data: any) {
  this._spinner.show();

  // Use different APIs based on active tab
  const apiCall = this.isActiveFinanceReview
    ? this.financeExceptionService.applyConfigurations(data)
    : this.financeExceptionService.applyExceptionConfigurations(data);

  apiCall.subscribe({
    next: (res: any) => {
      try {
        const result = this.utility.decryptString(res);
        this.toastr.success(result);
        this.closeApplyConfigurationsDialog();
        this._spinner.hide()
      } catch (e) {
        console.error('Process Error:', e);
        this.toastr.error('Response processing failed');
        this._spinner.hide()
      }
    },
    error: (err: any) => {
      try {
        const error = this.parseError(err.error);
        error?.message === 'Full authentication required'
          ? (localStorage.clear(), this.toastr.success('Signed Out'))
          : this.toastr.error(error?.message || 'Failed');
      } catch (e) {
        console.error('Error parse failed:', e);
        this.toastr.error('Configuration failed');
      }
    }
  });
}



private parseError(err: any) {
  if (typeof err === 'string') {
    try { return JSON.parse(err); } 
    catch { 
      try { 
        const decrypted = this.utility.decrypt(err);
        return JSON.parse(decrypted); 
      } catch { return { message: err }; }
    }
  }
  return err;
}

  openHistoryDialog() {
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);

    this.historyDialogRef = this.dialog.open(this.historyDialog, {
      width: '900px',
      maxHeight: '90vh',
      disableClose: false,
      panelClass: 'history-dialog-container',
      hasBackdrop: true,
    });

    this.historyDialogRef.afterClosed().subscribe(() => {
      this.historyData = [];
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  closeHistoryDialog() {
    this.historyDialogRef.close();
  }

  // Method to close filter dialogs safely
  closeFilterDialog() {
    try {
      if (this.filterMenuDailogRef) {
        this.filterMenuDailogRef.close();
      }
      if (this.filterMenuDailogProgressRef) {
        this.filterMenuDailogProgressRef.close();
      }
    } catch (error) {
      console.log('Error closing filter dialog:', error);
    }
  }

  // Status toggle methods for table actions
  toggleStatus(data: any) {
    const newStatus = data.status === 'Active' ? 'Inactive' : 'Active';
    const action = newStatus === 'Active' ? 'activate' : 'deactivate';

    // Simulate API call
    this._spinner.show();
    setTimeout(() => {
      data.status = newStatus;
      data.isActive = newStatus === 'Active';
      this._spinner.hide();
      this.toastr.success(`Item ${action}d successfully!`);

      // Refresh data
      if (this.isActiveFinanceReview) {
        this.getAllFinanceReviewData();
      } else {
        this.getAllFinanceReviewProgressData();
      }
    }, 1000);
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.actions-dropdown')) {
      this.isActionsDropdownOpen = false;
    }
  }

  handleFinanceReviewSubmit(requestBody: any) {
    if (!requestBody?.leaderId) {
      return;
    }
  }

  openEditFinanceReviewDialog(data?: any) {
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);

    this.addEditSlabDailogRef = this.dialog.open(this.addEditSlabDailog, {
      width: '600px',
      disableClose: false,
      panelClass: 'custom-popup',
      data: data,
      hasBackdrop: true,
    });

    this.addEditSlabDailogRef.afterClosed().subscribe(() => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  activeTabRoleBased() {
    if (this.userBusinessUnit == 2) {
      this.totalCount = 0;
      this.isFieldCrop = false;
      this.isForages = true;
      this.isActiveFinanceReview = true;
      this.isFinanceReviewProgress = false;
      this.financeReviewHistoryTab('isFinanceReview');
      this.getAllRedeemedMethodData();
    } else if (this.userBusinessUnit == 1) {
      this.totalCount = 0;
      this.isFieldCrop = true;
      this.isForages = false;
      this.isActiveFinanceReview = true;
      this.isFinanceReviewProgress = false;
      this.financeReviewHistoryTab('isFinanceReviewProgress');
      this.getAllRedeemedMethodData();
    } else if (this.roleId == 1 && this.userBusinessUnit == null) {
      this.totalCount = 0;
      this.isActiveFinanceReview = true;
      this.isFinanceReviewProgress = false;
      this.financeReviewHistoryTab('isActiveFinanceReview');
      this.getAllRedeemedMethodData();
    }
  }

  ngOnDestroy() {
    this.modelChanged.unsubscribe();
  }

  setDateRange() {
    this.dateRange = this.fb.group({
      start: [''],
      end: [''],
    });
  }

  setDateObject(date: any) {
    if (date) {
      const dateObject = {
        date: {
          year: date.getFullYear(),
          month: date.getMonth() + 1,
          day: date.getDate(),
        },
      };
      return dateObject;
    } else {
      return null;
    }
  }

  setTableHeader() {
    this.getRegion();
    if (this.isActiveFinanceReview) {
      this.tableHead = [
        'Region',
        'Zone',
        'Leader Name',
        'CP (Mex$)',
        'Achieved CP(Mex$)',
        'NPP (Mex$)',
        'Achieved NPP(Mex$)',
        'Achieved NPP(%)',
        'Total (Mex$)',
        'Achieved Total(Mex$)',
        'Achieved Total(%)',
        'Bonification Amount',
        'Bonification',
      ];

      this.tableColName = [
        'region',
        'zone',
        'leaderName',
        'cropProtectionAmount',
        'achievedCP',
        'nppAmount',
        'achievedNPPAmount',
        'achivedNPPPercentage',
        'total',
        'achievedTotal',
        'achievedTotalPercentage',
        'bonificationAmount',
        'bonification',
      ];

      // Configure actions for Finance Review tab (only active/inactive)
      this.configurationSettings.showActionsColumn = true;
      this.configurationSettings.showEdit = false;
      this.configurationSettings.isApprove = false;
      this.configurationSettings.isRejected = false;
      this.configurationSettings.showStatus = true;
      this.configurationSettings.changeStatus = true;

      this.financeReviewHistoryData = [];
      this.getAllFinanceReviewData();
    } else {
      this.tableHead = [
        'Region',
        'Zone',
        'Leader Name',
        'CP (Mex$)',
        'Achieved CP(Mex$)',
        'NPP (Mex$)',
        'Achieved NPP(Mex$)',
        'Achieved NPP(%)',
        'Total (Mex$)',
        'Achieved Total(Mex$)',
        'Achieved Total(%)',
        'Bonification Amount',
        'Bonification',
        'Exception Status'
      ];
      this.tableColName = [
        'region',
        'zone',
        'leaderName',
        'cropProtectionAmount',
        'achievedCP',
        'nppAmount',
        'achievedNPPAmount',
        'achivedNPPPercentage',
        'total',
        'achievedTotal',
        'achievedTotalPercentage',
        'bonificationAmount',
        'bonification',
        'exceptionStatus'
      ];

      // Configure actions for Exception tab (active/inactive, approve/reject)
      this.configurationSettings.showActionsColumn = true;
      this.configurationSettings.showEdit = false;
      this.configurationSettings.isApprove = true;
      this.configurationSettings.isRejected = true;
      this.configurationSettings.showStatus = true;
      this.configurationSettings.changeStatus = true;
      this.financeReviewHistoryData = [];
      this.getAllFinanceReviewProgressData();

    }
  }

  tabChanged(tabChangeEvent: any): void {
    this.cropTab = tabChangeEvent;
  }

  userRoleRewardPoints: string = '';
  financeReviewHistoryTab(data: any) {
    switch (data) {
      case 'isActiveFinanceReview':
        this.financeReviewFilters = 'redeemed';
        this.userRoleRewardPoints = 'isActiveFinanceReview';
        this.totalCount = 0;
        this.isActiveFinanceReview = true;
        this.isFinanceReviewProgress = false;
        this.currentPage = 0;
        this.activeTabTitle = 'Finance Review';
        this.events.setChangedStatusText('Finance Review'); // Set for dynamic table dialog
        this.selectedSeasonDropdownData = 0;
        this.redeemedHistoryForagesSeason = [];
        this.redeemedHistoryFCSeason = [];
        this.searchedString = '';
        this.model = '';
        this.getFinanceConfigData()
        // this.router.navigate(['/financial-review']);
        this.startDate = '';
        this.endDate = '';
        this.startDateModel = '';
        this.endDateModel = '';
        this.fromDateAPI = '';
        this.toDateAPI = '';
        this.startDateAPI = '';
        this.endDateAPI = '';
        this.dateForm.controls['startDatePicker'].setValue('');
        this.dateForm.controls['endDatePicker'].setValue('');
        this.redeemedMethod = [];
        this.selectedRedeemedMethod = '';
        this.configurationSettings.showActionsColumn = false;
        this.setTableHeader();
        break;
      case 'isFinanceReviewProgress':
        this.financeReviewFilters = 'redeemed';
        this.userRoleRewardPoints = 'isFinanceReviewProgress';
        this.totalCount = 0;
        this.isActiveFinanceReview = false;
        this.isFinanceReviewProgress = true;
        this.currentPage = 0;
        this.activeTabTitle = 'Exception';
        this.events.setChangedStatusText('Exception'); // Set for dynamic table dialog
        this.redeemedMethod = [];
        this.selectedRedeemedMethod = '';
        this.selectedSeasonDropdownData = 0;
        this.redeemedHistoryForagesSeason = [];
        this.redeemedHistoryFCSeason = [];
        this.searchedString = '';
        this.model = '';
        // this.router.navigate(['/financial-review']);
        this.startDate = '';
        this.endDate = '';
        this.startDateModel = '';
        this.endDateModel = '';
        this.fromDateAPI = '';
        this.toDateAPI = '';
        this.startDateAPI = '';
        this.endDateAPI = '';
        this.dateForm.controls['startDatePicker'].setValue('');
        this.dateForm.controls['endDatePicker'].setValue('');
        this.configurationSettings.showActionsColumn = true;
        this.getFinanceConfigData();
        this.setTableHeader();
        break;
      default:
        this.totalCount = 0;
        this.financeReviewFilters = 'redeemed';
        this.isActiveFinanceReview = true;
        this.isFinanceReviewProgress = false;
        this.isFieldCrop = true;
        this.isForages = false;
        this.currentPage = 0;
        this.searchedString = '';
        this.model = '';
        this.selectedRedeemedMethod = '';
        this.redeemedMethod = [];
        // this.router.navigate(['/financial-review']);
        this.setTableHeader();
        break;
    }
  }

  onSearch(event: any) {
    this.modelChanged.next(event);
  }

  clearSearch() {
    this.searchedString = '';
    this.model = '';
    this.isSearch = false;
    if (this.isFinanceReviewProgress) {
      this.getAllFinanceReviewProgressData();
    } else {
      this.getAllFinanceReviewData();
    }
  }

  filterDropdown(data?: any) {
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);
    this.filterMenuDailogRef = this.dialog.open(this.filterMenuDailogProgress, {
      width: '18%',
      height: '260px',
      position: {
        top: '17%',
        right: '2%',
      },
      backdropClass: 'custom-backdrop',
      panelClass: 'filter-dialog-container',
      data: data,
      disableClose: false,
      hasBackdrop: true,
    });
    this.filterMenuDailogRef.afterClosed().subscribe((result: any) => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  filterDropdownProgress(data?: any){
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);
    this.filterMenuDailogProgressRef = this.dialog.open(this.filterMenuDailogProgress, {
      width: '18%',
      height: '260px',
      position: {
        top: '17%',
        right: '2%',
      },
      backdropClass: 'custom-backdrop',
      panelClass: 'filter-dialog-container',
      data: data,
      disableClose: false,
      hasBackdrop: true,
    });
    this.filterMenuDailogProgressRef.afterClosed().subscribe((result: any) => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  clearFilter() {
    this.startDate = '';
    this.endDate = '';
    this.startDateModel = '';
    this.endDateModel = '';
    this.fromDateAPI = '';
    this.toDateAPI = '';
    this.startDateAPI = '';
    this.endDateAPI = '';
    this.selectedRedeemedMethod = '';
    this.redeemedMethod = [];
    this.selectedRedeemedMethod = '';

    // Close dialog immediately when clear is clicked
    this.closeFilterDialog();

    // Tab-wise API call like other methods in the component
    if (this.isActiveFinanceReview) {
      this.getAllFinanceReviewData();
    } else if (this.isFinanceReviewProgress) {
      this.getAllFinanceReviewProgressData();
    }
  }

  filterApply() {
    if (this.startDate && !this.endDate) {
      this.toastr.warning('Please select end date');
      return; // Don't close dialog if validation fails
    } else {
      // Close dialog immediately when apply is clicked (for valid cases)
      this.closeFilterDialog();

      // Tab-wise API call like other methods in the component
      if (this.isActiveFinanceReview) {
        this.getAllFinanceReviewData();
      } else if (this.isFinanceReviewProgress) {
        this.getAllFinanceReviewProgressData();
      }
    }
  }

  onRedeemedHistorySeasonSelected(event: any) {
    this.selectedSeasonDropdownData = event.id;
    // Tab-wise API call like other methods in the component
    if (this.isActiveFinanceReview) {
      this.getAllFinanceReviewData();
    } else if (this.isFinanceReviewProgress) {
      this.getAllFinanceReviewProgressData();
    }
  }

  onRedeemedHistorySeasonDeSelected(event: any) {
    this.selectedSeasonDropdownData = 0;
    // Tab-wise API call like other methods in the component
    if (this.isActiveFinanceReview) {
      this.getAllFinanceReviewData();
    } else if (this.isFinanceReviewProgress) {
      this.getAllFinanceReviewProgressData();
    }
  }

  onRedeemedHistorySeasonDeSelectedAll(event: any) {
    this.selectedSeasonDropdownData = 0;
    // Tab-wise API call like other methods in the component
    if (this.isActiveFinanceReview) {
      this.getAllFinanceReviewData();
    } else if (this.isFinanceReviewProgress) {
      this.getAllFinanceReviewProgressData();
    }
  }

  onRedeemedMethodHistorySelected(event: any) {
    this.selectedRedeemedMethod = event.method;
  }

  onRedeemedMethodHistoryDeSelected(event: any) {
    this.selectedRedeemedMethod = '';
    // Tab-wise API call like other methods in the component
    if (this.isActiveFinanceReview) {
      this.getAllFinanceReviewData();
    } else if (this.isFinanceReviewProgress) {
      this.getAllFinanceReviewProgressData();
    }
  }

  onRedeemedMethodHistoryDeSelectedAll(event: any) {
    this.selectedRedeemedMethod = '';
    // Tab-wise API call like other methods in the component
    if (this.isActiveFinanceReview) {
      this.getAllFinanceReviewData();
    } else if (this.isFinanceReviewProgress) {
      this.getAllFinanceReviewProgressData();
    }
  }

  getAllFinanceReviewData(page?: any) {

    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      unPaged: false,
      regionId: this.selectedRegion ? this.selectedRegion : '',
      zoneId: this.selectedZone ? this.selectedZone : '',
    };
    this._spinner.show();
    const rewards = this.financeExceptionService.getAllFinanceReviewStatusMethodData(data);
    rewards.subscribe({
      next: (scheme: any) => {
        try {
          let parsedResponse = scheme;
          if (typeof scheme === 'string') {
            try {
              parsedResponse = JSON.parse(scheme);
            } catch (e) {
              const decrypted = this.utility.decrypt(scheme);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          let schemeData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            schemeData = JSON.parse(decrypted);
          } else {
            schemeData = parsedResponse;
          }
          

          schemeData?.content?.forEach((schemeInfo: any) => {

          const schemeInfoObj = {
            financeReviewId: schemeInfo.achievementId || null,
            leaderId: schemeInfo.leaderId || schemeInfo.achievementId || null,
            region: schemeInfo.region || 'NA',
            zone: schemeInfo.zone || 'NA',
            leaderName: schemeInfo.name || 'NA',
            cropProtectionAmount: '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalCropProtectionAmount || 0),
            achievedCP: '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedCropProtectionAmount || 0),
            nppAmount: '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalNppAmount || 0),
            achievedNPPAmount: '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedNppAmount || 0),
            achivedNPPPercentage: this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalNppPercentage || 0) + '%',
            total: '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalTargetAmount || 0),
            achievedTotal: '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalAmount || 0),
            achievedTotalPercentage: this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalPercentage || 0) + '%',
            bonificationAmount: '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalBonus || 0),
            bonification: schemeInfo.bonification ? 'Yes' : 'No',
            is_active: schemeInfo.approvalStatus === 'Approved', // Field name expected by dynamic table
            status: schemeInfo.approvalStatus === 'Approved' ? 'Active' : 'Inactive',
            showStatusToggle: true,
            showApprove: schemeInfo.approvalStatus === 'Pending',
            showReject: schemeInfo.approvalStatus === 'Pending',
            approvalStatus: schemeInfo.approvalStatus || 'NA',
            currentApprovalLevel: schemeInfo.currentApprovalLevel || 'NA'
          };
            this.financeReviewHistoryData.push(schemeInfoObj);
          });

          this.configurationSettings.totalRecordCount = schemeData.numberOfElements;
          this.totalCount = schemeData.numberOfElements;
          this.events.setChangedContentTopText('Finance Review Management');
          this._spinner.hide();
        } catch (error) {
          const errorMessage = this.handleDecryptedError(error, "Failed to load finance review data");
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error(errorMessage);

        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error)
        try {
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            // this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message);
          }
        } catch (e) {
          this.toastr.error("Failed to load finance review data");
        }
      },
    });
  }

  getFinanceConfigData(page?: any) {
    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      unPaged: false,
    };
    this._spinner.show();

    // Use different APIs based on active tab
    const rewards = this.isActiveFinanceReview
      ? this.financeExceptionService.getFinanceConfigData(data)
      : this.financeExceptionService.getExceptionConfigData();

    rewards.subscribe({
      next: (scheme: any) => {
        try {
          let parsedResponse = scheme;
          if (typeof scheme === 'string') {
            try {
              parsedResponse = JSON.parse(scheme);
            } catch (e) {
              const decrypted = this.utility.decrypt(scheme);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          let schemeData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            schemeData = JSON.parse(decrypted);
          } else {
            schemeData = parsedResponse;
          }
          
        // Ensure we're working with an array
        const dataArray = Array.isArray(schemeData) 
            ? schemeData 
            : (schemeData || {}); // Fallback to content if exists, or empty array

        // Safely process the data
          this.configData = dataArray || {};
          this._spinner.hide();
        } catch (error) {
          this._spinner.hide();
          this.toastr.error("Failed to load finance review progress data");
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error)
        try {
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            // this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message);
          }
        } catch (e) {
          this.toastr.error("Failed to load finance review data");
        }
      },
    });
  }
  getHistoryData(page?: any) {
    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      unPaged: false,
    };
    this._spinner.show();

    // Use different APIs based on active tab
    const rewards = this.isActiveFinanceReview
      ? this.financeExceptionService.getHistory(data)
      : this.financeExceptionService.getExceptionHistory();

    rewards.subscribe({
      next: (scheme: any) => {
        try {
          let parsedResponse = scheme;
          if (typeof scheme === 'string') {
            try {
              parsedResponse = JSON.parse(scheme);
            } catch (e) {
              const decrypted = this.utility.decrypt(scheme);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          let schemeData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            schemeData = JSON.parse(decrypted);
          } else {
            schemeData = parsedResponse;
          }
          
        // Ensure we're working with an array
        const dataArray = Array.isArray(schemeData) 
            ? schemeData 
            : (schemeData?.content || []); // Fallback to content if exists, or empty array

        // Safely process the data
        dataArray.forEach((item: any) => {  // Using forEach instead of map since we're not transforming
            try {
                const historyItem = {
                    type: (item.nppPercentage || item.cpPercentage) ? 'Leader' : 'Grower',
                    npp: (item.nppPercentage ?? 0) + '%',
                    cp: (item.cpPercentage ?? 0) + '%',
                    total: (item.totalPercentage ?? 0) + '%',
                    updatedBy: item.changedBy || 'Unknown',
                    updatedOn: item.changedAt ? moment(item.changedAt).format('DD-MM-YYYY') : 'NA',
                    rawData: item
                };
                this.historyData.push(historyItem);
            } catch (itemError) {
                console.error('Error processing item:', item, itemError);
            }
        });
          this._spinner.hide();
        } catch (error) {
          this._spinner.hide();
          this.toastr.error("Failed to load finance review progress data");
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error)
        try {
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            // this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message);
          }
        } catch (e) {
          this.toastr.error("Failed to load finance review data");
        }
      },
    });
  }

  getAllFinanceReviewProgressData(page?: any) {
    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      unPaged: false,
      regionId: this.selectedRegion ? this.selectedRegion : '',
      zoneId: this.selectedZone ? this.selectedZone : '',
    };
    this._spinner.show();
    const rewards = this.financeExceptionService.getAllFinanceReviewProgress(data);

    rewards.subscribe({
      next: (scheme: any) => {
        try {
          let parsedResponse = scheme;
          if (typeof scheme === 'string') {
            try {
              parsedResponse = JSON.parse(scheme);
            } catch (e) {
              const decrypted = this.utility.decrypt(scheme);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          let schemeData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            schemeData = JSON.parse(decrypted);
          } else {
            schemeData = parsedResponse;
          }
          
          this.financeReviewHistoryData = [];
          // Add demo data for Exception tab
          schemeData?.content?.forEach((schemeInfo: any) => {
            console.log(schemeInfo,"pppppppppppppppppppp");

            // Check if buttons should be disabled based on isAssigned
            const isButtonsDisabled = schemeInfo.isAssigned === false || schemeInfo.isAssigned === "false" || schemeInfo.isAssigned === 0 || schemeInfo.isAssigned === "0";

            // Check if toggle should show tooltip based on approval status (any status)
            const hasApprovalStatus = schemeInfo.approvalStatus && schemeInfo.approvalStatus !== '' && schemeInfo.approvalStatus !== null && schemeInfo.approvalStatus !== undefined;
            console.log('Approval Status Debug:', {
              approvalStatus: schemeInfo.approvalStatus,
              hasApprovalStatus: hasApprovalStatus,
              leaderName: schemeInfo.name
            });

            const schemeInfoObj = {
                region: schemeInfo.region,
                zone: schemeInfo.zone,
                leaderName: schemeInfo.name,  // Changed from leaderName to name
                cropProtectionAmount: '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalCropProtectionAmount),
                achievedCP: '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedCropProtectionAmount),
                nppAmount: '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalNppAmount),
                achievedNPPAmount: '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedNppAmount),
                achivedNPPPercentage: this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalNppPercentage) + '%',
                total: '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalTargetAmount),
                achievedTotal: '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalAmount),
                achievedTotalPercentage: this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalPercentage) + '%',
                bonificationAmount: '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalBonus),
                bonification: schemeInfo.bonification,
                exceptionStatus: schemeInfo.approvalStatus,  // Assuming this maps to approvalStatus
                status: schemeInfo.isActive ? 'Active' : 'Inactive',  // Note: isActive not in response
                showStatusToggle: true,
                showApprove: !isButtonsDisabled, // Disable approve button when isAssigned is false
                showReject: !isButtonsDisabled, // Disable reject button when isAssigned is false
                isDisabled: isButtonsDisabled, // Add isDisabled property for button styling
                showTooltip: hasApprovalStatus, // Show tooltip when any approval status exists
                tooltipText: hasApprovalStatus ? 'Already activated' : '', // Tooltip text
                id: schemeInfo.achievementId,  // Changed from id to achievementId
                leaderId: schemeInfo.achievementId,  // Changed from id to achievementId
                isActive: schemeInfo.isActive,  // Note: isActive not in response
                isAssigned: schemeInfo.isAssigned, // Store original isAssigned value
                approvalStatus: schemeInfo.approvalStatus // Store original approval status
            };

            console.log('Final schemeInfoObj:', {
              leaderName: schemeInfoObj.leaderName,
              showTooltip: schemeInfoObj.showTooltip,
              tooltipText: schemeInfoObj.tooltipText,
              approvalStatus: schemeInfoObj.approvalStatus,
              isActive: schemeInfoObj.isActive
            });

            this.financeReviewHistoryData.push(schemeInfoObj);
          });

          this.configurationSettings.totalRecordCount = schemeData?.numberOfElements;
          this.totalCount = schemeData?.numberOfElements;
          this.events.setChangedContentTopText('Finance Review Management');
          this._spinner.hide();
        } catch (error) {
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error("Failed to load finance review progress data");
        }
      },
          error: (errorResponse: any) => {
            this._spinner.hide();
            debugger
            errorResponse = this.utility.decryptString(errorResponse.error);
            const decryptedError = this.utility.decryptString(errorResponse);
            errorResponse = JSON.parse(decryptedError);
            this.toastr.error(errorResponse.message);
          },
    });
  }

  endDateChanged(event: any) {
    if (!this.endDate) {
      this.toastr.warning('Please select end date');
    } else {
      this.fromDateAPI = moment(this.startDate, 'DD-MM-YYYY').format(
        'DD-MM-YYYY'
      );
      this.toDateAPI = moment(this.endDate, 'DD-MM-YYYY').format('DD-MM-YYYY');
    }
  }

  getAllRedeemedMethodData() {
    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      unPaged: false,
      regionId: this.selectedRegion ? this.selectedRegion : '',
      zoneId: this.selectedZone ? this.selectedZone : '',
    };
    this.financeExceptionService.getAllFinanceReviewStatusMethodData(data).subscribe({
      next: (redeemedMethodDetails: any) => {
        try {
          let parsedResponse = redeemedMethodDetails;
          if (typeof redeemedMethodDetails === 'string') {
            try {
              parsedResponse = JSON.parse(redeemedMethodDetails);
            } catch (e) {
              const decrypted = this.utility.decrypt(redeemedMethodDetails);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          let methodData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            methodData = JSON.parse(decrypted);
          } else {
            methodData = parsedResponse;
          }
          
          this.redeemedMethodDetails = [];
          methodData.forEach(
            (redeemedMethodDetails: any, index: number) => {
              let redeemedDataObj;
              if (index < 2) {
                redeemedDataObj = {
                  name: redeemedMethodDetails,
                  id: index + 1,
                  method: redeemedMethodDetails,
                };
              } else {
                redeemedDataObj = {
                  name: redeemedMethodDetails,
                  id: index + 1,
                  method: redeemedMethodDetails,
                };
              }

              this.redeemedMethodDetails.push(redeemedDataObj);
            }
          );
        } catch (error) {
          console.error('Error processing response:', error);
        }
      },
      error: (errorResponse: any) => {
        let error: any = (errorResponse.error)
        error = JSON.parse(error);
        if ('Full authentication is required to access this resource' == error.message) {
          localStorage.clear();
          // this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  exportFinanceReviewData(page?: any) {
    if (this.isActiveFinanceReview === true) {
      this.exportActiveFinanceReviewData(page);
    } else if (this.isActiveFinanceReview === false) {
      this.exportFinanceReviewProgressData(page);
    }
  }
  
  exportFinanceReviewProgressData(page?: any) {
    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      unPaged: true, // Export all data, not paginated
      regionId: this.selectedRegion ? this.selectedRegion : '',
      zoneId: this.selectedZone ? this.selectedZone : '',
    };

    this._spinner.show();
    const rewards = this.financeExceptionService.getAllFinanceReviewProgress(data);

    rewards.subscribe({
      next: (exportsDataRes: any) => {
        try {
          let parsedResponse = exportsDataRes;
          if (typeof exportsDataRes === 'string') {
            try {
              parsedResponse = JSON.parse(exportsDataRes);
            } catch (e) {
              const decrypted = this.utility.decrypt(exportsDataRes);
              parsedResponse = JSON.parse(decrypted);
            }
          }

          let exportData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            exportData = JSON.parse(decrypted);
          } else {
            exportData = parsedResponse;
          }

          this.exportData = [];
          if (
            exportData &&
            exportData.content &&
            exportData.content.length
          ) {
            exportData.content.forEach((schemeInfo: any) => {
              const schemeInfoObj = {
                Region: schemeInfo.region || 'NA',
                Zone: schemeInfo.zone || 'NA',
                'Leader Name': schemeInfo.name || 'NA',
                'CP (Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalCropProtectionAmount || 0),
                'Achieved CP(Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedCropProtectionAmount || 0),
                'NPP (Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalNppAmount || 0),
                'Achieved NPP(Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedNppAmount || 0),
                'Achieved NPP(%)': this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalNppPercentage || 0) + '%',
                'Total (Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalTargetAmount || 0),
                'Achieved Total(Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalAmount || 0),
                'Achieved Total(%)': this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalPercentage || 0) + '%',
                'Bonification Amount': '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalBonus || 0),
                'Bonification': schemeInfo.bonification ? 'Yes' : 'No',
                'Exception Status': schemeInfo.approvalStatus || 'NA',
              };

              this.exportData.push(schemeInfoObj);
            });

            let options = {
              fieldSeparator: ',',
              quoteStrings: '"',
              decimalseparator: '.',
              showLabels: true,
              headers: this.tableHead, // Use exact table headers
            };

            new ngxCsv(
              this.exportData,
              'Exception Management Details',
              options
            );
          } else {
            this.toastr.warning('No data available');
          }
          this._spinner.hide();
        } catch (error) {
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error('Failed to export data');
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error)
        try {
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message || 'Failed to export data');
          }
        } catch (e) {
          this.toastr.error('Failed to export data');
        }
      },
    });
  }
  
  exportActiveFinanceReviewData(page?: any) {
    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      unPaged: true, // Export all data, not paginated
      regionId: this.selectedRegion ? this.selectedRegion : '',
      zoneId: this.selectedZone ? this.selectedZone : '',
    };

    this._spinner.show();
    const rewards = this.financeExceptionService.getAllFinanceReviewStatusMethodData(data);

    rewards.subscribe({
      next: (exportsDataRes: any) => {
        try {
          let parsedResponse = exportsDataRes;
          if (typeof exportsDataRes === 'string') {
            try {
              parsedResponse = JSON.parse(exportsDataRes);
            } catch (e) {
              const decrypted = this.utility.decrypt(exportsDataRes);
              parsedResponse = JSON.parse(decrypted);
            }
          }

          let exportData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            exportData = JSON.parse(decrypted);
          } else {
            exportData = parsedResponse;
          }

          this.exportData = [];
          if (
            exportData &&
            exportData.content &&
            exportData.content.length
          ) {
            exportData.content.forEach((schemeInfo: any) => {
              const schemeInfoObj = {
                Region: schemeInfo.region || 'NA',
                Zone: schemeInfo.zone || 'NA',
                'Leader Name': schemeInfo.name || 'NA',
                'CP (Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalCropProtectionAmount || 0),
                'Achieved CP(Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedCropProtectionAmount || 0),
                'NPP (Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalNppAmount || 0),
                'Achieved NPP(Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedNppAmount || 0),
                'Achieved NPP(%)': this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalNppPercentage || 0) + '%',
                'Total (Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalTargetAmount || 0),
                'Achieved Total(Mex$)': '$' + this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalAmount || 0),
                'Achieved Total(%)': this.utility.formatNumberTwoDigits(schemeInfo.achievedTotalPercentage || 0) + '%',
                'Bonification Amount': '$' + this.utility.formatNumberTwoDigits(schemeInfo.totalBonus || 0),
                'Bonification': schemeInfo.bonification ? 'Yes' : 'No',
              };

              this.exportData.push(schemeInfoObj);
            });

            let options = {
              fieldSeparator: ',',
              quoteStrings: '"',
              decimalseparator: '.',
              showLabels: true,
              headers: this.tableHead, // Use exact table headers
            };

            new ngxCsv(
              this.exportData,
              'Finance Review Management Details',
              options
            );
          } else {
            this.toastr.warning('No data available');
          }
          this._spinner.hide();
        } catch (error) {
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error('Failed to export data');
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error)
        try {
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message || 'Failed to export data');
          }
        } catch (e) {
          this.toastr.error('Failed to export data');
        }
      },
    });
  }

  getPageData(page: any) {
    this.configurationSettings.currentPage = page;
    this.currentPage = page;

    // Tab-wise API call like other methods in the component
    if (this.isActiveFinanceReview) {
      this.getAllFinanceReviewData(this.currentPage);
    } else if (this.isFinanceReviewProgress) {
      this.getAllFinanceReviewProgressData(this.currentPage);
    }
  }

  onStartDateChanged(event: any) {
    if (event && event.formatted) {
      this.startDate = '';
      this.endDate = '';
      this.endDateModel = '';
      this.startDate = moment(event.formatted, 'DD-MM-YYYY').format(
        'DD-MM-YYYY'
      );
      const momentDate = moment(this.startDate, 'DD-MM-YYYY').subtract(
        1,
        'day'
      );
    } else {
      this.startDate = '';
    }
  }
  startdate2: any;

  onEndDateChanged(event: any) {
    if (event && event.formatted) {
      this.endDate = '';
      this.endDate = moment(event.formatted, 'DD-MM-YYYY').format('DD-MM-YYYY');
    } else {
      this.endDate = '';
    }
  }

  formatDate(date: Date): string {
    return date.toISOString();
  }

  filterSchemeByDate(rangePicker: any) {
    this.fromDateAPI = moment(this.startDate, 'DD-MM-YYYY').format(
      'DD-MM-YYYY'
    );
    this.toDateAPI = moment(this.endDate, 'DD-MM-YYYY').format('DD-MM-YYYY');
  }

  clearSchemeFilter() {
    this.startDate = '';
    this.endDate = '';
    this.startDateModel = '';
    this.endDateModel = '';
    this.fromDateAPI = '';
    this.toDateAPI = '';
    this.startDateAPI = '';
    this.endDateAPI = '';
    this.dateForm.controls['startDatePicker'].setValue('');
    this.dateForm.controls['endDatePicker'].setValue('');

    if (this.isDateFilter) {
      this.isDateFilter = false;
      this.financeReviewHistoryData = [];
      this.currentPage = 0;
      this.getAllFinanceReviewData();
    }
  }

  changeFilter(event: any) {
    this.startDate = '';
    this.endDate = '';
    this.startDateModel = '';
    this.endDateModel = '';
    this.fromDateAPI = '';
    this.toDateAPI = '';
    this.startDateAPI = '';
    this.endDateAPI = '';
    this.dateForm.controls['startDatePicker'].setValue('');
    this.dateForm.controls['endDatePicker'].setValue('');
    if (event.value) {
      if (this.isActiveFinanceReview) {
        event.value === 'redeemed'
          ? this.financeReviewHistoryTab('isRedeemedDistributor')
          : this.financeReviewHistoryTab('isFcDistributor');
      } else if (this.isFinanceReviewProgress) {
        event.value === 'redeemed'
          ? this.financeReviewHistoryTab('isForagesRetailer')
          : this.financeReviewHistoryTab('isFcRetailer');
      }
    }
  }

  viewChange(data: any) {
    switch (data) {
      case 'isTable':
        this.isTable = true;
        this.isMap = false;
        break;
      case 'isMap':
        this.isTable = false;
        this.isMap = true;
    }
  }
  cleanCustomAndCallOnDate(val: any, event: any) {
    let month = event.getMonth() + 1;
    let day = event.getDate();
    const year = event.getFullYear();
    if (day < 10) {
      day = '0' + day;
    }
    if (month < 10) {
      month = '0' + month;
    }
    const date = day + '-' + month + '-' + year;
    switch (val) {
      case 'from-date':
        this.fromDateAPI = date;
        break;
      case 'to-date':
        this.toDateAPI = date;
        break;
    }

    this.startDateAPI = this.fromDateAPI;
    this.endDateAPI = this.toDateAPI;
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
  }

  dateValidation(fromDateVal: any, toDateVal: any) {
    const fromDate = fromDateVal === undefined ? '' : fromDateVal.split('-');
    let toDate = toDateVal === undefined ? '' : toDateVal.split('-');
    const fromDateValue = fromDate[0] + fromDate[1] + fromDate[2];
    const toDateValue = toDate[0] + toDate[1] + toDate[2];
    if (fromDateValue > toDateValue) {
      this.toastr.error(
        'Invalid date. Start Date should be less than End Date'
      );
      return;
    } else {
      this.startDate = this.fromDateAPI;
      this.endDate = this.toDateAPI;
    }
  }
  funRestEmail(event: any) {
    var k;
    k = event.charCode;
    if (
      (k > 64 && k < 91) ||
      (k > 96 && k < 123) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57) ||
      event.key == '.' ||
      event.key == '-' ||
      event.key == '_' ||
      event.key == '+' ||
      event.key == '@' ||
      event.key == '' ||
      event.keyCode == 13
    ) {
    } else {
      this.toastr.error(event.key + ' ' + 'not allowed');
    }

    return (
      (k > 64 && k < 91) ||
      (k > 96 && k < 123) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57) ||
      event.key == '.' ||
      event.key == '-' ||
      event.key == '_' ||
      event.key == '+' ||
      event.key == '@' ||
      (event.key == '' && event.key !== 'Enter')
    );
  }

  funRestSearchPrevent(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
    } else {
      event.preventDefault();
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }

  @ViewChild('approveConfirmDialog') approveConfirmDialog!: TemplateRef<any>;
  selectedData: any;

  approveFinanceReview(data: any) {
    this.selectedData = data;
    this.dialog.open(this.approveConfirmDialog, {
      width: '400px',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      hasBackdrop: true,
      autoFocus: false,
      restoreFocus: true,
    });
  }

  confirmApproval() {
    this._spinner.show();
    this.dialog.closeAll();

    if (!this.selectedData) {
      this._spinner.hide();
      this.toastr.error("Missing required data for approval");
      return;
    }

    // Use different APIs based on active tab
    if (this.isFinanceReviewProgress) {
      // Exception tab - use new approve API
      const achievementId = this.selectedData.financeReviewId || this.selectedData.leaderId || this.selectedData.id;

      if (!achievementId) {
        this._spinner.hide();
        this.toastr.error('Achievement ID not found');
        return;
      }

      this.financeExceptionService.approveFinancialReviewException(achievementId).pipe(
        catchError((error) => {
          this._spinner.hide();
          let errorMessage = 'Failed to approve exception';

          try {
            if (error.error) {
              let errorData = error.error;

              // Check if error has encryptedBody
              if (typeof errorData === 'string') {
                errorData = JSON.parse(errorData);
              }

              if (errorData.encryptedBody) {
                const decryptedError = this.utility.decrypt(errorData.encryptedBody);
                const errorObj = JSON.parse(decryptedError);
                errorMessage = errorObj.message || errorMessage;
              } else if (errorData.message) {
                errorMessage = errorData.message;
              }
            }
          } catch (decryptError) {
            console.error('Failed to decrypt error response:', decryptError);
            // Keep the default error message if decryption fails
          }

          this.toastr.error(errorMessage);
          return of(null); // Return empty observable to prevent further error propagation
        })
      ).subscribe({
        next: (response) => {
          if (response !== null) {
            this._spinner.hide();
            this.toastr.success('Exception approved successfully');
            this.getAllFinanceReviewProgressData();
          }
        }
      });
      return;
    }

    // Finance Review tab - use existing logic
    if (!this.selectedData.financeReviewId && !this.selectedData.leaderId) {
      this._spinner.hide();
      this.toastr.error("Missing required data for approval");
      return;
    }
    const approvalPayload = {
      leaderId: this.selectedData.financeReviewId || this.selectedData.leaderId,
      amount: Number(this.selectedData.amount || this.selectedData.totalAmount),
      taxId: this.selectedData.taxId || this.selectedData.rfc,
      startDate: this.selectedData.startDate,
      endDate: this.selectedData.endDate,
      isActive: true,
      grower: this.selectedData.grower || false,
      cropProtectionAmount: Number(this.selectedData.cropProtectionAmount) || 0,
      nppAmount: Number(this.selectedData.nppAmount) || 0
    };

    this.financeExceptionService.approveFinanceReview(approvalPayload).subscribe({
      next: (response: any) => {
        try {
          if (typeof response === 'string' && response) {
            try {
              const decrypted = this.utility.decrypt(response);
              response = JSON.parse(decrypted);
            } catch (e) {
            }
          }
          
          this._spinner.hide();
          this.toastr.success("Finance review approved successfully");
          this.fixToastVisibility();
          this.getAllFinanceReviewData();
        } catch (error) {
          error = this.utility.decryptString(error);
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.fixToastVisibility();
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error);
        try {
          error = JSON.parse(error);
          this.toastr.error(error.message || "Failed to approve finance review");
          this.fixToastVisibility();
        } catch (e) {
          this.toastr.error("Failed to approve finance review");
          this.fixToastVisibility();
        }
      }
    });
  }

  rejectFinanceReviewId: number | null = null;
  rejectRemark: string = '';
  rejectDialogRef!: MatDialogRef<any>;
  @ViewChild('rejectDialog') rejectDialog!: TemplateRef<any>;

  openRejectDialog(data: any) {
    this.rejectFinanceReviewId = data.id || data.leaderId;
    this.rejectRemark = '';

    this.rejectDialogRef = this.dialog.open(this.rejectDialog, {
      width: '400px',
      disableClose: false,
      panelClass: 'reject-dialog-container',
      hasBackdrop: true,
    });
  }

  submitReject() {
    if (!this.rejectFinanceReviewId || !this.rejectRemark.trim()) {
      this.toastr.warning('Please enter a comment');
      return;
    }

    this._spinner.show();

    // Use different APIs based on active tab
    if (this.isFinanceReviewProgress) {
      // Exception tab - use new reject API
      this.financeExceptionService.rejectFinancialReviewException(this.rejectFinanceReviewId, this.rejectRemark).pipe(
        catchError((_error: any) => {
          this._spinner.hide();
          this.toastr.error('Failed to reject exception');
          return of(null); // Return empty observable to prevent further error propagation
        })
      ).subscribe({
        next: (response) => {
          if (response !== null) {
            this._spinner.hide();
            this.toastr.success('Exception rejected successfully');
            this.rejectDialogRef.close();
            this.getAllFinanceReviewProgressData();
          }
        }
      });
      return;
    }

    // Finance Review tab - use existing logic
    this.financeExceptionService.rejectFinanceReview(this.rejectFinanceReviewId, this.rejectRemark).subscribe({
      next: (response: any) => {
        try {
          if (typeof response === 'string' && response) {
            try {
              const decrypted = this.utility.decrypt(response);
              response = JSON.parse(decrypted);
            } catch (e) {
            }
          }
          
          this._spinner.hide();
          this.toastr.success("Finance review rejected successfully");
          this.fixToastVisibility();
          this.rejectDialogRef.close();
          this.getAllFinanceReviewData();
        } catch (error) {
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error("Failed to reject finance review");
          this.fixToastVisibility();
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error);
        try {
          error = JSON.parse(error);
          this.toastr.error(error.message || "Failed to reject finance review");
          this.fixToastVisibility();
        } catch (e) {
          this.toastr.error("Failed to reject finance review");
          this.fixToastVisibility();
        }
      }
    });
  }

  cancelReject() {
    this.rejectDialogRef.close();
  }
  private fixToastVisibility(): void {
    this.toastFixService.fixToastVisibility();
  }

  selectRegion(event: any) {
    this.selectedRegion = event.id;

    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';

    this.getZoneByID(this.selectedRegion);
  }

  deselectionRegion(event: any) {
    this.selectedRegion = [];

    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';
  }

  deselectionAllRegion(event: any) {
    this.deselectionRegion(event);

    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';
  }

  selectZone(event: any) {
    this.selectedZone = event.id;
  }

  deselectionZone(event: any) {
    this.selectedZone = [];
  }

  deselectionAllZone(event: any) {
    this.deselectionZone(event);
  }

  clearFilterProgress() {
    this._spinner.show();

    // Close dialog immediately when clear is clicked
    this.closeFilterDialog();

    // Clear filter values
    this.selectedRegion = [];
    this.regionValue = '';
    this.selectedZone = [];
    this.zoneValue = '';

    // Tab-wise API call like other methods in the component
    if (this.isActiveFinanceReview) {
      this.getAllFinanceReviewData();
    } else if (this.isFinanceReviewProgress) {
      this.getAllFinanceReviewProgressData();
    }
  }
  
  filterApplyProgress() {
    this._spinner.show();

    // Close dialog immediately when apply is clicked
    this.closeFilterDialog();

    // Tab-wise API call like other methods in the component
    if (this.isActiveFinanceReview) {
      this.getAllFinanceReviewData();
    } else if (this.isFinanceReviewProgress) {
      this.getAllFinanceReviewProgressData();
    }
  }
  
  getZoneByID(id: any) {
    const data = {
      zoneId: id
    };
    this.zoneDataList = [];
    this.userService.getZoneById(data).subscribe(
      (response: any) => {
        try {
          let parsedResponse = response;
          if (typeof response === 'string') {
            try {
              parsedResponse = JSON.parse(response);
            } catch (e) {
              const decrypted = this.utility.decrypt(response);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          let zoneData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            zoneData = JSON.parse(decrypted);
          } else {
            zoneData = parsedResponse;
          }
          
          this.zoneDataList = zoneData.map((item: any) => ({
            id: item.id,
            name: item.name,
            code: item.code,
          }));
        } catch (error) {
          console.error('Error processing zone response:', error);
          this.toastr.error("Failed to load zone data");
        }
      },
      (error) => {
        console.error('Failed to fetch zone data', error);
        this.toastr.error("Failed to load zone data");
      }
    );
  }

  regionDropdownSettings = {
    text: 'Select Region',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  
  zoneDropdownSettings = {
    text: 'Select Zone',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  getRegion() {
    this.regionDataList = [];
    this.userService.getRegion().subscribe(
      (response: any) => {
        try {
          let parsedResponse = response;
          if (typeof response === 'string') {
            try {
              parsedResponse = JSON.parse(response);
            } catch (e) {
              const decrypted = this.utility.decrypt(response);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          let regionData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            regionData = JSON.parse(decrypted);
          } else {
            regionData = parsedResponse;
          }
          
          this.regionDataList = regionData.map((item: any) => ({
            id: item.id,
            name: item.name,
            code: item.code,
          }));
        } catch (error) {
          console.error('Error processing region response:', error);
          this.toastr.error("Failed to load region data");
        }
      },
      (error) => {
        console.error('Failed to fetch region data', error);
        this.toastr.error("Failed to load region data");
      }
    );
  }

  /**
   * Handle status change for finance review items (different APIs for different tabs)
   * @param event - The finance review item data
   */
  changeFinanceReviewStatus(event: any): void {
    if (!event) return;

    this._spinner.show();

    // Determine which API to call based on current tab
    if (this.isActiveFinanceReview) {
      // Finance Review tab - use notify API with leaderId
      const leaderId = event.leaderId || event.financeReviewId || event.id;

      if (!leaderId) {
        this._spinner.hide();
        this.toastr.error('Leader ID not found');
        return;
      }

      this.financeExceptionService.notifyFinancialReview(leaderId).pipe(
        catchError((errorResponse: any) => {
          this.handleStatusChangeError(errorResponse, event, leaderId, 'Finance Review');
          return of(null); // Return empty observable to prevent further error propagation
        })
      ).subscribe({
        next: (response) => {
          if (response !== null) {
            this.handleStatusChangeSuccess(event, leaderId, 'Finance Review');
          }
        }
      });

    } else if (this.isFinanceReviewProgress) {
      // Exception tab - use send for approval API with achievementId
      const achievementId = event.financeReviewId || event.leaderId || event.id;

      if (!achievementId) {
        this._spinner.hide();
        this.toastr.error('Achievement ID not found');
        return;
      }

      this.financeExceptionService.sendFinancialReviewForApproval(achievementId).pipe(
        catchError((errorResponse: any) => {
          this.handleStatusChangeError(errorResponse, event, achievementId, 'Exception');
          return of(null); // Return empty observable to prevent further error propagation
        })
      ).subscribe({
        next: (response) => {
          if (response !== null) {
            this.handleStatusChangeSuccess(event, achievementId, 'Exception');
          }
        }
      });
    }
  }

  /**
   * Handle successful status change
   */
  private handleStatusChangeSuccess(event: any, itemId: any, tabType: string): void {
    this._spinner.hide();

    // Update the toggle state immediately in the local data
    const dataArray = this.isActiveFinanceReview ? this.financeReviewHistoryData : this.financeReviewHistoryData;
    const itemIndex = dataArray.findIndex((item: any) =>
      (item.leaderId || item.financeReviewId || item.id) == itemId
    );

    if (itemIndex !== -1) {
      // Toggle the status immediately
      dataArray[itemIndex].is_active = !dataArray[itemIndex].is_active;
      dataArray[itemIndex].status = dataArray[itemIndex].is_active ? 'Active' : 'Inactive';
    }

    // Show success message
    const action = event.is_active ? 'deactivated' : 'activated';
    this.toastr.success(`${tabType} ${action} successfully`);

    // Refresh the data
    if (this.isActiveFinanceReview) {
      this.getAllFinanceReviewData();
    } else {
      this.getAllFinanceReviewProgressData();
    }
  }

  /**
   * Handle status change error
   */
  private handleStatusChangeError(errorResponse: any, event: any, itemId: any, tabType: string): void {
    this._spinner.hide();

    // Only show error if it's actually an HTTP error (4xx, 5xx)
    if (errorResponse.status >= 400) {
      try {
        let error: any = errorResponse.error;
        if (typeof error === 'string') {
          try {
            error = JSON.parse(error);
          } catch (e) {
            const decrypted = this.utility.decrypt(error);
            error = JSON.parse(decrypted);
          }
        }

        if (error && error.encryptedBody) {
          const decrypted = this.utility.decrypt(error.encryptedBody);
          error = JSON.parse(decrypted);
        }

        if (error && error.message) {
          this.toastr.error(error.message);
        } else {
          this.toastr.error(`Failed to update ${tabType.toLowerCase()} status`);
        }
      } catch (e) {
        this.toastr.error(`Failed to update ${tabType.toLowerCase()} status`);
      }
    } else {
      // If it's not a real error, treat as success
      this.handleStatusChangeSuccess(event, itemId, tabType);
    }
  }

  /**
   * Handle approve action for Exception tab (new API integration)
   */
  approveFinanceReviewException(event: any): void {
    if (!event) return;

    this._spinner.show();

    const achievementId = event.financeReviewId || event.leaderId || event.id;

    if (!achievementId) {
      this._spinner.hide();
      this.toastr.error('Achievement ID not found');
      return;
    }

    this.financeExceptionService.approveFinancialReviewException(achievementId).subscribe({
      next: () => {
        this._spinner.hide();
        this.toastr.success('Finance review approved successfully');
        this.getAllFinanceReviewProgressData(); // Refresh Exception tab data
      },
      error: () => {
        this._spinner.hide();
        this.toastr.error('Failed to approve finance review');
      }
    });
  }

  /**
   * Handle reject action for Exception tab (new API integration)
   */
  rejectFinanceReviewException(event: any, remark: string): void {
    if (!event || !remark) {
      this.toastr.error('Remark is required for rejection');
      return;
    }

    this._spinner.show();

    const achievementId = event.financeReviewId || event.leaderId || event.id;

    if (!achievementId) {
      this._spinner.hide();
      this.toastr.error('Achievement ID not found');
      return;
    }

    this.financeExceptionService.rejectFinancialReviewException(achievementId, remark).subscribe({
      next: () => {
        this._spinner.hide();
        this.toastr.success('Finance review rejected successfully');
        this.getAllFinanceReviewProgressData(); // Refresh Exception tab data
      },
      error: () => {
        this._spinner.hide();
        this.toastr.error('Failed to reject finance review');
      }
    });
  }

  /**
   * Private method to handle and decrypt error messages
   * @param error - The error object that may contain encrypted data
   * @param defaultMessage - Default message to show if decryption fails
   * @returns Decrypted error message or default message
   */
  private handleDecryptedError(error: any, defaultMessage: string): string {
    console.log('handleDecryptedError - Input error:', error);

    try {
      let errorData = error;

      // Handle different error structures
      if (error && error.error) {
        errorData = error.error;
        console.log('handleDecryptedError - Using error.error:', errorData);
      }

      // If errorData is a string, try to parse it as JSON
      if (typeof errorData === 'string') {
        console.log('handleDecryptedError - Parsing string error:', errorData);
        try {
          errorData = JSON.parse(errorData);
          console.log('handleDecryptedError - Parsed JSON:', errorData);
        } catch (parseError) {
          console.log('handleDecryptedError - JSON parse failed, trying direct decrypt');
          try {
            const decrypted = this.utility.decrypt(errorData);
            errorData = JSON.parse(decrypted);
            console.log('handleDecryptedError - Direct decrypt successful:', errorData);
          } catch (decryptError) {
            console.log('handleDecryptedError - Direct decrypt failed:', decryptError);
            return defaultMessage;
          }
        }
      }

      // Check for encryptedBody property
      if (errorData && errorData.encryptedBody) {
        console.log('handleDecryptedError - Found encryptedBody, decrypting...');
        try {
          const decryptedError = this.utility.decrypt(errorData.encryptedBody);
          console.log('handleDecryptedError - Decrypted content:', decryptedError);
          const errorObj = JSON.parse(decryptedError);
          console.log('handleDecryptedError - Parsed decrypted object:', errorObj);
          return errorObj.message || errorObj.error || defaultMessage;
        } catch (decryptError) {
          console.error('handleDecryptedError - Decryption failed:', decryptError);
          return defaultMessage;
        }
      }

      // Check for direct message property
      if (errorData && errorData.message) {
        console.log('handleDecryptedError - Found direct message:', errorData.message);
        return errorData.message;
      }

      console.log('handleDecryptedError - No encrypted body or message found, using default');
      return defaultMessage;

    } catch (error) {
      console.error('handleDecryptedError - Unexpected error:', error);
      return defaultMessage;
    }
  }
}