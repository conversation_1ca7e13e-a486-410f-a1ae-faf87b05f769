<div class="reward-points-container add-edit-scheme">
  <div class="reward-points-grid-container">
    <div class="reward-points-grid-data-parent-container">
      <div class="reward-points-grid-data-container">
        <div class="pc-container">
          <span *ngIf="!isCustomerCount && rewardId && rewardTitle"> </span>
          <div class="pc-title">PROFIT CENTER INFORMATION</div>
          <div class="dropdown-container">
            <label>Profit Center<i class="required">&nbsp;*</i></label>
            <select
              [(ngModel)]="profitCenter"
              (ngModelChange)="PCchangeDropDown($event)"
              [disabled]="rewardId"
              [ngClass]="{ 'disable-dropdown': rewardId }"
            >
              <option value="" selected disabled>Select Profit Center</option>
              <option value="swal">SWAL</option>
            </select>
          </div>
          <div class="dropdown-container">
            <label>Enter Scheme Title<i class="required">&nbsp;*</i></label>
            <input
              class="scheme-title"
              type="text"
              [(ngModel)]="rewardTitle"
              placeholder="Enter Scheme Title"
            />

            <div class="error-message">
              <span
                *ngIf="!rewardTitle && submitClicked"
                class="help-block sub-little-error confpass"
                >Scheme title is required</span
              >
            </div>
          </div>
        </div>

        <div class="brand-selection" *ngIf="isAF || isSWAL">
          <div class="brand-sku">
            <div class="dropdown-container brands-multi-select">
              <label>Select Brand<i class="required">&nbsp;*</i></label>

              <!-- <angular2-multiselect
                [data]="brandData"
                [(ngModel)]="selectedBrands"
                [settings]="brandDropdownSettings"
                (onSelect)="selectedBrand($event)"
                (onDeSelect)="deSelectedBrand($event)"
                (onSelectAll)="selectedAllBrands($event)"
                (onDeSelectAll)="deSelectedAllBrands($event)"
              >
              </angular2-multiselect> -->
              <div class="error-message">
                <span
                  *ngIf="!selectedBrands.length && submitClicked"
                  class="help-block sub-little-error confpass"
                  >Please select brand</span
                >
              </div>
            </div>

            <div class="dropdown-container brands-multi-select">
              <label>Select SKU</label>

              <!-- <angular2-multiselect
                [data]="skuData"
                [(ngModel)]="product"
                [settings]="skuDropdownSettings"
                (onSelect)="selectedSKU($event)"
                (onDeSelect)="deSelectedSKU($event)"
                (onSelectAll)="selectedAllSKUS($event)"
                (onDeSelectAll)="deSelectedAllSKUS($event)"
              >
              </angular2-multiselect> -->
            </div>
          </div>

          <div class="dropdown-container">
            <label>Enter Reward Points<i class="required">&nbsp;*</i></label>

            <div class="error-message">
              <span
                *ngIf="!rewardPoints && submitClicked"
                class="help-block sub-little-error confpass"
                >Reward point is required</span
              >
            </div>
          </div>

          <div class="dropdown-container">
            <div class="start-date-container">
              <label>Select Start Date<i class="required">&nbsp;*</i></label>

              <div class="error-message">
                <span
                  *ngIf="!startDateModel && !startDate && submitClicked"
                  class="help-block sub-little-error confpass"
                  >Start date is required</span
                >
              </div>
            </div>

            <div class="end-date-container">
              <label>Select End Date<i class="required">&nbsp;*</i></label>

              <div class="error-message">
                <span
                  *ngIf="!endDateModel && !endDate && submitClicked"
                  class="help-block sub-little-error confpass"
                  >End date is required</span
                >
              </div>
            </div>
          </div>

          <div class="customer-section">
            <div class="zone-region">
              <div class="dropdown-container" *ngIf="isAF">
                <label>Select SBU</label>
                <!-- <angular2-multiselect
                  [data]="sbuDetails"
                  [(ngModel)]="sbu"
                  [settings]="sbuDropdownSettings"
                  (onSelect)="selectedSBU($event)"
                  (onDeSelect)="deSelectedSBU($event)"
                  (onSelectAll)="selectedAllSBUS($event)"
                  (onDeSelectAll)="deSelectedAllSBUS($event)"
                >
                </angular2-multiselect> -->
              </div>

              <div class="dropdown-container">
                <!-- <label>Select Zone</label>
                <angular2-multiselect
                  [data]="zoneDetails"
                  [(ngModel)]="zone"
                  [settings]="zoneDropdownSettings"
                  (onSelect)="selectedZone($event)"
                  (onDeSelect)="deSelectedZone($event)"
                  (onSelectAll)="selectedAllZones($event)"
                  (onDeSelectAll)="deSelectedAllZones($event)"
                >
                </angular2-multiselect> -->
              </div>

              <div class="dropdown-container" *ngIf="isSWAL">
                <label>Select Region</label>
                <!-- <angular2-multiselect
                  [data]="regionsDetails"
                  [(ngModel)]="region"
                  [settings]="regionDropdownSettings"
                  (onSelect)="selectedRegion($event)"
                  (onDeSelect)="deSelectedRegion($event)"
                  (onSelectAll)="selectedAllRegions($event)"
                  (onDeSelectAll)="deSelectedAllRegions($event)"
                >
                </angular2-multiselect> -->
              </div>
            </div>
            <div class="territory-customer">
              <div class="dropdown-container">
                <label>Select Territory</label>
                <!-- <angular2-multiselect
                  [data]="territoriesDetails"
                  [(ngModel)]="territory"
                  [settings]="territoryDropdownSettings"
                  (onSelect)="selectedTerritory($event)"
                  (onDeSelect)="deSelectedTerritory($event)"
                  (onSelectAll)="selectedAllTerritories($event)"
                  (onDeSelectAll)="deSelectedAllTerritories($event)"
                >
                </angular2-multiselect> -->
              </div>

              <div class="dropdown-container">
                <label>Select Customer Type</label>
                <!-- <angular2-multiselect
                  [data]="customersDetails"
                  [(ngModel)]="customer"
                  [settings]="customerDropdownSettings"
                  (onSelect)="selectedCustomer($event)"
                  (onDeSelect)="deSelectedCustomer($event)"
                  (onSelectAll)="selectedAllCustomers($event)"
                  (onDeSelectAll)="deSelectedAllCustomers($event)"
                >
                </angular2-multiselect> -->
              </div>
            </div>
            <div class="rewards-action">
              <div class="rewards-action-container">
                <input
                  class="action-btn"
                  type="button"
                  value="Cancel"
                  (click)="cancel()"
                />
                <input
                  class="action-btn submit-rewards"
                  type="button"
                  (click)="addScheme()"
                  [ngClass]="{
                    disableSubmit:
                      !selectedBrands.length ||
                      !startDate ||
                      !endDate ||
                      !rewardPoints
                  }"
                  value="Submit"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="reward-delete-container"></div>
</div>
