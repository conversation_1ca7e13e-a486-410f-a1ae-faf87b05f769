import {  waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';
import { RewardsPointsFormComponent } from './rewards-points-form.component';

describe('RewardsPointsFormComponent', () => {
    let component: RewardsPointsFormComponent;
    let fixture: ComponentFixture<RewardsPointsFormComponent>;

    beforeEach( waitForAsync(() => {
        TestBed.configureTestingModule({
    imports: [RewardsPointsFormComponent]
}).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(RewardsPointsFormComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
