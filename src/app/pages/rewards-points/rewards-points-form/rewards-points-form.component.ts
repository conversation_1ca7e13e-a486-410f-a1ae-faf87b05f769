import { Component, ElementRef } from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { GlobalEvents } from "../../../helpers/global.events";
import { UserService } from "../../../app-services/user-service";
import { ZonesService } from "../../../app-services/zones-service";
import { RegionsService } from "../../../app-services/regions-service";
import { TerritoriesService } from "../../../app-services/territories-service";
import { AppConstant } from "../../../constants/app.constant";
import { IMyOptions } from "mydatepicker";
import moment from "moment";
import { Location, NgIf, NgClass, DatePipe } from "@angular/common";
import * as _ from "lodash";
import { RewardPointsService } from "../../../app-services/reward-points-service";
// import { AngularMultiSelectModule } from "angular2-multiselect-dropdown";
import { FormsModule } from "@angular/forms";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
@Component({
    selector: "nga-rewards-points-form",
    styleUrls: [
        "rewards-points-form.component.scss",
        "./../rewards-points.component.scss",
    ],
    templateUrl: "rewards-points-form.component.html",
    imports: [NgIf, FormsModule, NgClass],
    providers: [DatePipe, BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class RewardsPointsFormComponent {
  zoneDetails: any = [];
  regionsDetails: any = [];
  territoriesDetails: any = [];
  brandData: any = [];
  skuData: any = [];
  tableHead: any = [];
  sbuDetails: any = [];
  customersDetails: any = [];
  tableColName: any = [];
  profitCenter: any = "";
  zone: any = [];
  brand: any = "";
  region: any = [];
  sbu: any = [];
  product: any = [];
  customer: any = [];
  rewardPoints: any;
  rewardTitle: any;
  territory: any = [];
  rewardId: any;
  showForm: boolean = true;
  configurationSettings: any = {
    showPagination: true,
    perPage: 10,
    totalRecordCount: 0,
    currentPage: 0,
    showActionsColumn: true,
    actionsColumnName: "Actions",
    noDataMessage: "No data found",
  };
  currentPage: number = 1;
  activeData: any;
  model: any;
  showErrorBlockSection: boolean = false;
  showIndex: any = { index: null };

  showOtherFilters: any = {
    showRadioFilters: false,
    showSearch: false,
    add: true,
    showdropdown1Filters: false,
    showdropdown2Filters: false,
    showSearchiconFilters: false,
    showReport: false,
  };
  userButton: any = [];
  isAF: boolean = false;
  isSWAL: boolean = false;
  isSBO: boolean = false;
  isZone: boolean = false;
  isRegion: boolean = false;
  isTerritory: boolean = false;
  startDate: any;
  endDate: any;
  startDateOptions: IMyOptions = {
    dateFormat: "dd-mm-yyyy",
    showTodayBtn: false,
    inline: false,
    firstDayOfWeek: "su",
    editableDateField: false,
    showClearDateBtn: false,
    openSelectorOnInputClick: true,
    disableUntil: { day: 0, month: 0, year: 0 },
  };
  endDateOptions: IMyOptions = {
    dateFormat: "dd-mm-yyyy",
    showTodayBtn: false,
    inline: false,
    firstDayOfWeek: "su",
    editableDateField: false,
    showClearDateBtn: false,
    openSelectorOnInputClick: true,
    disableUntil: { day: 0, month: 0, year: 0 },
  };
  isCustomer: boolean = false;
  brandDropdownSettings = {
    text: "Select Brand",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 10,
  };
  skuDropdownSettings = {
    text: "Select SKU",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 10,
  };
  sbuDropdownSettings = {
    text: "Select SBU",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 10,
  };
  zoneDropdownSettings = {
    text: "Select Zone",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 10,
  };
  regionDropdownSettings = {
    text: "Select Region",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 10,
  };
  territoryDropdownSettings = {
    text: "Select Territory",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 10,
  };
  customerDropdownSettings = {
    text: "Select Customer",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 10,
  };
  selectedBrands: any = [];
  rewardMask = [/[0-9]/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/];
  selectedBrandCodes: any = [];
  selectedSKUCodes: any = [];
  selectedSBUCodes: any = [];
  selectedZoneCodes: any = [];
  selectedTerritoryCodes: any = [];
  selectedRegionCodes: any = [];
  selectedCustomerTypes: any = [];
  startDateModel: any = "";
  endDateModel: any = "";
  submitClicked: boolean = false;
  isCustomerCount: number = 0;
  constructor(
    private routes: ActivatedRoute,
    private eRef: ElementRef,
    private router: Router,
    private userService: UserService,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private events: GlobalEvents,
    private zonesService: ZonesService,
    private regionsService: RegionsService,
    private location: Location,
    private territoriesService: TerritoriesService,
    private rewardService: RewardPointsService
  ) {
    this.events.setChangedContentTopText("Add Target");
    this.rewardId = this.routes.snapshot.queryParams["reward_id"];
    if (this.rewardId) {
      this.showForm = false;
      this.customerDropdownSettings.disabled = true;
      this.territoryDropdownSettings.disabled = true;
      this.regionDropdownSettings.disabled = true;
      this.zoneDropdownSettings.disabled = true;
      this.sbuDropdownSettings.disabled = true;
      this.skuDropdownSettings.disabled = true;
      this.brandDropdownSettings.disabled = true;
      this.events.setChangedContentTopText("Edit Scheme");
      this.getRewards(this.rewardId);
    } else {
      this.isAF = true;
      this.profitCenter = "swal";
      this.PCchangeDropDown("swal");
    }
    this.getCustomersType();
  }

  ngOnInit() {
    this.setTableHeader();
    this.commonAddButton();
    window.scrollTo(0, 0);
  }

  commonAddButton(): void {
    this.userButton = [
      {
        path: "/rewards-points/add-rewards-points",
        title: " Add New",
      },
    ];
  }

  setTableHeader(): void {
    this.tableHead = [
      "Profit Center",
      "Territory",
      "Brand",
      "Rewards & Points",
    ];
    this.tableColName = [
      "profit_center",
      "territory",
      "brand",
      "rewards_and_points",
    ];
  }

  /**
   * Triggered when the profit center dropdown is changed
   * @param event
   * @constructor
   */
  PCchangeDropDown(event: any): void {
    this.isCustomer = false;
    (this.customer = ""),
      (this.zone = ""),
      (this.region = ""),
      (this.territory = ""),
      (this.sbu = "");
    (this.selectedBrands = []), (this.zone = []), (this.territory = []);
    (this.region = []), (this.sbu = []), (this.skuData = []);
    (this.customer = []), (this.product = []), (this.brandData = []);
    (this.zoneDetails = []),
      (this.sbuDetails = []),
      (this.regionsDetails = []),
      (this.territoriesDetails = []);
    (this.isTerritory = false),
      (this.isRegion = false),
      (this.isZone = false),
      (this.isSBO = false);
    this.rewardPoints = "";
    (this.endDate = ""), (this.startDate = "");
    (this.startDateModel = ""), (this.endDateModel = "");
    this.submitClicked = false;
    this.selectedBrandCodes = [];
    event == "swal"
      ? ((this.isSWAL = true),
        (this.isAF = false),
        this.getAllZones(),
        this.getAllProduct(AppConstant.PC_SWAL_CODE))
      : (this.isSWAL = false);
  }

  /**
   * called when start date filter is changed
   * @param event
   */
  onStartDateChanged(event: any): void {
    if (event && event.formatted) {
      this.startDate = "";
      this.endDate = "";
      this.endDateModel = "";
      this.startDate = event.formatted;
      const d: Date = event.jsdate;
      d.setDate(d.getDate() - 1);
      const disableDate = {
        day: d.getDate(),
        month: d.getMonth() + 1,
        year: d.getFullYear(),
      };
      this.endDateOptions = {
        dateFormat: "dd-mm-yyyy",
        showTodayBtn: false,
        inline: false,
        firstDayOfWeek: "su",
        editableDateField: false,
        showClearDateBtn: false,
        openSelectorOnInputClick: true,
        disableUntil: disableDate,
      };
    } else {
      this.startDate = "";
      this.endDateOptions = {
        dateFormat: "dd-mm-yyyy",
        showTodayBtn: false,
        inline: false,
        firstDayOfWeek: "su",
        editableDateField: false,
        showClearDateBtn: false,
        openSelectorOnInputClick: true,
        disableUntil: {
          day: 0,
          month: 0,
          year: 0,
        },
      };
    }
  }

  /**
   * called when end date filter is changed
   * @param event
   */
  onEndDateChanged(event: any): void {
    if (event && event.formatted) {
      this.endDate = "";
      this.endDate = event.formatted;
    } else {
      this.endDate = "";
    }
  }

  /**
   * Method to get all the SBUs list
   * @param data
   */

  getAllSbus(data: any): void {
    this.spinner.show();
    this.userService.getAllSbuByPC(data).subscribe({
      next: (sbusDetails: any) => {
        this.sbuDetails = [];
        if (sbusDetails && sbusDetails.content && sbusDetails.content.length) {
          sbusDetails.content.forEach((sbuInfo: any) => {
            const sbuInfoObj = {
              code: sbuInfo.code,
              id: sbuInfo.id,
              name: sbuInfo.name,
            };
            this.sbuDetails.push(sbuInfoObj);
          });
        }
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.SBU_FETCH_ERROR);
        this.spinner.hide();
      },
    });
  }

  /**
   * Method to get all the zones list
   * @param code
   */
  getAllZones(): void {
    this.spinner.show();
    this.zonesService.getAllZonesByPC().subscribe((zonesDetails: any) => {
      this.zoneDetails = [];
      zonesDetails.forEach((zoneInfo: any) => {
        const zoneInfoObj = {
          code: zoneInfo.code,
          id: zoneInfo.id,
          name: zoneInfo.name,
        };
        this.zoneDetails.push(zoneInfoObj);
      });
      this.spinner.hide();
    });
  }

  /**
   * Method to get all the zones list by SBU
   * @param _code
   */

  getAllZonesBySbu(_code?: any): void {
    this.spinner.show();
    this.zonesService
      .getAllZonesBySBU(encodeURIComponent(this.selectedSBUCodes.join(",")))
      .subscribe({
        next: (zonesDetails: any) => {
          this.zoneDetails = [];
          if (
            zonesDetails &&
            zonesDetails.content &&
            zonesDetails.content.length
          ) {
            zonesDetails.content.forEach((zoneInfo: any) => {
              const zoneInfoObj = {
                code: zoneInfo.code,
                id: zoneInfo.id,
                name: zoneInfo.name,
              };
              this.zoneDetails.push(zoneInfoObj);
            });
          }
          this.spinner.hide();
        },
        error: () => {
          this.toastr.error(AppConstant.ZONE_FETCH_ERROR);
          this.spinner.hide();
        },
      });
  }

  /**
   * Method to get all the regions list by zone
   * @param code
   */

  getAllRegionsByZone(code: any): void {
    this.spinner.show();
    const allRegions = this.regionsService.getAllRegionsByZoneCode(
      encodeURIComponent(code)
    );
    allRegions.subscribe({
      next: (regionsDetails: any) => {
        this.regionsDetails = [];
        if (
          regionsDetails &&
          regionsDetails.content &&
          regionsDetails.content.length
        ) {
          regionsDetails.content.forEach((regionInfo: any) => {
            const zoneInfoObj = {
              code: regionInfo.code,
              id: regionInfo.id,
              name: regionInfo.name,
            };
            this.regionsDetails.push(zoneInfoObj);
          });
        }
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.REGION_FETCH_ERROR);
        this.spinner.hide();
      },
    });
  }

  /**
   * Method to get all the territories list by zone
   * @param code
   */

  getAllTerritoriesByZone(code: any): void {
    this.spinner.show();
    this.territoriesService
      .getAllTerritoriesByZoneCode(encodeURIComponent(code))
      .subscribe({
        next: (territoriesDetails: any) => {
          this.territoriesDetails = [];
          if (
            territoriesDetails &&
            territoriesDetails.content &&
            territoriesDetails.content.length
          ) {
            territoriesDetails.content.forEach((territoriesInfo: any) => {
              const territoriesInfoObj = {
                code: territoriesInfo.code,
                id: territoriesInfo.id,
                name: territoriesInfo.name,
              };
              this.territoriesDetails.push(territoriesInfoObj);
            });
          }
          this.spinner.hide();
        },
        error: () => {
          this.toastr.error(AppConstant.TERRITORY_FETCHING_ERROR);
          this.spinner.hide();
        },
      });
  }

  /**
   * Method to get all the territories list by region
   * @param code
   */

  getAllTerritoriesByRegion(code: any): void {
    this.spinner.show();
    this.territoriesService
      .getAllTerritoriesByRegionCode(encodeURIComponent(code))
      .subscribe({
        next: (territoriesDetails: any) => {
          this.territoriesDetails = [];
          if (
            territoriesDetails &&
            territoriesDetails.content &&
            territoriesDetails.content.length
          ) {
            territoriesDetails.content.forEach((territoriesInfo: any) => {
              const territoriesInfoObj = {
                code: territoriesInfo.code,
                id: territoriesInfo.id,
                name: territoriesInfo.name,
              };
              this.territoriesDetails.push(territoriesInfoObj);
            });
          }
          this.spinner.hide();
        },
        error: () => {
          this.toastr.error(AppConstant.TERRITORY_FETCHING_ERROR);
          this.spinner.hide();
        },
      });
  }

  /**
   * Method to get all the brands list
   * @param _event
   */
  getAllProduct(_event: any): void {
    this.spinner.hide();
  }

  /**
   * Method to get all the SKUs list by brand
   * @param _event
   */
  productsByBrand(_event?: any): void {
    this.spinner.show();
    this.userService
      .skuDetailsByBrandCode(
        encodeURIComponent(this.selectedBrandCodes.join(","))
      )
      .subscribe((res: any) => {
        this.skuData = [];
        res.forEach(
          (skuDetails: any) => {
            let skuDetailsObj = {
              id: skuDetails.id,
              code: skuDetails.code,
              name: skuDetails.name,
              description: skuDetails.description,
            };
            this.skuData.push(skuDetailsObj);
          },
          (errResponse: any) => {
            this.toastr.error(AppConstant.BRAND_SKU_ERROR);
          }
        );
        this.spinner.hide();
      });
  }

  /**
   * Method to get the specific reward details
   * @param id
   */

  getRewards(id?: any): void {
    this.spinner.show();
    this.rewardService.getRewardDetailsById(id).subscribe({
      next: (data: any) => {
        if (data) {
          if (data.profitCenterCode == "DO1301") {
            this.profitCenter = "af";
            this.PCchangeDropDown("af");
          } else {
            this.profitCenter = "swal";
            this.PCchangeDropDown("swal");
          }
          this.rewardPoints = data.points ? data.points : "";
          this.rewardTitle = data.title ? data.title : "";
          this.selectedBrandCodes =
            data.brands && data.brands.length ? _.map(data.brands, "code") : [];
          this.selectedBrands =
            data.brands && data.brands.length ? data.brands : "";
          if (data.startDate) {
            const startDates = moment(data.startDate, "DD-MM-YYYY");
            let startDateObj: any = {
              date: {
                month: parseInt(startDates.format("MM")),
                day: parseInt(startDates.format("DD")),
                year: parseInt(startDates.format("YYYY")),
              },
            };
            this.startDateModel = startDateObj;
            this.startDate = data.startDate ? data.startDate : "";
            const momentDate = moment(this.startDate, "DD-MM-YYYY");
            const d: Date = momentDate.toDate();
            d.setDate(d.getDate() - 1);
            const disableDate = {
              day: d.getDate(),
              month: d.getMonth() + 1,
              year: d.getFullYear(),
            };
            this.endDateOptions = {
              dateFormat: "dd-mm-yyyy",
              showTodayBtn: false,
              inline: false,
              firstDayOfWeek: "su",
              editableDateField: false,
              showClearDateBtn: false,
              openSelectorOnInputClick: true,
              disableUntil: disableDate,
            };
          }
          if (data.endDate) {
            const endDates = moment(data.endDate, "DD-MM-YYYY");
            let endDateObj: any = {
              date: {
                month: parseInt(endDates.format("MM")),
                day: parseInt(endDates.format("DD")),
                year: parseInt(endDates.format("YYYY")),
              },
            };
            this.endDateModel = endDateObj;
            this.endDate = data.endDate ? data.endDate : "";
          }
          this.selectedSKUCodes =
            data.skus && data.skus.length ? _.map(data.skus, "code") : [];
          this.product = data.skus && data.skus.length ? data.skus : "";
          this.selectedSBUCodes =
            data.sbus && data.sbus.length ? _.map(data.sbus, "code") : [];
          this.sbu = data.sbus && data.sbus.length ? data.sbus : "";
          this.selectedZoneCodes =
            data.zones && data.zones.length ? _.map(data.zones, "code") : [];
          this.zone = data.zones && data.zones.length ? data.zones : "";
          this.selectedTerritoryCodes =
            data.territories && data.territories.length
              ? _.map(data.territories, "code")
              : [];
          this.territory =
            data.territories && data.territories.length ? data.territories : "";
          this.selectedRegionCodes =
            data.regions && data.regions.length
              ? _.map(data.regions, "code")
              : [];
          this.region = data.regions && data.regions.length ? data.regions : "";
          this.customer = data.customerTypes ? data.customerTypes : "";
          this.isCustomerCount = data.customerCount ? data.customerCount : 0;
          this.spinner.hide();
        }
      },
      error: () => {
        this.toastr.error(AppConstant.FETCH_SCHEME_ERR);
        this.router.navigate(["/rewards-points"]);
        this.spinner.hide();
      },
    });
  }

  /**
   * Triggered when a brand is selected in multi select dropdown
   * @param event
   */
  selectedBrand(event: any): void {
    if (!_.includes(this.selectedBrandCodes, event.code)) {
      this.selectedBrandCodes.push(event.code);
      this.skuData = [];
      this.product = [];
      this.productsByBrand();
    }
  }

  /**
   * Triggered when a brand is deselected in multi select dropdown
   * @param event
   */
  deSelectedBrand(event: any): void {
    if (_.includes(this.selectedBrandCodes, event.code)) {
      _.remove(this.selectedBrandCodes, (item) => item === event.code);
      if (this.selectedBrandCodes && this.selectedBrandCodes.length) {
        this.skuData = [];
        this.productsByBrand();
      } else {
        this.skuData = [];
      }
      this.product = [];
    }
  }
  /**
   * Triggered when all brands are selected in multi select dropdown
   * @param event
   */
  selectedAllBrands(event: any): void {
    if (event && event.length) {
      this.selectedBrandCodes = [];
      this.selectedBrandCodes = _.map(event, "code");
      this.productsByBrand();
    }
  }

  /**
   * Triggered when all brands are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllBrands(_event: any): void {
    this.selectedBrandCodes = [];
    this.skuData = [];
    this.product = [];
  }

  /**
   * Triggered when a SKU is selected in multi select dropdown
   * @param event
   */
  selectedSKU(event: any): void {
    if (!_.includes(this.selectedSKUCodes, event.code)) {
      this.selectedSKUCodes.push(event.code);
    }
  }

  /**
   * Triggered when a SKU is deselected in multi select dropdown
   * @param event
   */
  deSelectedSKU(event: any): void {
    if (_.includes(this.selectedSKUCodes, event.code)) {
      _.remove(this.selectedSKUCodes, (item) => item === event.code);
    }
  }

  /**
   * Triggered when all SKUs are selected in multi select dropdown
   * @param event
   */
  selectedAllSKUS(event: any): void {
    if (event && event.length) {
      this.selectedSKUCodes = [];
      this.selectedSKUCodes = _.map(event, "code");
    }
  }

  /**
   * Triggered when all SKUs are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllSKUS(_event: any): void {
    this.selectedSKUCodes = [];
  }

  /**
   * Triggered when a SBU is selected in multi select dropdown
   * @param event
   */
  selectedSBU(event: any): void {
    if (!_.includes(this.selectedSBUCodes, event.code)) {
      this.selectedSBUCodes.push(event.code);
      this.zone = [];
      this.selectedTerritoryCodes = [];
      this.territoriesDetails = [];
      this.territory = [];
      this.getAllZonesBySbu();
    }
  }

  /**
   * Triggered when a SBU is deselected in multi select dropdown
   * @param event
   */
  deSelectedSBU(event: any): void {
    if (_.includes(this.selectedSBUCodes, event.code)) {
      _.remove(this.selectedSBUCodes, (item) => item === event.code);
      this.zoneDetails = [];
      this.selectedTerritoryCodes = [];
      this.territoriesDetails = [];
      this.territory = [];
      this.zoneDetails = [];
      this.zone = [];
      if (this.selectedSBUCodes && this.selectedSBUCodes.length) {
        this.getAllZonesBySbu();
      }
    }
  }

  /**
   * Triggered when all SBUs are selected in multi select dropdown
   * @param event
   */
  selectedAllSBUS(event: any): void {
    if (event && event.length) {
      this.selectedSBUCodes = [];
      this.selectedSBUCodes = _.map(event, "code");
      this.getAllZonesBySbu();
    }
  }

  /**
   * Triggered when all SBUs are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllSBUS(_event: any): void {
    this.selectedSBUCodes = [];
    this.zoneDetails = [];
    this.zone = [];
    this.selectedTerritoryCodes = [];
    this.territoriesDetails = [];
    this.territory = [];
  }

  /**
   * Triggered when a zone is selected in multi select dropdown
   * @param event
   */
  selectedZone(event: any): void {
    if (!_.includes(this.selectedZoneCodes, event.code)) {
      this.selectedZoneCodes.push(event.code);
      this.region = [];
      this.territory = [];
      this.selectedTerritoryCodes = [];
      this.territoriesDetails = [];
      this.territory = [];
      if (this.isAF) {
        this.getAllTerritoriesByZone(this.selectedZoneCodes.join(","));
      } else if (this.isSWAL) {
        this.getAllRegionsByZone(this.selectedZoneCodes.join(","));
      }
    }
  }

  /**
   * Triggered when a zone is deselected in multi select dropdown
   * @param event
   */
  deSelectedZone(event: any): void {
    if (_.includes(this.selectedZoneCodes, event.code)) {
      _.remove(this.selectedZoneCodes, (item) => item === event.code);
      this.regionsDetails = [];
      this.territoriesDetails = [];
      this.region = [];
      this.territory = [];
      this.selectedTerritoryCodes = [];
      this.territoriesDetails = [];
      this.territory = [];
      if (this.selectedZoneCodes && this.selectedZoneCodes.length) {
        if (this.isAF) {
          this.getAllTerritoriesByZone(this.selectedZoneCodes.join(","));
        } else if (this.isSWAL) {
          this.getAllRegionsByZone(this.selectedZoneCodes.join(","));
        }
      }
    }
  }

  /**
   * Triggered when all zones are selected in multi select dropdown
   * @param event
   */
  selectedAllZones(event: any): void {
    if (event && event.length) {
      this.selectedZoneCodes = [];
      this.selectedTerritoryCodes = [];
      this.territoriesDetails = [];
      this.territory = [];
      this.selectedZoneCodes = _.map(event, "code");
      if (this.isAF) {
        this.getAllTerritoriesByZone(this.selectedZoneCodes.join(","));
      } else if (this.isSWAL) {
        this.getAllRegionsByZone(this.selectedZoneCodes.join(","));
      }
    }
  }

  /**
   * Triggered when all zones are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllZones(_event: any): void {
    this.selectedZoneCodes = [];
    this.regionsDetails = [];
    this.region = [];
    this.territoriesDetails = [];
    this.selectedTerritoryCodes = [];
    this.territory = [];
  }

  /**
   * Triggered when a region is selected in multi select dropdown
   * @param event
   */
  selectedRegion(event: any): void {
    if (!_.includes(this.selectedRegionCodes, event.code)) {
      this.selectedRegionCodes.push(event.code);
      if (this.isSWAL) {
        this.territory = [];
        this.getAllTerritoriesByRegion(this.selectedRegionCodes.join(","));
      }
    }
  }

  /**
   * Triggered when a region is deselected in multi select dropdown
   * @param event
   */
  deSelectedRegion(event: any): void {
    if (_.includes(this.selectedRegionCodes, event.code)) {
      _.remove(this.selectedRegionCodes, (item) => item === event.code);
      if (this.isSWAL) {
        this.territory = [];
        this.territoriesDetails = [];
        if (this.selectedRegionCodes && this.selectedRegionCodes.length) {
          this.getAllTerritoriesByRegion(this.selectedRegionCodes.join(","));
        }
      }
    }
  }

  /**
   * Triggered when all regions are selected in multi select dropdown
   * @param event
   */
  selectedAllRegions(event: any): void {
    if (event && event.length) {
      this.selectedRegionCodes = [];
      this.selectedRegionCodes = _.map(event, "code");
      if (this.isSWAL) {
        this.territory = [];
        this.territoriesDetails = [];
        this.getAllTerritoriesByRegion(this.selectedRegionCodes.join(","));
      }
    }
  }

  /**
   * Triggered when all regions are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllRegions(_event: any): void {
    this.selectedRegionCodes = [];
    this.territoriesDetails = [];
    this.territory = [];
  }

  /**
   * Triggered when a territory is selected in multi select dropdown
   * @param event
   */
  selectedTerritory(event: any): void {
    if (!_.includes(this.selectedTerritoryCodes, event.code)) {
      this.selectedTerritoryCodes.push(event.code);
    }
  }

  /**
   * Triggered when a territory is deselected in multi select dropdown
   * @param event
   */
  deSelectedTerritory(event: any): void {
    if (_.includes(this.selectedTerritoryCodes, event.code)) {
      _.remove(this.selectedTerritoryCodes, (item) => item === event.code);
    }
  }

  /**
   * Triggered when all territories are selected in multi select dropdown
   * @param event
   */
  selectedAllTerritories(event: any): void {
    if (event && event.length) {
      this.selectedTerritoryCodes = [];
      this.selectedTerritoryCodes = _.map(event, "code");
    }
  }

  /**
   * Triggered when all territories are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllTerritories(_event: any): void {
    this.selectedTerritoryCodes = [];
    this.territory = [];
  }

  /**
   * Triggered when a customer is selected in multi select dropdown
   * @param event
   */
  selectedCustomer(event: any): void {
    if (!_.includes(this.selectedCustomerTypes, event.id)) {
      this.selectedCustomerTypes.push(event.id);
    }
  }

  /**
   * Triggered when a customer is deselected in multi select dropdown
   * @param event
   */
  deSelectedCustomer(event: any): void {
    if (_.includes(this.selectedCustomerTypes, event.id)) {
      _.remove(this.selectedCustomerTypes, (item) => item === event.id);
    }
  }

  /**
   * Triggered when all customers are selected in multi select dropdown
   * @param event
   */
  selectedAllCustomers(event: any): void {
    if (event && event.length) {
      this.selectedCustomerTypes = [];
      this.selectedCustomerTypes = _.map(event, "id");
    }
  }

  /**
   * Triggered when all customers are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllCustomers(_event: any): void {
    this.selectedCustomerTypes = [];
    this.customer = [];
  }
  cancel(): void {
    this.location.back();
  }
  getCustomersType(): void {
    this.spinner.show();
    this.userService.getAllCustomersType().subscribe({
      next: (customer: any) => {
        this.customersDetails = [];
        if (customer && customer.content && customer.content.length) {
          customer.content.forEach((item: any) => {
            const customerInfo = {
              description: item.description,
              id: item.id,
              name: item.name,
            };
            this.customersDetails.push(customerInfo);
          });
        }
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.CUST_TYPE_FETCHING_ERROR);
        this.spinner.hide();
      },
    });
  }

  addScheme(): void {
    this.rewardTitle = this.rewardTitle.trim();
    this.submitClicked = true;
    if (
      this.rewardId &&
      this.rewardPoints &&
      this.rewardTitle &&
      this.startDate &&
      this.endDate
    ) {
      let updateAddscheme: any = {
        points: this.rewardPoints ? this.rewardPoints : 0,
        title: this.rewardTitle ? this.rewardTitle : "",
        startDate: this.startDate ? this.startDate : "",
        endDate: this.endDate ? this.endDate : "",
      };

      this.spinner.show();
      const updateSchemeDetails: any =
        this.rewardService.updateScheme(updateAddscheme);

      updateSchemeDetails.subscribe({
        next: (res: any) => {
          this.toastr.success(AppConstant.UPDATE_SCHEME_SUCCESS);
          this.router.navigate(["/rewards-points"]);
        },
        error: (errorResponse: any) => {
          if (
            errorResponse &&
            errorResponse.error &&
            errorResponse.error.message
          ) {
            this.toastr.error(errorResponse.error.message);
          } else {
            this.toastr.error(AppConstant.UPDATE_SCHEME_ERR);
          }
          this.spinner.hide();
        },
      });
    } else if (
      !this.rewardId &&
      this.selectedBrands.length &&
      this.rewardPoints &&
      this.rewardTitle &&
      this.startDate &&
      this.endDate
    ) {
      let addSchemeDet = {
        sbuCode: this.selectedSBUCodes ? this.selectedSBUCodes : [],
        brandCode: this.selectedBrandCodes ? this.selectedBrandCodes : [],
        profitCenterCode: this.profitCenter
          ? this.profitCenter == "af"
            ? AppConstant.PC_AF_CODE
            : AppConstant.PC_SWAL_CODE
          : "",
        customerTypeId: this.selectedCustomerTypes
          ? this.selectedCustomerTypes
          : [],
        regionCode: this.selectedRegionCodes ? this.selectedRegionCodes : [],
        skuCode: this.selectedSKUCodes ? this.selectedSKUCodes : [],
        points: this.rewardPoints ? this.rewardPoints : 0,
        title: this.rewardTitle ? this.rewardTitle : "",
        territoryCode: this.selectedTerritoryCodes
          ? this.selectedTerritoryCodes
          : [],
        zoneCode: this.selectedZoneCodes ? this.selectedZoneCodes : "",
        startDate: this.startDate ? this.startDate : "",
        endDate: this.endDate ? this.endDate : "",
      };

      this.spinner.show();
      const addSchemeDetails = this.rewardService.addScheme(addSchemeDet);

      addSchemeDetails.subscribe({
        next: (res: any) => {
          this.toastr.success(AppConstant.ADD_SCHEME_SUCCESS);
          this.router.navigate(["/rewards-points"]);
        },
        error: (errorResponse: any) => {
          if (
            errorResponse &&
            errorResponse.error &&
            errorResponse.error.message
          ) {
            this.toastr.error(errorResponse.error.message);
          } else {
            this.toastr.error(AppConstant.ADD_SCHEME_ERR);
          }
          this.spinner.hide();
        },
      });
    }
  }

  deleteScheme(): void {
    if (this.rewardId && !this.isCustomerCount) {
      this.spinner.show();
      const deleteSchemeDetails = this.rewardService.deleteSchemeURL(
        this.rewardId
      );

      deleteSchemeDetails.subscribe({
        next: () => {
          this.toastr.success(AppConstant.DELETE_SCHEME_SUCCESS);
          this.router.navigate(["/rewards-points"]);
        },
        error: (errorResponse: any) => {
          if (
            errorResponse &&
            errorResponse.error &&
            errorResponse.error.message
          ) {
            this.toastr.error(errorResponse.error.message);
          } else {
            this.toastr.error(AppConstant.DELETE_SCHEME_ERR);
          }
          this.spinner.hide();
        },
      });
    }
  }
}
