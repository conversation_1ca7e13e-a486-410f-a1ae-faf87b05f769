:host {
  // Dev server specific toast fixes
  ::ng-deep {
    .toast-container {
      position: fixed !important;
      z-index: 999999 !important; 
      pointer-events: auto !important;
      top: 12px !important;
      right: 12px !important;
      width: auto !important;
      max-width: 350px !important;
      display: block !important;
      visibility: visible !important;
      
      .ngx-toastr {
        opacity: 1 !important;
        box-shadow: 0 0 12px rgba(0, 0, 0, 0.25) !important;
        display: block !important;
        visibility: visible !important;
        pointer-events: auto !important;
      }
      
      .toast-success {
        background-color: #51A351 !important;
      }
      
      .toast-error {
        background-color: #BD362F !important;
      }
      
      .toast-info {
        background-color: #2F96B4 !important;
      }
      
      .toast-warning {
        background-color: #F89406 !important;
      }
    }
  }
}

@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

$font-size: 13px;

.reward-points-container {
  width: 96%;
  margin: 45px 2.5% 15px 2.5%;
  .reward-points-grid-container {
    width: 99%;
    float: left;
    border-radius: $border-radius;
    margin-top: 30px;
    position: relative;
    .reward-grid-action {
      padding: 0 15px;
      width: 100%;
      position: relative;
      margin-bottom: 10px;
      float: left;
      background: #fff;
      border-radius: $border-radius;
      box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.1);
      .reward-grid-search-container {
        width: 18%;
        float: left;
        padding: 1% 0;
        .reward-grid-search-input {
          flex-wrap: inherit;
          @include gridSearchInput();
          .input-fields {
            padding: 0.5rem 0.75rem;
            font-size: 14px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            line-height: 1.25;
          }
        }
        @include inputRightBorder($border-radius);
        .input-group {
          margin-bottom: 0;
          width: 100%;
          @include inputAddOnBorder($border-radius);
        }
        @include placeholderColor(#666, 0.7);
      }
      .reward-grid-action-container {
        width: 82%;
        float: left;
        padding: 1% 0;
        display: block;
        position: relative;
        .date-filter {
          width: 65%;
          float: left;
          height: 50px;
          .date-filter-title {
            float: left;
            width: 25%;
            padding: 2.5% 12px;
          }
          .date-filter-content {
            width: 55%;
            float: left;
            padding: 5px 0;
            .start-date {
              width: 48%;
              float: left;
              margin-left: 5px;
            }
            .end-date {
              width: 48%;
              float: right;
            }
          }
          .search-date-action {
            width: 20%;
            float: left;
            padding: 5px 0;
            .filter {
              height: 35px;
              width: 35%;
              float: left;
              background: #FF8033;
              font-size: 22px;
              text-align: center;
              border-radius: 3px;
              margin: 0 0 0 11px;
              color: #ffffff;
              cursor: pointer;
            }
            .disable {
              opacity: 0.9;
              cursor: not-allowed;
            }
          }
        }
        .reward-grid-action-add {
          @include addButtonContainer(); 
          float: right !important;
          .add-btn-width { 
            margin: 0 0 0 10px;
            
          }
          .export {
            width: auto;
            float: right;
            padding: 0 10px;
          }
        }
        .dropdown-adjustment {
          position: relative;
          top: 5px;
          .role-pc-dd {
            float: left;
            height: 35px;
            border-radius: 3px;
            width: auto;
            margin-left: 11px;
          }
        }
        .profit-center-dropdown {
          width: auto;
          float: left;
          .profit-center-title {
            float: left;
            padding: 7px;
            margin: 0;
          }
        }
      }
    }
    .main-campaign {
      padding-top: 20px;
      .panel {
        .panel-body {
          .wizard {
            width: 100%;
            float: left;
            background: #fff;
            margin: 0 0 10px 0 !important;
            border: 1px solid #e0e0e0;
            font-size: 14px;
            border-radius: $table-border-radius;

            a {
              // text-transform: uppercase;
              color: #195c94;
            }

            @media screen and (max-width: 500px) {
              font-size: 8px;
            }
            @media screen and (max-width: 768px) {
              font-size: 12px;
            }
            .profile-tab {
              float: left;
              text-align: center;
              border-right: 1px solid #e0e0e0;
              height: 50px;
              line-height: 50px;
              cursor: pointer;
              border-radius: 0px;
              &:hover {
                // background: #ddd9;
                background: rgba(221, 221, 221, 0.6);
              }
              &:last-child {
                border-right: none;
              }
            }
            .reward-tab-width {
              width: 50%;
            }
            .active {
              border-bottom: 3px solid #FF8033; 
              color: #FF8033;
              border-radius: 0px;
            }
          }
        }
      }
    }
    .no-result {
      width: 100%;
      float: left;
      background-color: white;
      text-align: center;
      padding: 1% 0;
      box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.1);
    }

    .reward-points-grid-data-parent-container {
      background: #ffffff;
    }
    .reward-points-grid-data-container {
      width: 100%;
      float: left;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;

      .reward-points {
        font-size: 15px;
        width: 100%;
        float: left;
      }

      .pc-container {
        width: 100%;
        float: left;
        padding: 10px 0 0 0;
        position: relative;
        .pc-title {
          font-weight: bold;
          color: #374767;
        }
        .delete-scheme {
          position: absolute;
          right: 15px;
          font-size: 17px;
          color: red;
          cursor: pointer;
        }
      }
      .dropdown-container {
        width: 50%;
        float: left;
        padding: 10px;
        @media screen and (max-width: 768px) {
          width: 100%;
          float: left;
        }
        .disable-dropdown {
          cursor: not-allowed;
        }

        select {
          width: 100%;
          float: left;
          height: 45px;
          border-radius: 5px;
        }
        .scheme-title {
          width: 100%;
          float: left;
          height: 45px;
          border-radius: 5px;
          padding: 5px;
          border: 1px solid #cccccc;
        }
        .start-date-container {
          width: 48%;
          float: left;
          @media screen and (max-width: 768px) {
            width: 100%;
            float: left;
          }
        }
        .end-date-container {
          width: 48%;
          float: right;
          @media screen and (max-width: 768px) {
            width: 100%;
            float: left;
          }
        }
        .mydrp {
          width: 100% !important;

          .selectiongroup {
            height: 43px !important;
          }

          .selection {
            padding: 0 0 0 10px !important;
            height: 43px !important;
            border: none;
          }
        }
        .error-message {
          .help-block {
            color: red;
          }
        }
      }

      .brand-selection {
        width: 100%;
        float: left;

        .brand-sku {
          width: 100%;
          float: left;
        }

        .customer-section {
          width: 100%;
          float: left;
          .zone-region {
            width: 100%;
            float: left;
          }
          .territory-customer {
            width: 100%;
            float: left;
          }
        }

        .reward-input {
          width: 100%;
          float: left;
          height: 45px;
          border-radius: 5px;
          padding: 5px;
          border: 1px solid #cccccc;
        }

        .rewards-action {
          width: 100%;
          float: left;
          .rewards-action-container {
            width: 100%;
            float: left;
            padding: 10px;
            .action-btn {
              border-radius: 3px;
              height: 40px;
              background-color: grey;
              color: #fff;
              font-weight: bold;
              min-width: 150px;
              cursor: pointer;
            }

            .submit-rewards {
              background-color: #FF8033;
              float: right;
            }

            .disableSubmit {
              opacity: 0.9;
              cursor: not-allowed;
              outline: 0;
            }
          }
        }
      }
    }

    .rewards-container {
      width: 100%;
      float: left;
      .reward-cards-container {
        width: 100%;
        float: left;
        .reward-cards {
          width: 50%;
          float: left;     
          padding: 5px ;
          @media screen and (max-width: 768px) {
            width: 100%;
          }
          .reward-details {
            width: 100%;
            float: left;
            .reward {
              width: 100%;
              float: left;
              background: #ffffff;
              padding: 10px;
              border-radius: 5px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12),
                0 1px 2px rgba(0, 0, 0, 0.24);
              position: relative;
              .reward-content {
                width: 100%;
                float: left;
                padding: 5px 0;
                span {
                  width: 50%;
                  float: left;
                  padding: 5px 0;
                  line-height: 22px;
                  word-break: break-all;
                  @media screen and (max-width: 600px) {
                    width: 100%;
                  }
                }
                .reward-fields {
                  width: 100% !important;
                  float: left;
                  .title {
                    width: auto !important;
                  }
                }
              }
              .reward-actions {
                position: absolute;
                color: #374767;
                right: 17px;
                top: 5px;
                cursor: pointer;
              }
            }
            .expired-scheme {
              border-left: 3px solid red;
            }
            .active-scheme {
              border-left: 3px solid #3c3;
            }
          }
        }
        .scheme-pagination {
          width: 100%;
          float: left;
        }
        .card-main-section {
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          -webkit-box-orient: horizontal;
          -webkit-box-direction: normal;
          -ms-flex-flow: row wrap;
          flex-flow: row wrap;
          .search-results {
            width: 100%;
          }
        }
      }
    }
  }
}
 

@media only screen and (max-width: 768px) {
  .reward-points-container {
    .reward-points-grid-container {
      .reward-grid-action {
        .reward-grid-search-container {
          width: 100%;
        }
        .reward-grid-action-container {
          width: 100%;
          .date-filter {
            width: 100%;
            .date-filter-title {
              width: 100% !important;
            }
            .date-filter-content {
              width: 100%;
            }
          }
          .reward-grid-action-add {
            width: 100% !important;
            .add-btn-width {
              width: 100%;
              margin: 10px;
            }
          }
        }
      }
    }
  }
}
@media only screen and (max-width: 1100px) {
  .reward-points-container {
    .reward-points-grid-container {
      .reward-grid-action {
        .reward-grid-action-container {
          .date-filter {
            .date-filter-title {
              width: 30%;
            }
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 968px) {
  .reward-points-container {
    .reward-points-grid-container {
      .reward-grid-action {
        .reward-grid-action-container {
          .date-filter {
            .date-filter-title {
              width: 35%;
            }
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 842px) {
  .reward-points-container {
    .reward-points-grid-container {
      .reward-grid-action {
        .reward-grid-action-container {
          .date-filter {
            .date-filter-title {
              width: 38%;
            }
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 781px) {
  .reward-points-container {
    .reward-points-grid-container {
      .reward-grid-action {
        .reward-grid-action-container {
          .date-filter {
            .date-filter-title {
              width: 40%;
            }
          }
        }
      }
    }
  }
}

.confirmrewardActiveContainer {
  @include confirmDialogueActiveInActive();
}

.modal-backdrop.fade {
  opacity: 0.6;
}

.confirmrewardActiveContainer .fade {
  opacity: 1 !important;
}

.disable-submit {
  opacity: 0.8;
  cursor: not-allowed;
  &:hover {
    opacity: 0.8;
    cursor: not-allowed;
  }
}

@media only screen and (max-width: 1100px) {
  .reward-points-container {
    .reward-points-grid-container {
      .reward-grid-action {
        .reward-grid-action-container {
          .date-filter {
            width: 65%;
            .date-filter-title {
              width: 20% !important;
            }
            .date-filter-content {
              width: 65% !important;
            }
            .search-date-action {
              width: 20% !important;
            }
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 650px) {
  .reward-points-container {
    .reward-points-grid-container {
      .reward-grid-action {
        .reward-grid-action-container {
          .date-filter {
            width: 100%;
            .date-filter-title {
              width: 100% !important;
            }
            .date-filter-content {
              width: 100% !important;
            }
            .search-date-action {
              width: 100% !important;
            }
          }
        }
      }
    }
  }
}
.mdc-text-field--outlined {
  padding-left: 6px !important;
}
:host::ng-deep mat-form-field {
  width: 100% !important;
}
:host::ng-deep .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper {
  padding-right: 0 !important;
  height: 35px !important;
}
ng-
  :host::ng-deep
  .mat-mdc-text-field-wrapper.mdc-text-field--outlined
  .mat-mdc-form-field-infix {
  padding-top: 5px !important;
  padding-bottom: 0px !important;
  font-size: 14px !important;
}
:host::ng-deep .mat-mdc-icon-button.mat-mdc-button-base {
  width: 48px !important;
  height: 48px !important;
}
:host::ng-deep .mat-mdc-form-field-flex {
  height: 35px !important;
}
