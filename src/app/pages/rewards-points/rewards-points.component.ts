import { Component } from "@angular/core";
import { RouterOutlet } from "@angular/router";
import { AddSchemeComponent } from "./add-scheme/add-scheme.component";
import { UpdateAddSchemeComponent } from "./update-add-scheme/update-add-scheme.component";
import { DatePipe } from "@angular/common";

import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MatButtonModule } from "@angular/material/button";
import { MatCardModule } from "@angular/material/card";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatNativeDateModule, MatOptionModule } from "@angular/material/core";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatIconModule } from "@angular/material/icon";
import { MatInputModule } from "@angular/material/input";
import { MatSelectModule } from "@angular/material/select";
import { AngularMultiSelectModule } from "angular2-multiselect-dropdown";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { BaThemeSpinner } from "src/app/theme/services/baThemeSpinner/baThemeSpinner.service";
@Component({
    selector: "nga-rewards-points",
    templateUrl: "rewards-points.component.html",
    styleUrls: ["rewards-points.component.scss"],
    imports: [
    RouterOutlet,
    CommonModule,
    MatCardModule,
    MatInputModule,
    MatCheckboxModule,
    MatNativeDateModule,
    MatButtonModule,
    // AngularMultiSelectModule,
    MatIconModule,
    ReactiveFormsModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatInputModule,
    MatNativeDateModule,
    MatFormFieldModule,
    MatDatepickerModule
],
    providers: [DatePipe, BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class RewardsPointsComponent {
  constructor() {
    window.scrollTo(0, 0);
  }
}
