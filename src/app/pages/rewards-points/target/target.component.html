<div class="app-container">
  <div class="app-grid-container">
    <div class="app-grid-data-parent-container">
      <div id="foo" class="app-grid-data-container">
        <div class="history-filter-container">
          <div class="left-column second-left-column">
            <div class="main-campaign">
              <div class="panel">
                <div class="panel-body">
                  <div class="wizard">
                    <a
                      class="profile-tab history-tab-width"
                      [ngClass]="{ active: isActiveTarget }"
                      (click)="rewardHistoryTab('isActiveTarget')"
                    >
                      <span> Target Details<span *ngIf="isActiveTarget"> ({{ totalCount }})</span>
                      </span>
                    </a>
                    <a
                      class="profile-tab history-tab-width"
                      [ngClass]="{ active: isTargetProgress }"
                      (click)="rewardHistoryTab('isTargetProgress')"
                    >
                     <span>Target Progress<span *ngIf="isTargetProgress"> ({{ totalCount }})</span>
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="right-column">
            <div class="input-group">
              <div class="search-input">
                <span class="input-group-add">
                  <i class="fa fa-search" aria-hidden="true"></i>
                  <input
                    #searchBox
                    class="input-fields"
                    placeholder="Type to search"
                    [(ngModel)]="model"
                    (ngModelChange)="onSearch($event)"
                    (keypress)="funRestSearchPrevent($event)"
                  />
                  <span (click)="clearSearch()" *ngIf="model?.length">
                    <img title="Clear" src="../../../../assets/img/icons8-cancel-50.png" alt="Clear" />
                  </span>
                </span>
              </div>
              <div class="filter-button">
                <button class="add custom-button" title="Filter" (click)="isActiveTarget ? filterDropdown($event) : filterDropdownProgress($event)">
                  <i class="fa fa-filter export-icon"></i>
                </button>
              </div>
              <ng-template #filterMenuDailogProgress>
                <div class="filter-main-menu-container">
                  <div class="filter-menu-heading">
                    <span class="filter-title-size">Filter By</span>
                  </div>
                  <div class="history-filter-menu-container">
                     <div class="area-filter-container">
                      <label> Region </label>
                       <angular2-multiselect [data]="regionDataList" [(ngModel)]="regionValue"
                                [settings]="regionDropdownSettings" (onSelect)="selectRegion($event)"
                                (onDeSelect)="deselectionRegion($event)" (onDeSelectAll)="deselectionAllRegion($event)"
                                [ngModelOptions]="{ standalone: true }">
                              </angular2-multiselect>
                    </div>
                     <div class="area-filter-container">
                      <label class="zone-label"> Zone </label>
                       <angular2-multiselect [data]="zoneDataList" [(ngModel)]="zoneValue" [settings]="zoneDropdownSettings"
                                (onSelect)="selectZone($event)" (onDeSelect)="deselectionZone($event)"
                                (onDeSelectAll)="deselectionAllZone($event)" [ngModelOptions]="{ standalone: true }">
                              </angular2-multiselect>
                    </div>
                  </div>
                  <div class="button-container">
                    <button type="button" class="btn-cancel" (click)="clearFilterProgress()">
                      {{ "Clear" }}
                    </button>
                    <button type="button" class="btn-submit" (click)="filterApplyProgress()">
                      {{ "Apply" }}
                    </button>
                  </div>
                </div>
              </ng-template>
              <div class="export-button">
                <button class="add" (click)="exportTargetData()" title="Export">
                  <i class="fa fa-share-square-o export-icon"></i>
                </button>
              </div>
              <div
                class="add-btn-container"
                *ngIf="isAdmin && isActiveTarget"
              >
                <button
                  class="addSchemeButton"
                  (click)="handleOpenTargetPopUp()"
                  title="Add"
                >
                  <img
                    width="18px"
                    src="assets/img/addButton.png" alt="Add Button"
                  />
                  {{ "Add Target" }}
                </button>
              </div>
            </div>
          </div>
                  
        </div>
        <div class="app-table">
          <dynamic-table
            [tableHeads]="tableHead"
            [tableData]="rewardsHistoryData"
            [tableConfiguration]="configurationSettings"
            [tableColName]="tableColName"
            (pageChange)="getPageData($event)"
            (editFormClose)="editTarget($event)"
            (onApproveTarget)="approveTarget($event)"
            (onRejectTarget)="openRejectDialog($event)"
            [showIndex]="showIndex"
          >
          </dynamic-table>
        </div>
        <div class="no-result" *ngIf="isMap && !rewardsHistoryData.length">
          No data found
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #filterMenuDailog>
  <div class="filter-main-menu-container">
    <div class="filter-menu-heading">
      <span class="filter-title-size">Filter By</span>
    </div>
    <div class="history-filter-menu-container">
      <div class="area-filter-container">
        <label> Target Status</label>
        <angular2-multiselect
          class="dropdown"
          [data]="redeemedMethodDetails"
          [(ngModel)]="redeemedMethod"
          [settings]="redeemedMethodSeasonDropdownSettings"
          (onSelect)="onRedeemedMethodHistorySelected($event)"
          (onDeSelect)="onRedeemedMethodHistoryDeSelected($event)"
          (onDeSelectAll)="onRedeemedMethodHistoryDeSelectedAll($event)"
          [ngModelOptions]="{ standalone: true }">
        >
        </angular2-multiselect>
      </div>
      <!-- <div class="area-filter-container">
        <label> Region</label>
        <angular2-multiselect
          class="dropdown"
          [data]="redeemedMethodDetails"
          [(ngModel)]="redeemedMethod"
          [settings]="redeemedMethodSeasonDropdownSettings"
          (onSelect)="onRedeemedMethodHistorySelected($event)"
          (onDeSelect)="onRedeemedMethodHistoryDeSelected($event)"
          (onDeSelectAll)="onRedeemedMethodHistoryDeSelectedAll($event)"
          [ngModelOptions]="{ standalone: true }">
        >
        </angular2-multiselect>
      </div>
          <div class="area-filter-container">
        <label> Zone</label>
        <angular2-multiselect
          class="dropdown"
          [data]="redeemedMethodDetails"
          [(ngModel)]="redeemedMethod"
          [settings]="redeemedMethodSeasonDropdownSettings"
          (onSelect)="onRedeemedMethodHistorySelected($event)"
          (onDeSelect)="onRedeemedMethodHistoryDeSelected($event)"
          (onDeSelectAll)="onRedeemedMethodHistoryDeSelectedAll($event)"
          [ngModelOptions]="{ standalone: true }">
        >
        </angular2-multiselect>
      </div> -->
    </div>
    <div class="button-container">
      <button type="button" class="btn-cancel" (click)="clearFilter()">
        {{ "Clear" }}
      </button>
      <button type="button" class="btn-submit" (click)="filterApply()">
        {{ "Apply" }}
      </button>
    </div>
  </div>
</ng-template>
<!-- <app-target-modal [style.display]="modalOpen ? 'block' : 'none'" [isOpen]="modalOpen" [modalType]="modalMode" [defaultValues]="targetDefaults" [addTargetType]="true"
  (close)="modalOpen = false" (submit)="handleTargetSubmit($event)">
</app-target-modal> -->

<app-target-modal 
  [style.display]="modalOpen ? 'block' : 'none'" 
  [isOpen]="modalOpen" 
  [modalType]="modalMode" 
  [defaultValues]="targetDefaults"
  [addTargetType]="true"
  (close)="modalOpen = false" 
  (submit)="handleTargetSubmit($event)"
  (getAllTargetData)="getAllTargetData()"
  >
  
</app-target-modal>

<ng-template #rejectDialog>
  <div class="reject-dialog">
    <h2 class="reject-title">Add Comment</h2>
    <div class="reject-content">
      <textarea 
        [(ngModel)]="rejectRemark" 
        placeholder="Enter your comment here..." 
        class="reject-textarea"
        rows="5">
      </textarea>
    </div>
    <div class="reject-actions">
      <button class="btn-cancel"  (click)="cancelReject()">Cancel</button>
      <button class="btn-submit" (click)="submitReject()" [disabled]="!rejectRemark || rejectRemark.length === 0" >Submit</button>
    </div>
  </div>
</ng-template>

<ng-template #approveConfirmDialog>
  <div class="confirm-dialog">
    <div class="image-container">
      <img src="../../../assets/img/target-dailog.svg" alt="dialog" />
    </div>
    <div class="confirm-content">
      <span class="text-fields">
        Are you sure you want to approve this target?
      </span>
    </div>
    <div class="action-button-section">
      <button class="action-btn submit-btn" (click)="confirmApproval()">
        Yes
      </button>
      <button class="action-btn cancel-btn" (click)="dialog.closeAll()">
        No
      </button>
    </div>
  </div>
</ng-template>

