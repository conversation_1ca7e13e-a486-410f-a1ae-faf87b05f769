<mat-card class="mat-elevation-z8 parent-container" id="mat1">
  <mat-card-title class="card-title" 
    >Scheme Details
  </mat-card-title> 
  <mat-card-actions>
    <form [formGroup]="userForm"> 
      <div class="container" style="width: 100%;">
        <div class="row">
          <div class="col-md-4 row-bottom">
            <div class="label">
              <mat-label>Crop<span class="star">*</span></mat-label>
            </div>
            <!-- <angular2-multiselect
              [data]="cropData"
              [(ngModel)]="selectedCrops"
              [settings]="cropDropdownSettings"
              (onSelect)="selectedCrop($event)"
              (onDeSelect)="deSelectedCrop($event)"
              (onSelectAll)="selectedAllCrop($event)"
              (onDeSelectAll)="deSelectedAllCrop()"
              [ngModelOptions]="{ standalone: true }"
            >
            </angular2-multiselect> -->
          </div>
          <div class="col-md-4">
            <div class="label">
              <mat-label>Brand<span class="star">*</span></mat-label>
            </div>

            <!-- <angular2-multiselect
              [data]="brandData"
              [(ngModel)]="selectedBrands"
              [settings]="brandDropdownSettings"
              (onSelect)="selectedBrand($event)"
              (onDeSelect)="deSelectedBrand($event)"
              (onSelectAll)="selectedAllBrands($event)"
              (onDeSelectAll)="deSelectedAllBrands($event)"
              formControlName="brands"
            >
            </angular2-multiselect> -->
            <span
              *ngIf="!selectedBrands.length && submitClicked"
              class="help-block sub-little-error confpass"
              >Please select brand</span
            >
          </div>
          <div class="col-md-4">
            <div class="label">
              <mat-label>SKU<span class="star">*</span></mat-label>
            </div>
            <div style="height: 20px">
              <!-- <angular2-multiselect
                [data]="skuData"
                [(ngModel)]="selectedSkuData"
                [settings]="skuDropdownSettings"
                (onSelect)="selectedSKU($event)"
                (onDeSelect)="deSelectedSKU($event)"
                (onSelectAll)="selectedAllSKUS($event)"
                (onDeSelectAll)="deSelectedAllSKUS($event)"
                formControlName="skus"
              >
                <input matInput disabled required />
              </angular2-multiselect> -->
            </div>
          </div>
        </div>
        <div class="row">
          <!-- <div class="col-md-4" >
            <div class="label inputmarginBottem">
              <mat-label>Packshot<span class="star">*</span></mat-label>
            </div>
            <div >
              <div class="input-container" >
                <i class="fa fa-times" aria-hidden="true"></i>
                <input
                required
                readonly
                formControlName="packshot"
                [ngModel]="imageName"
                class="input-field"
                />
                <mat-icon class="upload-icon">upload</mat-icon>
                <input
                readonly 
                  hidden
                  type="file" 
                  #fileInput
                  id="file"
                />
              </div>
            </div>
          </div> -->
          <div class="col-md-4">
            <div class="label inputmarginBottem">
              <mat-label>Reward Point Per Kg<span class="star">*</span></mat-label>
            </div>
            <mat-form-field class="Reward-full-width" appearance="outline">
              <input
                matInput
                placeholder="Reward Points"
                formControlName="point"
                id="points"
                autocomplete="off"
              />
              <mat-error *ngIf="userForm.get('point')?.invalid">
                Field is required
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-md-4">
            <div class="label">
              <mat-label>Select Start Date<span class="star">*</span> </mat-label> 
            </div>

            <div class="dropdown-container">
              <div class="start-date-container">
                <mat-form-field
                  (click)="picker.open()"
                  class="custom-datepicker-container"
                  appearance="outline"
                > 
                  <input
                      [(ngModel)]="startDateModel"
                      (click)="picker.open()"
                      placeholder="Start Date"
                      autocomplete="off"
                      (dateChange)="onStartDateChanged($event)"
                      class="custom-datepicker reward-date-range"
                      matInput
                      [matDatepicker]="picker"
                      [min]="minDate"
                      name="startDate"
                      [ngModelOptions]="{standalone: true}"
                    />
                  <mat-datepicker-toggle
                    matIconSuffix
                    [for]="picker"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #picker></mat-datepicker>
                </mat-form-field> 
                <div class="error-message">
                  <span
                    *ngIf="!startDateModel && submitClicked"
                    class="help-block sub-little-error confpass"
                    >Start date is required</span
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="label"> 
              <mat-label>Select End Date<span class="star">*</span> </mat-label> 
            </div>

            <div class="end-date-container">
              <mat-form-field
                (click)="picker1.open()"
                class="custom-datepicker-container"
                appearance="outline"
              >
              <input
              [(ngModel)]="endDateModel"
              autocomplete="off"
              (click)="picker1.open()"
              placeholder="End Date"
              (dateChange)="onEndDateChanged($event)"
              class="custom-datepicker reward-date-range"
              matInput
              [matDatepicker]="picker1"
              [min]="startDateModel"
              name="endDate"
              [ngModelOptions]="{standalone: true}"
            />
                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="picker1"
                ></mat-datepicker-toggle>
                <mat-datepicker #picker1></mat-datepicker>
              </mat-form-field>

              <div class="error-message">
                <span
                  *ngIf="!endDateModel && submitClicked"
                  class="help-block sub-little-error confpass"
                  >End date is required</span
                >
              </div>
            </div>
          </div>
        </div>
        <div class="row">
            
            <div class="col-md-4">
              <div class="label">
                <mat-label>Zone<span class="star">*</span></mat-label>
              </div>
              <!-- <angular2-multiselect
                [data]="zonesList"
                [(ngModel)]="selectedZones"
                [settings]="zoneDropdownSettings"
                (onSelect)="selectedZone($event)"
                (onDeSelect)="deSelectedZone($event)"
                (onSelectAll)="selectedAllZones($event)"
                (onDeSelectAll)="deSelectedAllZones()"
                formControlName="selectedZones"
              >
              </angular2-multiselect> -->
            </div>
            <div class="col-md-4">
              <div class="label">
                <mat-label>Region<span class="star">*</span></mat-label>
              </div>
              <div>
                <!-- <angular2-multiselect
                  [data]="regionsList"
                  [(ngModel)]="selectedRegions"
                  [settings]="regionDropdownSettings"
                  (onSelect)="selectedRegion($event)"
                  (onDeSelect)="deSelectedRegion($event)"
                  (onSelectAll)="selectedAllRegions($event)"
                  (onDeSelectAll)="deSelectedAllRegions()"
                  formControlName="regions"
                >
                </angular2-multiselect> -->
              </div>
            </div>
        </div>
      </div>

    </form>
  </mat-card-actions>
</mat-card>
<mat-card class="mat-elevation-z8 card2">
  <div class="form-row org-desc-parent-margin">
    <!-- <h1 style="margin: 0; display: inline; float: left">Slab</h1> -->
    <mat-card-title class="card-title" 
    >Slabs
  </mat-card-title> 
    <button
      class="btn  add-button add-slab-button"
      color="primary"
      (click)="addRowSlabs()" 
       [disabled]=" disabledButton()"
    >
      <mat-icon class="icons">add</mat-icon> Add Slab
    </button>
  </div>
  <div class="container">
    <form class="example-form" [formGroup]="slabForm">
      <div formArrayName="sectionFormArray">
        <div
          *ngFor="let item of sectionFormArray['controls']; let i = index"
          [formGroupName]="i"
        >
          <div id="card3" style="overflow-x: auto">
            <mat-card class="mat-elevation-z8">
              <div class="form-row2">
                <button
                  *ngIf="i > 0"
                  class="btn btn-primary float-end mb-3 deleteBtn"
                  style="
                    margin: 0px;
                    display: inline;
                    float: right;
                    width: 50px;
                    display: flex;
                  "
                  disabled
                >
                  <mat-icon
                    class="delIcons"
                    style="
                      vertical-align: middle;
                      top: 4px;
                      right: 4px;
                      position: relative;
                    "
                    >delete
                  </mat-icon>
                </button>
              </div>

              <!-- <div class="table-responsive">
                <table class="table">
                  <tr>
                    <td>
                      <div>
                        <div>
                          <mat-label class="label2">Slab*</mat-label>
                        </div>
                        <mat-form-field appearance="outline">
                          <input
                            matInput
                            formControlName="slab"
                            class="form-control"
                            autocomplete="off"
                            placeholder="Slab{{ i + 1 }}"
                            ngModel="Slab{{ i + 1 }}"
                          />
                          <mat-error
                            *ngIf="
                              this.sectionFormArray.at(i).get('slab')?.invalid
                            "
                          >
                            Field is required
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </td>

                    <td>
                      <div>
                        <div>
                          <mat-label class="label2">Min*</mat-label>
                        </div>
                        <mat-form-field appearance="outline">
                          <input
                            matInput
                            type="number"
                            formControlName="minQuantity"
                            placeholder="Min Quantity (Kg)*"
                            (ngModelChange)="maxValueOnChange1($event)"
                            [min]="
                              i > 0
                                ? this.sectionFormArray
                                    .at(i - 1)
                                    .get('maxQuantity')?.value
                                : 0
                            "
                            class="form-control"
                            required
                            (keyup)="minKeypress(i)"
                            autocomplete="off"
                          />
                          <mat-error
                            *ngIf="
                              this.sectionFormArray.at(i).get('minQuantity')
                                ?.invalid
                            "
                          >
                            Field is required
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </td>
                    <td>
                      <div>
                        <div>
                          <mat-label class="label2">Max* </mat-label>
                        </div>
                        <mat-form-field appearance="outline">
                          <input
                            matInput
                            type="number"
                            formControlName="maxQuantity"
                            placeholder="Max Quantity (Kg)*"
                            class="form-control"
                            required
                            [min]="
                              this.sectionFormArray.at(i).get('minQuantity')
                                ?.value
                            "
                            max="10"
                            (ngModelChange)="maxValueOnChange($event)"
                            (keyup)="maxKeypress(i)"
                            autocomplete="off"
                          />
                        </mat-form-field>
                        <span *ngIf="invalidNumber" id="span">
                          Can not be less than Min {{ minQuant }}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div>
                        <div>
                          <mat-label class="label2">Bonus*</mat-label>
                        </div>
                        <mat-form-field appearance="outline">
                          <input
                            type="number"
                            placeholder="Bonus"
                            formControlName="bonus"
                            matInput
                            class="form-control"
                            required
                            autocomplete="off"
                            [disabled]="true"
                          />
                          <mat-error
                            *ngIf="
                              this.sectionFormArray.at(i).get('bonus')?.invalid
                            "
                          >
                            Field is required
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </td>
                  </tr>
                </table> -->

                <div class="container">
                  <div class="row">
                    <div class="col-md-3"> 
                      <div>
                        <div>
                          <mat-label class="label2">Slab<span class="star">*</span></mat-label>
                        </div>
                        <mat-form-field appearance="outline">
                          <input
                            matInput
                            formControlName="slab"
                            class="form-control"
                            autocomplete="off"
                            placeholder="Slab{{ i + 1 }}"
                            ngModel="Slab{{ i + 1 }}"
                          />
                          <mat-error
                            *ngIf="
                              this.sectionFormArray.at(i).get('slab')?.invalid
                            "
                          >
                            Field is required
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div>
                        <div>
                          <mat-label class="label2">Min<span class="star">*</span></mat-label>
                        </div>
                        <mat-form-field appearance="outline">
                          <input
                            matInput
                            type="number"
                            formControlName="minQuantity"
                            placeholder="Min Quantity (Kg)*"
                            (ngModelChange)="maxValueOnChange1($event)"
                            [min]="
                              i > 0
                                ? this.sectionFormArray
                                    .at(i - 1)
                                    .get('maxQuantity')?.value
                                : 0
                            "
                            class="form-control"
                            required
                            (keyup)="minKeypress(i)"
                            autocomplete="off"
                          />
                          <mat-error
                            *ngIf="
                              this.sectionFormArray.at(i).get('minQuantity')
                                ?.invalid
                            "
                          >
                            Field is required
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div>
                        <div>
                          <mat-label class="label2">Max<span class="star">*</span> </mat-label>
                        </div>
                        <mat-form-field appearance="outline">
                          <input
                            matInput
                            type="number"
                            formControlName="maxQuantity"
                            placeholder="Max Quantity (Kg)*"
                            class="form-control"
                            required
                            [min]="
                              this.sectionFormArray.at(i).get('minQuantity')
                                ?.value
                            "
                            max="10"
                            (ngModelChange)="maxValueOnChange($event)"
                            (keyup)="maxKeypress(i)"
                            autocomplete="off"
                          />
                        </mat-form-field>
                        <span *ngIf="invalidNumber" id="span">
                          Can not be less than Min {{ minQuant }}
                        </span>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div>
                        <div>
                          <mat-label class="label2">Bonus<span class="star">*</span></mat-label>
                        </div>
                        <mat-form-field appearance="outline">
                          <input
                            type="number"
                            placeholder="Bonus"
                            formControlName="bonus"
                            matInput
                            class="form-control"
                            required
                            autocomplete="off"
                            [disabled]="true"
                          />
                          <mat-error
                            *ngIf="
                              this.sectionFormArray.at(i).get('bonus')?.invalid
                            "
                          >
                            Field is required
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              <!-- </div> -->
            </mat-card>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="row">
    <!-- <div
      class="action-button" 
    >
      <button
        class="btn btn-cancel"
        id="cancel-button one"
        (click)="cancelButton()" 
      >
        Cancel
      </button>
      <button
      class="btn btn-primary submit-btn"
      type="submit"
      [disabled]="!userForm.valid"
      (click)="updateSchemeByRewarId()"
      style="width: 20%; float: right; background-color: #FF8033; color: #fff;"
    >
      Update
    </button>
    </div> -->
    <!-- <div class="column">
      <button
        class="btn btn-primary submit-btn"
        type="submit"
        [disabled]="!userForm.valid"
        (click)="updateSchemeByRewarId()"
        style="width: 20%; float: right; background-color: #FF8033; color: #fff;"
      >
        Update
      </button>
    </div> -->
  </div>
  <div class="parent-button">
    <!-- class="btn btn-primary" -->
      <button class="cancel-button" (click)="cancelButton()"> Cancel </button>
      <!-- class="btn btn-primary" -->
      <button class="submit-button" type="submit"  [disabled]="!userForm.valid && !startDateModel && !endDateModel"
      (click)="updateSchemeByRewarId()">Update</button>
  </div>
</mat-card>