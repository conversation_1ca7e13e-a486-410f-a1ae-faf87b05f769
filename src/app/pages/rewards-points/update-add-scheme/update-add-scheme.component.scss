.parent-container {
  .mat-form-field-flex {
    height: 49px;
    width: 115%;
    margin-left: 0px;
  }
  .mat-form-field-appearance-outline .mat-form-field-outline {
    border-color: #1919a4;
  }
}
.region-full-width {
  .cdk-text-field-autofill-monitored[_ngcontent-c8]:not(:-webkit-autofill)
    .dropdown-container {
    margin-left: 10px;
  }
}
mat-card {
  margin-left: 30px;
  margin-right: 10px;
  margin-top: 0.5%;
  text-align: center;
  height: 45%;
  width: 95%;
  margin-bottom: 10px;
}

.label {
  text-align: left;
  margin-left: 5px;
  font-size: 15px;
  position: relative;
}
.label2 {
  text-align: left;
  float: left;
  font-size: 17px;
  position: relative;
  margin-left: 20px;
}
#mat1 {
  margin-top: 7% !important;
}
#card3 mat-card mat-form-field {
  height: 500%;
  margin-bottom: 25px;
}
#span[_ngcontent-c8] {
  position: relative;
  top: -55px;
  display: block;
  font-size: 10px;
  font-family: sans-serif;
  color: red;
  float: left;
  margin-left: 25px;
}

#card3 {
  border-bottom: 50px;
}
.card2 {
  margin-bottom: 30px;
  .container {
    width: 100%;
  }
}
.form-row {
  margin-bottom: 15px;
}
.form-row2 {
  float: right;
  margin-right: 30px;
}
h1 {
  text-align: left;
}
form {
  background: none;
  margin-left: 10px;
  width: 100%;
}
table {
  position: relative;
  width: 100%;
}
td {
  padding-right: 8px;
  width: 25%;
}
.btn-container button {
  border: 1px solid black;
}

.btn-primary {
  width: 12.6%;
  height: 35.5px;
  margin-top: 50px;
  margin-right: 350px;
  font-size: 20px;
  padding: 5px 6px;
  text-align: center;
}
#submit-button {
  margin-left: -110px;
}
#cancel-button {
  margin-left: 300px;
  background-color: #a9abad;
  border: none;

  width: 12.6%;
  height: 35.5px;
  margin-top: 50px;
  margin-right: 350px;
  font-size: 20px;
  padding: 5px 6px;
  text-align: center;
}
.star{
  color: red !important;
}

.org-desc-parent-margin button {
  text-align: center;
  margin-right: 75px;
  height: 35px;
  color: #fff;
  font-weight: 800;
  padding: 8px 12px;
}
.icons {
  transform: scale(1.5);
  font-size: 14px;
  text-align: center;
  padding-top: 3px;
  color: #fff;
  padding-right: 5px;
  font-weight: 800;
}
.delIcons {
  transform: scale(1.5);
  font-size: 16px;
  text-align: center;
  padding-left: 10px;
  text-align: center;
}

.form-row {
  display: flex;
  justify-content: space-between;
}
.brand-width {
  margin-right: 25px;
  flex-direction: row;
  flex-wrap: wrap;
  position: relative;
  border-radius: 5px;
  top: 0px;
  margin-bottom: 30px;
  height: 43px;
  background: none;
  border: 1px solid rgb(189, 187, 187);
}
.example-full-width {
  margin-right: 25px;
  flex-direction: row;
  flex-wrap: wrap;
  position: relative;
  top: -20px;
  margin-bottom: 30px;
  height: 50px;
}

::ng-deep {
  .mat-form-field:not(.mat-form-field-appearance-legacy)
    .mat-form-field-suffix
    .mat-icon-button {
    display: block;
    font-size: inherit;
    width: 2.5em;
    height: 2.5em;
    position: relative;
    top: -5px;
  }
}

.packshot-full-width {
    i{
      position: relative;
      top: -10px;
    }
    input{
      width: 90%;
      left: 18px;
    }
  .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
    -webkit-animation-name: cdk-text-field-autofill-end;
    animation-name: cdk-text-field-autofill-end;
    position: relative;
  }
  padding-right: 7px;
  flex-direction: row;
  flex-wrap: wrap;
  height: 10px;
  width: 100%;

  margin: -4px 40px 54px 0px !important;
  matInput {
    height: 20px;
  }

  .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 0.75em 0 0.75em;
    margin-top: -7px;
    position: relative;
    width: 50%;
  }
}
.Reward-full-width {
  .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
    -webkit-animation-name: cdk-text-field-autofill-end;
    animation-name: cdk-text-field-autofill-end;
    position: relative;
  }
  :host
    .cdk-text-field-autofill-monitored[_ngcontent-c8]:not(:-webkit-autofill)
    .dropdown-container {
    text-align: left;
    position: relative;
    top: -7px;
  }
  padding-right: 7px;
  flex-direction: row;
  flex-wrap: wrap;
  height: 10px;
  width: 100%;
  margin: -5px 40px 54px 0px !important;
  matInput {
    height: 20px;
  }
}
.container {
  .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
    -webkit-animation-name: cdk-text-field-autofill-end;
    animation-name: cdk-text-field-autofill-end;
    position: relative;
    top: -12px;
  }
}
.slab-field {
  display: inline-flex;
  top: 10px;
  width: 100%;
}
.slab-field {
  mat-form-field {
    position: relative;
    width: 100%;
    margin-left: 0px;
    padding: 10px;
  }
}
element.style {
  margin-left: 90%;
}

#cancel-button {
  float: right;
  width: 70%;
}
@media screen and (min-width: 600px) {
  .column {
    height: auto;
    overflow: hidden;
  }
}

* {
  box-sizing: border-box;
}
.column {
  float: left;
  width: 50%;
  padding: 10px;
  height: 100px;
}
.row:after {
  content: "";
  display: table;
  clear: both;
}
@media screen and (max-width: 600px) {
  .column {
    width: 100%;
  }
}

@media screen and (min-width: 1500px) {
  .column {
    height: auto;
    overflow: hidden;
    float: left;

    .submit-btn {
      float: left !important;
      left: 5%;
      position: relative;
    }
  }
}
.submit-btn {
  float: left !important; 
      height: 35px;
      margin-top: 50px; 
      font-size: 14px;
      font-weight: 500;
}
 

:host::ng-deep .table > :not(caption) > * > * {
  padding: 0px;
}
mat-card-actions {
  margin-top: 10px;
}
:host::ng-deep mat-card {
  padding-top: 20px !important;
  margin-top: 4% !important;
}
:host::ng-deep mat-card {
  padding: 24px;
}

:host::ng-deep .packshot-full-width .mdc-notched-outline {
  border: 1px solid #ccc;
  border-radius: 4px;
}
:host::ng-deep .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper {
  padding: 2px 0 40px 6px;

  outline: unset;
}

.mdc-notched-outline__notch {
  border-right: none;
}
.inputmarginBottem {
  margin-bottom: 6px;
}
:host::ng-deep .mat-mdc-form-field-flex {
  margin: 3px;
}

.deleteBtn {
  cursor: not-allowed !important;
  pointer-events: auto !important;
}


.row-bottom{
  margin-bottom: 24px;
}

.card-title{
  text-align: left;
  // margin-left: 48px;
  margin-bottom: -12px;
  font-size: 18px;
  color: #374767;
  font-weight: 700;
}

.add-slab-button{
  margin: 0; 
  display: inline; 
  float: right; 
  display: flex; 
  background-color: #FF8033;
}
 

.btn-cancel{
  margin-left: 300px;
  background-color: #a9abad;
  color: #fff;
  border: none;
  width: 12.6%;
  height: 35px;
  margin-top: 50px;
  margin-right: 350px;
  font-size: 14px;
  font-weight: 500;
  padding: 5px 6px;
  text-align: center;
}


.disabled-cursor {
  cursor: not-allowed;
}

#file{
  background-color: #FF8033;
}
 
::ng-deep .mat-form-field-label {
  transform: translateY(-20px);  
  font-size: 14px; 
}


::ng-deep .mat-input-element {
  padding-top: 10px;  
}


.custom-input{
  position: relative !important;
  top: -35px !important;
  width: 97% !important;
}


.input-container {
  height: 42px !important;
  border: 1px solid #ccc;
  border-radius: 5px;
  display: flex;
  align-items: center;
  position: relative;
  background-color: #ffffff;
  bottom: 6px;
  cursor: not-allowed;
  i{
    margin-left: 10px;
    cursor: not-allowed;
  }
}

.input-field {
  flex: 1;
  padding: 8px 0px 8px 5px;
  border: none;
  // border: 1px solid #ccc;
  border-radius: 5px;
  outline: none;
  height: 38px ;
  cursor: not-allowed;
}

.input-field::placeholder {
  color: #999;
}

.parent-button{
  .cancel-button{
    width: 10%;
    margin-right: 20px;
    background-color: #a9abad;
    border: none; 
    color: white;
    font-weight: bold; 
    border-radius: 3px;
    cursor: pointer; 
    font-size: 14px; 
    height: 35px;
  }
  .submit-button{
    width: 10%;
    background-color: #FF8033;
    color: #ffffff;
    border: none;
    font-weight: bold; 
    border-radius: 3px; 
    font-size: 14px; 
    height: 35px;
  }
  .submit-button:disabled{
    opacity: 0.6;
    cursor: not-allowed;
  }
}