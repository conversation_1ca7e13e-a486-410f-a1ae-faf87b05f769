import {  waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';

import { UpdateAddSchemeComponent } from './update-add-scheme.component';

describe('UpdateAddSchemeComponent', () => {
  let component: UpdateAddSchemeComponent;
  let fixture: ComponentFixture<UpdateAddSchemeComponent>;

  beforeEach( waitForAsync(() => {
    TestBed.configureTestingModule({
    imports: [UpdateAddSchemeComponent]
})
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UpdateAddSchemeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
