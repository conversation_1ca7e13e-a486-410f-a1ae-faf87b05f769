import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from "@angular/core";
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from "@angular/forms";
import { UserService } from "src/app/app-services/user-service";
import { ApiServiceService } from "../../api-service.service";
import { TerritoriesService } from "../../../app-services/territories-service";
import { RegionsService } from "../../../app-services/regions-service";
import { ToastrService } from "ngx-toastr";
import { AppConstant } from "../../../constants/app.constant";
import { ZonesService } from "../../../app-services/zones-service";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { IMyOptions } from "mydatepicker";
import * as _ from "lodash";
import { RewardPointsService } from "src/app/app-services/reward-points-service";
import moment from "moment";
import { ActivatedRoute, Router } from "@angular/router";
import { DatePipe, NgIf, NgFor, CommonModule } from "@angular/common";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatIconModule } from "@angular/material/icon";
import { MatButtonModule } from "@angular/material/button";
import { MatInputModule } from "@angular/material/input";
// import { AngularMultiSelectModule } from "angular2-multiselect-dropdown";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatCardModule } from "@angular/material/card";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { GlobalEvents } from "src/app/helpers/global.events"; 
import { DashboardService } from "src/app/app-services/dashboard.service";
import { NgxDropzoneModule } from "ngx-dropzone";
import { Utility } from "src/app/shared/utility/utility";

@Component({
    selector: "app-update-add-scheme",
    templateUrl: "./update-add-scheme.component.html",
    styleUrls: ["./update-add-scheme.component.scss"],
    providers: [DatePipe, BaThemePreloader, BaThemeSpinner, BaMenuService],
    imports: [
        MatCardModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        NgIf,
        MatInputModule,
        MatButtonModule,
        MatIconModule,
        MatDatepickerModule,
        NgFor,
        CommonModule,
        NgxDropzoneModule,
    ]
})
export class UpdateAddSchemeComponent implements OnInit {
  @Input() disabled = true;
  selectedBrands: any = [];
  names!: any[];
  name = "Angular Multi Select Dropdown";
  dropdownList: any = [];
  dropdownSettings: any = {};
  dropDownForm!: FormGroup;
  selectedItems: any = [];
  data: any = [];
  selectedSKUCodes: any = [];
  zonesList: any = [];
  selectedZones: any = [];
  selectedZoneCodes: any = [];
  selectedRegions: any = [];
  regionsList: any = [];
  selectedRegionCodes: any = [];
  file!: File;
  fileName = "";
  selectedFiles: any;
  fileToUpload!: File;
  imageDetails!: any[];
  uploadedImage: any;
  startDate: any;
  endDate: any;
  endDateModel: Date | undefined;
  startDateModel:  Date | undefined;
  rewardId: any;
  slab: string = "";
  maxQuantity: string = "";
  minQuantity: string = "";
  bonus: string = "";
  rewardPoints: any;
  rewardTitle: any;
  selectedSlab: any = [];
  submitClicked: boolean = false;
  brandData: any = [];
  selectedBrandCodes: any = [];
  skuData: any = [];
  selectedSkuData: any = [];
  product: any = [];
  points: string = "";
  userButton!: { path: string; title: string }[];
  slabData: any = [];
  reward_bonus: any = [];
  skuDataItem: any = [];
  regionDataItem: any = [];
  regionItem: any = [];
  startDateISO!: {};
  resultTitle!: string;
  brandsCodes!: string;
  public userForm!: FormGroup;
  gallaryImagesDetails!: any[];
  files!: any[];
  packshotFiles: any;
  minQuantityFlag: boolean = false;
  maxQuantityFlag: boolean = false;
  slabForm!: FormGroup;
  minQuant: any;
  maxQuant: any;
  isRewardId: any;
  resData: any = [];
  dataArray: any = [];
  dataArry: any;
  setDate: any;
  startDatevalue!: string;
  endDateSelect: any;
  isDisabled = true;
  selectedCropId: any = 0;
  cropData: any = [];
  cropAllData: any = [];
  selectedCrops: any = [];
  isPackshotImageDisabled : boolean = false;

  minDate: any;
  maxDate: any;
  randomNumber : any;

  skuDropdownSettings = {
    text: "Select SKU",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 1,
    autoPosition: false,
  };

  zoneDropdownSettings = {
    text: "Select Zone",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 1,
    autoPosition: false,
  };

  regionDropdownSettings = {
    text: "Select Region",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "regionName",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: false,
    badgeShowLimit: 1,
    autoPosition: false,
  };

  brandDropdownSettings = {
    text: "Select Brand",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 200,
    disabled: true,
    badgeShowLimit: 1,
    autoPosition: false,
  };
  cropDropdownSettings = {
    text: "Select Crop",
    enableSearchFilter: true,
    classes: "myclass customer-dd brands-multi-select",
    labelKey: "name",
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: true,
    badgeShowLimit: 1,
    autoPosition: false,
  };


  constructor(
    private userService: UserService,
    private rewardService: RewardPointsService,
    private routes: ActivatedRoute,
    private fb: FormBuilder,
    private apiService: ApiServiceService,
    private territoriesService: TerritoriesService,
    private regionsService: RegionsService,
    public toastr: ToastrService,
    private zonesService: ZonesService,
    private spinner: BaThemeSpinner,
    private router: Router,
    private route: ActivatedRoute,
    private events: GlobalEvents,
    private dashboardService  :DashboardService,
    private utility : Utility
  ) {
    this.events.setChangedContentTopText("Update Scheme"); 
    this.randomNumber = Math.floor(1000 + Math.random() * 9000);
    const today = new Date();
    this.minDate = today;
    this.maxDate = today; 
  }

  ngOnInit() { 
    this.spinner.show();
    this.getAllProduct();
    this.getAllZones();
    this.initializeForms();
    this.updateScheme();
  }
  packshotForm = new FormGroup({
    file: new FormControl("", Validators.required),
  });

  get f() {
    return this.packshotForm.controls;
  }

  updateScheme(): void {   
    this.spinner.hide()
    this.route.queryParams.subscribe((item: any) => {
      this.isRewardId = item.reward_id;
      this.userService
        .getUpdateScheme(this.isRewardId)
        .subscribe({
          next : (result : any)=>{    
            result = this.dashboardService.removeSuffix(result);     
              this.resData = result["rewardBonus"]; 
              result["brands"].forEach((brand: any) => {
                this.skuDataItem.push(brand["code"]);
              });
              this.brandsCodes = result["brands"];
              this.selectedCropId = result["cropId"]; 
              this.selectedSkuData = result["skus"];
              this.selectedZones = result["zones"];
              this.selectedRegions = result["regions"];
              this.resultTitle = result["title"]; 
              if (result.startDate) { 
                const dateParts = result.startDate.split(" ")[0].split("-");
                if (dateParts.length === 3) {
                  const day = parseInt(dateParts[0], 10);
                  const month = parseInt(dateParts[1], 10);
                  const year = parseInt(dateParts[2], 10);
                  this.startDateModel = new Date(year, month - 1, day);
                }  
              }
              
              if (result.endDate) { 
                const dateParts = result.endDate.split(" ")[0].split("-");
                if (dateParts.length === 3) {
                  const day = parseInt(dateParts[0], 10);
                  const month = parseInt(dateParts[1], 10);
                  const year = parseInt(dateParts[2], 10);
                  this.endDateModel = new Date(year, month - 1, day); 
                }  
              }
              
              this.packshotFiles = this.utility.decrypt(result["packshot"]);
              const parts = this.packshotFiles.split('/');
                this.imageName = parts[parts.length - 1];   
              if (this.resData) {
                this.rewardBonusResponse();
              } 
              this.selectedRegions.forEach((regionInfo: any) => {
                this.selectedRegionCodes.push(regionInfo.code); 
                  }); 
                
              this.userForm = new FormGroup({
                cropId: new FormControl(result["cropId"], Validators.required),
                point: new FormControl(result["points"], Validators.required),
                brands: new FormControl(result["brands"], Validators.required),
                endDate: new FormControl( '', Validators.required),
                // packshot: new FormControl( result["packshot"], Validators.required),
                skus: new FormControl(result["skus"], Validators.required),
                selectedZones: new FormControl(
                  result["zones"],
                  Validators.required
                ),
                regions: new FormControl(result["regions"], Validators.required),
                startDate: new FormControl( '',
                  Validators.required
                ),
              }); 
              // this.userForm.get('packshot')?.disable();   
              this.selectedZones.forEach((data: any) => {
                this.selectedZoneCodes.push(data.code.toString());
              }); 
              this.skuProduct();
              this.regionDropdown();
              this.getAllCropsData();  
              this.spinner.hide(); 
             
          },
          error : (err  :any)=>{ 
            this.spinner.hide();
            let errorMsg = err.status;
            if(+err.status === 400){ 
              this.toastr.warning(err.error.message);
            }else if(+errorMsg ===  401 || +errorMsg ===  404){
              localStorage.clear();
              this.router.navigate([""]);
              this.toastr.success("Signed Out Successfully");
            }else{
              if(err.error.message === undefined){
              }
            }
          }
        })  
    });

    this.userForm = new FormGroup({
      cropId: new FormControl("", Validators.required),
      point: new FormControl("", Validators.required),
      brands: new FormControl("", Validators.required),
      endDate: new FormControl("", Validators.required),
      // packshot: new FormControl("", Validators.required),
      points: new FormControl("", Validators.required),
      skus: new FormControl("", Validators.required),
      selectedZones: new FormControl("", Validators.required),
      regions: new FormControl("", Validators.required),
      startDate: new FormControl("", Validators.required),
    });  
  }
  
  isPackshotDisabled(): boolean {  
    const packshotControl = this.userForm.get('packshot');
    return packshotControl ? packshotControl.disabled : true;
  }
  
  

  rewardBonusResponse(): void { 
    for (let i = 0; i < Object.keys(this.resData).length; i++) {
      this.dataArray = this.slabForm.get("sectionFormArray") as FormArray;

      this.dataArry = this.fb.group({
        slab: new FormControl({ value: this.resData[i].slab,disabled: this.disabled, }),

        minQuantity: new FormControl({
          value: this.resData[i].minQuantity,
          disabled: this.disabled,
        }),
        maxQuantity: new FormControl({
          value: this.resData[i].maxQuantity,
          disabled: this.disabled,
        }),
        bonus: new FormControl({
          value: this.resData[i].bonus,
          disabled: this.disabled,
        }),
      });
      this.dataArray.push(this.dataArry);
    } 
  }

  imageName: string = '';
  uploadedFiles(event: any): void {

    const file = event.target.files[0];
    
    if (file) {
      const formData = new FormData();
      formData.append("file", file);
      this.imageName = file.name;
      this.userService.uploadProfileImage(formData).subscribe((data) => {
        data = this.utility.decrypt(data);
        this.packshotFiles = data.message; 
      });
    } else {
      // Handle the case when no file is selected, e.g., show an error message.
      this.toastr.warning('Please upload again');
    }
  }
  
  clearPackshot(){
    this.packshotFiles = null;
    this.imageName='Upload Packshot';
  }

  addRowSlabs(): void {
    this.sectionFormArray.push(this.createItem());
  }
  id: any;
  getAllCropsData(): void { 
    this.userService.getAllCrops(this.id).subscribe((cropResponse) => {
      cropResponse = this.dashboardService.removeHashSuffix(cropResponse); 
      this.cropAllData = cropResponse;
      this.cropData = [];
      this.cropAllData.forEach((cropInfo: any) => {  
        let cropObj = {
          id: cropInfo.id,
          name: cropInfo.name,
          description: cropInfo.description,
        };  
        if (+this.selectedCropId == cropObj.id) {
          this.cropData.push(cropObj);
          this.selectedCrops.push(cropObj);
        } 
      }); 
    });
  }

  /**
   * Triggered when a brand is selected in multi select dropdown
   * @param event
   */
  selectedBrand(event: any): void {
    if (!_.includes(this.selectedBrandCodes, event.code)) {
      this.selectedBrandCodes.push(event.code);
      this.skuData = [];
      this.product = [];
      this.productsByBrand();
    }
  }

  /**
   * Triggered when a brand is deselected in multi select dropdown
   * @param event
   */
  deSelectedBrand(event: any): void {
    if (_.includes(this.selectedBrandCodes, event.code)) {
      _.remove(this.selectedBrandCodes, (item) => item === event.code);
      if (this.selectedBrandCodes && this.selectedBrandCodes.length) {
        this.skuData = [];
        this.productsByBrand();
      } else {
        this.skuData = [];
      }
      this.product = [];
    }
  }

  /**
   * Triggered when all brands are selected in multi select dropdown
   * @param event
   */
  selectedAllBrands(event: any): void {
    if (event && event.length) {
      this.selectedBrandCodes = [];
      this.selectedBrandCodes = _.map(event, "code");
      this.productsByBrand();
    }
  }
  /**
   * Triggered when all brands are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllBrands(_event: any): void {
    this.selectedBrandCodes = [];
    this.skuData = [];
    this.product = [];
  }

  productsByBrand(): void { 
    this.userService
      .skuDetailsByBrandCode(this.selectedBrandCodes)
      .subscribe((res: any) => {
        res = this.dashboardService.removeHashSuffix(res); 
        this.skuData = [];
        res.forEach(
          (skuDetails: any) => {
            let skuDetailsObj = {
              id: skuDetails.id,
              code: skuDetails.code,
              name: skuDetails.name,
              description: skuDetails.description,
            };
            this.skuData.push(skuDetailsObj);
          },
          (errResponse: any) => {
            this.toastr.error(AppConstant.BRAND_SKU_ERROR);
          }
        ); 
      });
  }

  /**
   * Triggered when a brand is selected in multi select dropdown
   * @param event
   */
  selectedCrop(event: any): void {
    this.selectedCropId = event.id;
    this.selectedBrands = [];
  }

  /**
   * Triggered when a brand is deselected in multi select dropdown
   * @param _event
   */
  deSelectedCrop(_event: any): void {
    this.brandData = [];
    this.skuData = [];
    this.product = [];
    this.selectedCropId = null;
    this.selectedBrands = [];
  }

  /**
   * Triggered when all brands are selected in multi select dropdown
   * @param event
   */
  selectedAllCrop(event: any): void {
    if (event && event.length) {
      this.selectedCropId = 0;
      this.brandData = [];
    }
  }
  /**
   * Triggered when all brands are deselected in multi select dropdown
   * @param event
   */
  deSelectedAllCrop(): void {
    this.selectedCropId = null;
  }

  getAllProduct(): void { 
    const brandData = this.apiService.getBrands(this.selectedCropId);
    brandData.subscribe((brandDataInfo: any) => { 
      brandDataInfo = this.dashboardService.removeHashSuffix(brandDataInfo);
      this.brandData = [];
      if(brandDataInfo.length){
        brandDataInfo.forEach((brandInfo: any) => {
          let brandObj = {
            code: brandInfo.code,
            name: brandInfo.name,
            id: brandInfo.id,
          };
          this.brandData.push(brandObj);
        });
      }
    }); 
  }


  brandsId: any = {};
  skuCodes = [];
  skuProduct(): void { 
    this.userService
      .skuDetailsByBrandCode(this.skuDataItem)
      .subscribe((res: any) => {
        res = this.dashboardService.removeHashSuffix(res);  
        this.skuData = [];
        this.selectedSKUCodes = [];
        res.forEach(
          (skuDetails: any) => {
            let skuDetailsObj = {
              id: skuDetails.id,
              code: skuDetails.code,
              name: skuDetails.name,
            };
            this.skuData.push(skuDetailsObj);
            this.selectedSKUCodes.push(skuDetailsObj.code);
          },
          (errResponse: any) => {
            this.toastr.error(AppConstant.BRAND_SKU_ERROR);
          }
        ); 
      });
  }


  regionDropdown(): void { 
    const allRegions = this.regionsService.updateRegionByZoneId(
      this.selectedZoneCodes
    );
    allRegions.subscribe((res: any) => {
      res = this.dashboardService.removeHashSuffix(res);
      this.regionsList = res;
      // res.forEach((regionInfo: any) => {
      //   const zoneInfoObj = {
      //     id: regionInfo.id,
      //     regionName: regionInfo.regionName,
      //     code: regionInfo.regionCode,
      //     zoneCode: regionInfo.zoneCode,
      //   };
      //   this.regionsList.push(zoneInfoObj);
      // });
    });
  }

  /**
   * Triggered when a SKU is selected in multi select dropdown
   * @param event
   */
  selectedSKU(event: any): void {
    if (!_.includes(this.selectedSKUCodes, event.code)) {
      this.selectedSKUCodes.push(event.code);
    }
  }

  /**
   * Triggered when a SKU is deselected in multi select dropdown
   * @param event
   */
  deSelectedSKU(event: any): void {
    if (_.includes(this.selectedSKUCodes, event.code)) {
      _.remove(this.selectedSKUCodes, (item) => item === event.code);
    }
  }

  /**
   * Triggered when all SKUs are selected in multi select dropdown
   * @param event
   */
  selectedAllSKUS(event: any): void {
    if (event && event.length) {
      this.selectedSKUCodes = [];
      this.selectedSKUCodes = _.map(event, "code");
    }
  }

  /**
   * Triggered when all SKUs are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllSKUS(_event: any): void {
    this.selectedSKUCodes = [];
  }

  /**
   * Triggered when a zone is selected in multi select dropdown
   * @param event
   */
  selectedZone(event: any): void {
    if (!_.includes(this.selectedZoneCodes, event.code)) {
      this.selectedZoneCodes.push(event.code);
      this.getAllRegionsByZone(this.selectedZoneCodes);
    }
  }

  /**
   * Triggered when a zone is deselected in multi select dropdown
   * @param event
   */
  deSelectedZone(event: any): void {
    _.remove(this.selectedZoneCodes, (code) => code == event.code);
    let tempSelectedRegions = [];
    this.selectedRegions.forEach((region: any) =>
      tempSelectedRegions.push(region)
    );
    this.selectedRegions = this.selectedRegions.filter(
      (region: any) => region.zoneCode != event.code
    );

    let tempRegionList = [];
    this.regionsList.forEach((region: any) => tempRegionList.push(region));
    this.regionsList = this.regionsList.filter(
      (region: any) => region.zoneCode != event.code
    );
  }

  /**
   * Triggered when all zones are selected in multi select dropdown
   * @param event
   */
  selectedAllZones(event: any): void {
    if (event && event.length) {
      this.selectedZoneCodes = _.map(event, "id");
      this.getAllRegionsByZone(this.selectedZoneCodes.join(","));
    }
  }

  /**
   * Triggered when all zones are deselected in multi select dropdown
   * @param _event
   */
  deSelectedAllZones(): void {
    this.selectedZoneCodes = [];
    this.regionsList = [];
    this.selectedRegions = [];
    this.getAllRegionsByZone(this.selectedZoneCodes.join(","));
  }
  /**
   * Method to get all the zones list
   * @param code
   */
  zonesCode: any = [];
  getAllZones(): void { 
    this.zonesService.getAllZonesByPC().subscribe((zonesDetails: any) => {
      zonesDetails = this.dashboardService.removeHashSuffix(zonesDetails);
      this.zonesList = [];
      zonesDetails.forEach((zoneInfo: any) => {
        const zoneInfoObj = {
          code: zoneInfo.code,
          id: zoneInfo.id,
          name: zoneInfo.name,
        };
        this.zonesList.push(zoneInfoObj);
        this.zonesCode.push(zoneInfo.code);
      }); 
    } );
  }

  /**
   * Method to get all the regions list by zone
   * @param code
   */

  getAllRegionsByZone(id: any): void { 
    const allRegions = this.regionsService.getAllRegionsByZoneCode(id);

    this.regionsList = [];
    allRegions.subscribe({
      next: (regionsDetails: any) => {
        this.regionsList = regionsDetails 
      },
      error: () => {
        this.toastr.error(AppConstant.REGION_FETCH_ERROR); 
      },
    });
  }

  /**
   * Triggered when a region is selected in multi select dropdown
   * @param event
   */
  selectedRegion(event: any): void {  
    if (!_.includes(this.selectedRegionCodes, event.code )) {
      this.selectedRegionCodes.push(event.code ); 
    }
  }

  /**
   * Triggered when a region is deselected in multi select dropdown
   * @param event
   */
  deSelectedRegion(event: any): void {
    _.remove(this.selectedRegionCodes, (item) => item === event.code);
  }

  /**
   * Triggered when all regions are selected in multi select dropdown
   * @param event
   */
  selectedAllRegions(event: any): void { 
    if (event && event.length) {
      this.selectedRegionCodes = [];
      this.selectedRegionCodes = _.map(event, "code");
    }
  }

  /**
   * Triggered when all regions are deselected in multi select dropdown
   * @param event
   */
  deSelectedAllRegions(): void {
    this.selectedRegionCodes = [];
  }

  /**
   * called when start date filter is changed
   * @param event
   */
  startDateOptions: IMyOptions = {
    dateFormat: "dd-mm-yyyy",
    showTodayBtn: false,
    inline: false,
    firstDayOfWeek: "su",
    editableDateField: false,
    showClearDateBtn: false,
    openSelectorOnInputClick: true,
    disableUntil: { day: 0, month: 0, year: 0 },
  };

  endDateOptions = {
    dateFormat: "dd-mm-yyyy",
    showTodayBtn: false,
    inline: false,
    firstDayOfWeek: "su",
    editableDateField: false,
    showClearDateBtn: false,
    openSelectorOnInputClick: true,
    disableUntil: {
      day: 0,
      month: 0,
      year: 0,
    },
  };

  onStartDateChanged(event: any): void {
   this.endDateModel = undefined;
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
  }

  /**
   * called when end date filter is changed
   * @param event
   */
  onEndDateChanged(event: any): void {
    if (event && event.value) {
      this.endDate = "";
      this.endDate = moment(event.value, "DD-MM-YYYY").format("DD-MM-YYYY");
    } else {
      this.endDate = "";
    }
  }

  updateSchemeByRewarId(): void {
    this.spinner.show();
    let slectedRegions:any = [];
    this.submitClicked = true;
    if (this.selectedSKUCodes.length !== 0) {
      this.skuCodes = this.selectedSKUCodes;
    }
    let points = "";
    const pointControl = this.userForm.get("point");
    if (
      pointControl &&
      pointControl.value !== null &&
      pointControl.value !== undefined
    ) {
      points = pointControl.value.toString();
    }

    let brands = "";
    const brandsControl = this.userForm.get("brands");
    if (
      brandsControl &&
      brandsControl.value !== null &&
      brandsControl.value !== undefined
    ) {
      brands = brandsControl.value;
    }

    let skus = "";
    const skusControl = this.userForm.get("skus");
    if (
      skusControl &&
      skusControl.value !== null &&
      skusControl.value !== undefined
    ) {
      skus = skusControl.value;
    }
 
    this.selectedRegions.forEach((regionInfo: any) => {
      slectedRegions.push(regionInfo.code); 
        }); 

    let updateScheme: any = {
      cropId: this.selectedCropId,
      id: this.isRewardId,
      points: points,
      title: this.resultTitle,
      brands: brands,
      brandCode: this.skuDataItem ? this.skuDataItem : [],
      regions: this.selectedRegions,
      regionCode: this.selectedRegionCodes,
      skuCode: this.skuCodes ? this.skuCodes : [],
      skus: skus,
      zones: this.selectedZones,
      zoneCode: this.selectedZoneCodes,
      startDate: moment(this.startDateModel).format('DD-MM-YYYY HH:mm:ss'),
      endDate: moment(this.endDateModel).format('DD-MM-YYYY HH:mm:ss'),
      packshot: this.packshotFiles,
      rewardBonus: this.resData,
    }; 
 
    const usersBodyJSON = JSON.stringify(updateScheme);
    const usersBodyJSONData = usersBodyJSON + "#" + this.randomNumber; 

    const updateSchemeDetails = this.rewardService.updateScheme(usersBodyJSONData);

    updateSchemeDetails.subscribe({
      next: (updateSchemeDetails : any) => {  
        this.spinner.hide(); 
        updateSchemeDetails = (this.dashboardService.removeSuffix(updateSchemeDetails));  
        this.toastr.success(updateSchemeDetails.message);
        this.router.navigate(["rewards-points"]);
      },
      error: (errorResponse) => { 
        this.spinner.hide();
        this.toastr.warning("Something went wrong !");
      },
    });
  }

  minKeypress(index: number): boolean {
    this.minQuantityFlag = false;
    const sectionFormArray: any = this.sectionFormArray;
    
    if (sectionFormArray && index > 0) {
      const previousMaxQuantityControl = sectionFormArray
        .at(index - 1)
        .get("maxQuantity");
      const currentMinQuantityControl = sectionFormArray
        .at(index)
        .get("minQuantity");
      if (previousMaxQuantityControl && currentMinQuantityControl) {
        if (
          previousMaxQuantityControl.value <= currentMinQuantityControl.value
        ) {
          this.minQuantityFlag = true;
        }
      }
    } else {
      this.minQuantityFlag = true;
    }
    return true;
  }


  maxKeypress(index: number): boolean {
    this.maxQuantityFlag = false;
    const sectionFormArray: any = this.sectionFormArray;
    if (sectionFormArray) {
      const minQuantityControl = sectionFormArray.at(index).get("minQuantity");
      const maxQuantityControl = sectionFormArray.at(index).get("maxQuantity");
      if (minQuantityControl && maxQuantityControl) {
        if (minQuantityControl.value <= maxQuantityControl.value) {
          this.maxQuantityFlag = true;
        }
      }
    }
    return this.maxQuantityFlag;
  }

  disabledButton(): boolean {
    return !(
      this.selectedCrops &&
      this.selectedBrands &&
      this.selectedBrands.length &&
      this.product &&
      this.product.length &&
      this.packshotFiles &&
      this.packshotFiles.productPicture &&
      this.userForm.valid &&
      this.startDate &&
      this.endDate &&
      this.zonesList &&
      this.zonesList.length &&
      this.selectedRegions &&
      this.selectedRegions.length.valid
    );
  }

  get sectionFormArray() {
    return this.slabForm.get("sectionFormArray") as FormArray;
  }

  initializeForms(): void {
    this.slabForm = this.fb.group({
      sectionFormArray: this.fb.array([]),
    });
    this.maxQuant = this.slabForm.value.sectionFormArray.minQuantity;
  }
  initialValue!: number;
  createItem(): FormGroup {
    return this.fb.group({
      slab: [null, Validators.compose([Validators.required])],
      minQuantity: [null, Validators.compose([Validators.required])],
      maxQuantity: [null, Validators.compose([Validators.required])],
      bonus: [null, Validators.compose([Validators.required])],
    });
  }

  invalidNumber!: boolean;
  maxValueOnChange1(value: number) {
    this.minQuant = value;
  }

  maxValueOnChange(value: number) {
    this.invalidNumber = false;
    this.maxQuant = value;
    if (this.maxQuant <= this.minQuant) {
      this.invalidNumber = true;
    }
  }

  cancelButton() {
    this.router.navigate(["rewards-points"]);
  }

  sanitizeInput(input: string): string { 
    let sanitizedValue = input.replace(/<[^>]*>/g, ''); 
    sanitizedValue = sanitizedValue.replace(/[&^*#$!@()%]/g, '');
  
    return sanitizedValue;
  }

  // Event handler for input field
onInputChange(event: Event): void {
  const inputElement = event.target as HTMLInputElement;
  const inputValue = inputElement.value;
  const sanitizedValue = this.sanitizeInput(inputValue);

  inputElement.value = sanitizedValue;
}

// Event handler for paste event
onPaste(event: ClipboardEvent): void {
  event.preventDefault();
  const pastedText = event.clipboardData!.getData('text/plain');
  const sanitizedValue = this.sanitizeInput(pastedText);
  document.execCommand('insertText', false, sanitizedValue);
}

funRestNumber(event: any) {
  var k = event.charCode;

  if ((k >= 48 && k <= 57) || k == 8) {
    // Allow numbers (48-57) and backspace (8)
    return true;
  } else {
    event.preventDefault();
    this.toastr.warning('Only numbers are allowed');
    return false;
  }
}
}
