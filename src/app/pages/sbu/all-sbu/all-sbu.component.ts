import {
  Component,
  ViewEncapsulation,
  ElementRef,
  OnInit,
  AfterViewInit,
} from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { GlobalEvents } from "../../../helpers/global.events";
import { UserService } from "../../../app-services/user-service";
import { AppConstant } from "../../../constants/app.constant";
import * as _ from "lodash";
import { ngxCsv } from "ngx-csv";
import { CommonModule, DatePipe } from "@angular/common";
import { NgxPaginationModule } from "ngx-pagination";
import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import { DynamicTableComponent } from "src/app/shared/data-table/data-table.component";
import { FilterComponent } from "src/app/shared/filter/filter.component";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
@Component({
    selector: "nga-all-sbu",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["../sbu.scss"],
    templateUrl: "all-sbu.component.html",
    imports: [
        CommonModule,
        ReactiveFormsModule,
        FormsModule,
        NgxPaginationModule,
        DynamicTableComponent,
        FilterComponent,
    ],
    providers: [DatePipe, BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class AllSbuComponent implements OnInit, AfterViewInit {
  sbuData: any = [];
  tableHead: any = [];
  tableColName: any = [];
  showIndex: any = { index: null };
  configurationSettings: any = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: true,
    actionsColumnName: "Actions",
    noDataMessage: "No data found",
    showStatus: true,
    showEdit: false,
    changeStatus: false,
  };

  showOtherFilters: any = {
    showRadioFilters: false,
    showSearch: true,
    add: false,
    showdropdown1Filters: false,
    showdropdown2Filters: false,
    showSearchiconFilters: false,
    showReport: false,
    export: true,
  };
  userButton: any = [];
  searchedValue: string = "";
  isSearch: boolean = false;
  exportData: any = [];

  constructor(
    private routes: ActivatedRoute,
    private spinner: BaThemeSpinner,
    private eRef: ElementRef,
    private router: Router,
    private userService: UserService,
    public toastr: ToastrService,
    private events: GlobalEvents
  ) {}

  /**
   *  Called automatically on init
   */
  ngOnInit() {
    this.setTableHeader();
    window.scrollTo(0, 0);
    this.spinner.hide();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.events.setChangedContentTopText("SBU");
    }, 0);
  }

  setTableHeader(): void {
    this.tableHead = ["SBU Code", "SBU Name", "Profit Center"];
    this.tableColName = ["code", "name", "profit_center"];
    this.sbuData = [];
    this.getAllSbus();
  }

  /**
   * API call for getting all SBUs list
   * @param page number
   */

  getAllSbus(page?: number): void {
    window.scrollTo(0, 0);
    this.spinner.show();
    let data = {
      pageLimit: AppConstant.PER_PAGE_ITEMS,
      currentPage: page ? page - 1 : 0,
      searchedValue: encodeURIComponent(this.searchedValue),
    };

    const getAllSbus = this.userService.getAllSbus(data);

    getAllSbus.subscribe({
      next: (sbusData: any) => {
        this.sbuData = [];
        if (sbusData && sbusData.content && sbusData.content.length) {
          sbusData.content.forEach((sbuInfo: any) => {
            let sbuObj = {
              code: sbuInfo.code ? sbuInfo.code : "",
              name: sbuInfo.name ? sbuInfo.name : "",
              profit_center: _.get(sbuInfo, "profitCenter.name", ""),
              is_active: sbuInfo.active ? sbuInfo.active : false,
            };
            this.sbuData.push(sbuObj);
          });
          this.configurationSettings.totalRecordCount = sbusData.totalElements;
        } else {
          this.configurationSettings.totalRecordCount = 0;
        }
        this.events.setChangedContentTopText(
          "SBU (" + this.configurationSettings.totalRecordCount + ")"
        );
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.SBU_FETCH_ERROR);
        this.spinner.hide();
      },
    });
  }

  /**
   * To handle the page change event
   * @param page
   */
  getSbusPageData(page: number): void {
    this.configurationSettings.currentPage = page;
    this.getAllSbus(this.configurationSettings.currentPage);
  }

  /**
   * To show the results on the basis of searched query
   * @param event
   */
  onSearch(event: any): void {
    if (event.trim()) {
      this.isSearch = true;
      this.searchedValue = event.trim();
      this.getSbusPageData(1);
    } else {
      if (this.isSearch) {
        this.isSearch = false;
        this.searchedValue = "";
        this.getSbusPageData(1);
      }
    }
  }

  /**
   * Method for exporting the data in CSV format
   * @param event
   */
  onExport(): void {
    this.getSbusExportData();
  }

  getSbusExportData(): void {
    window.scrollTo(0, 0);
    this.spinner.show();

    const getSbusExportData = this.userService.getAllSbusExportData();

    getSbusExportData.subscribe({
      next: (exportsData: any) => {
        this.exportData = [];
        if (exportsData && exportsData.length) {
          exportsData.forEach((exportInfo: any) => {
            let exportObj = {
              id: exportInfo.id ? exportInfo.id : "",
              code: exportInfo.code ? exportInfo.code : "",
              name: exportInfo.name ? exportInfo.name : "",
              pc: exportInfo.profitCenter
                ? exportInfo.profitCenter.name
                  ? exportInfo.profitCenter.name
                  : ""
                : "",
              is_active: exportInfo.active ? exportInfo.active : "",
            };
            this.exportData.push(exportObj);
          });
          let options = {
            fieldSeparator: ",",
            quoteStrings: '"',
            decimalseparator: ".",
            showLabels: true,
            headers: [
              "ID",
              "SBU Code",
              "SBU Name",
              "Profit Center",
              "Is Active",
            ],
          };
          new ngxCsv(this.exportData, "sbu", options);
        }
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.EXPORT_ERROR);
        this.spinner.hide();
      },
    });
  }
}
