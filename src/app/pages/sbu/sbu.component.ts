import { Component } from "@angular/core";
import { CommonModule, DatePipe } from "@angular/common";

import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { NgxPaginationModule } from "ngx-pagination";
import { RouterOutlet } from "@angular/router";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaThemeSpinner } from "src/app/theme/services/baThemeSpinner/baThemeSpinner.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
@Component({
    selector: "zones",
    templateUrl: "sbu.html",
    styleUrls: ["sbu.scss"],
    imports: [
        RouterOutlet,
        CommonModule,
        ReactiveFormsModule,
        FormsModule,
        NgxPaginationModule,
    ],
    providers: [DatePipe, BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class SbuComponent {
  constructor() {}
}
