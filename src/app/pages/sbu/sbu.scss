@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

$font-size: 13px;

.sbu-container {
  width: 96%;
  margin: 70px 2.5% 15px 2.5%;
  overflow-y: hidden;
  .sbu-grid-container {
    width: 99%;
    float: left;
    border-radius: $border-radius;
    margin-top: 30px;
    position: relative;
    .sbu-grid-data-parent-container {
      min-height: 485px;
      background: #ffffff;
      overflow-y: auto;
    }
    .sbu-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;

      .sbu-table {
        font-size: 15px;
        min-width: 100%;
        overflow-y: hidden;
      }
    }
  }
}
