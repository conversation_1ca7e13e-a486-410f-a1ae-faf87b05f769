<div (window:resize)="onWindowResizeBrands($event)"></div>
<div class="support-container">
  <div class="support-grid-container">
    <div class="support-grid-data-parent-container">
      <div id="foo" class="support-grid-data-container">
        <div class="area-filter-container">
          <div class="left-column">
            <div class="main-campaign">
              <div class="panel">
                <div class="panel-body">
                  <div class="wizard">
                    <a class="profile-tab area-tab-width" [ngClass]="{ active: isNewTicket }"
                      (click)="ticketTab('PROCESSING')">
                      <span>{{ "New" }}</span>
                      <img [src]="!isNewTicket ? disableNewIcon : newImage" alt="Icon" />
                    </a>
                    <a class="profile-tab area-tab-width" [ngClass]="{ active: isResolvedTicket }"
                      (click)="ticketTab('RESOLVED')">
                      <span>{{ "Resolved" }}</span>
                      <img [src]="!isResolvedTicket ? disableResolvedIcon : resolvedImage" alt="Icon" />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="right-column">
            <div class="search-container">
              <div class="input-group">
                <div class="search-input">
                  <span class="input-group-add">
                    <i class="fa fa-search" aria-hidden="true"></i>
                    <input #searchBox class="input-fields" placeholder="Type to search" target [(ngModel)]="model"
                      (ngModelChange)="onSearch($event)" (keypress)="funRestSearchPrevent($event)" />
                    <span (click)="clearSearch()" *ngIf="model !== ''">
                      <img title="Clear" src="../../../assets/img/icons8-cancel-50.png" alt="Example Image" />
                    </span>
                  </span>
                </div>
                <div class="category">
                  <angular2-multiselect [data]="categoryDataList" [(ngModel)]="category"
                    [settings]="categoryDropdownSettings" (onSelect)="onCategorySelect($event)"
                    (onDeSelect)="onCategoryDeselect($event)" (onDeSelectAll)="onDeselectAllCategory($event)"
                    [ngModelOptions]="{ standalone: true }" style="width: 100%;">
                    <!-- Add inline style to force border visibility -->
                    <div class="c-btn"
                      style="border: 1px solid #FF8033 !important; border-radius: 4px !important; min-height: 36px !important;">
                    </div>
                  </angular2-multiselect>
                </div>
                <div class="date-picker-container">
                  <mat-form-field appearance="outline" class="custom-mat-field">
                    <mat-date-range-input [rangePicker]="rangePicker">
                      <input readonly matStartDate placeholder="Start Date" [(ngModel)]="startDate" #startDateInput
                        (dateChange)="endDateChanged(endDate)" />
                      <input readonly matEndDate placeholder="End Date" [(ngModel)]="endDate" #endDateInput />
                    </mat-date-range-input>
                    <mat-datepicker-toggle matIconSuffix [for]="rangePicker"></mat-datepicker-toggle>
                    <mat-date-range-picker #rangePicker>
                      <mat-date-range-picker-actions>
                        <button mat-button matDateRangePickerCancel class="cancel-button" (click)="
                            clearDateRange(
                              startDateInput.value,
                              endDateInput.value
                            )
                          ">
                          Clear
                        </button>
                        <button mat-raised-button class="submit-button" matDateRangePickerApply (click)="
                            filterSchemeByDate(
                              startDateInput.value,
                              endDateInput.value
                            )
                          ">
                          Apply
                        </button>
                      </mat-date-range-picker-actions>
                    </mat-date-range-picker>
                  </mat-form-field>
                </div>
                <div class="export-button">
                  <button class="add" (click)="onExport($event)" title="Export">
                    <i class="fa fa-share-square-o export-icon"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="support-table">
          <div class="user-ticket-table">
            <dynamic-table [tableHeads]="tableHead" [tableData]="tableData" [tableConfiguration]="configurationSettings"
              [tableColName]="tableColName" (onRowEdit)="openDialogForm($event)" (onStatusChange)="ChangeStatus($event)"
              (pageChange)="getsupportPageData($event)" [showIndex]="showIndex">
            </dynamic-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #supoortDialog>
  <div>
    <form [formGroup]="formGroup!" style="margin-right: 10px">
      <div class="support-dialog-scrollable">
        <div class="main-support-container">
          <div class="heading-container">
            <h3>{{ "Support" }}</h3>
          </div>
          <div class="form-input1-container">
            <span>
              <label for="ticketNumber">{{ "Ticket Number" }}</label>
              <input type="text" class="input-field disabled-field" id="ticketNumber" formControlName="ticketNumber"
                autocomplete="off" readonly />
            </span>
            <span>
              <label for="leaderName">{{ "Leader Name" }}</label>
              <input type="text" class="input-field disabled-field" id="leaderName" formControlName="leaderName"
                autocomplete="off" readonly />
            </span>

            <span>
              <label for="businessUnit">{{ "Category" }}<i class="required">&nbsp;*</i></label>
              <angular2-multiselect [data]="dialogCategoryDataList" [(ngModel)]="dialogCategory"
                [settings]="dialogCategoryDropdownSettings" (onSelect)="onDialogCategorySelect($event)"
                (onDeSelect)="onDialogCategoryDeselect($event)" (onDeSelectAll)="onDialogDeselectAllCategory($event)"
                [ngModelOptions]="{ standalone: true }">
              </angular2-multiselect>
            </span>

            <span style="position: relative; display: inline-block;">
              <label for="queryTitle">{{ "Query Title" }}</label>
              <input type="text" class="input-field disabled-field" id="queryTitle" formControlName="queryTitle"
                autocomplete="off" readonly (mouseover)="showTooltipIfTruncated($event)"
                style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" />
            </span>
            <span>
              <label for="createdDate">{{ "Created Date" }}</label>
              <input type="text" class="input-field disabled-field" id="leaderName" formControlName="createdDate"
                autocomplete="off" readonly />
            </span>
            <span>
              <label for="businessUnit">{{ "Status" }}<i class="required">&nbsp;*</i></label>
              <angular2-multiselect [data]="statusDataList | customCase : ['An']" [(ngModel)]="status"
                [settings]="statusDropdownSettings" (onSelect)="onStatusSelect($event)"
                (onDeSelect)="onStatusDeselect($event)" (onDeSelectAll)="onDeselectAllStatus($event)"
                [ngModelOptions]="{ standalone: true }">
              </angular2-multiselect>
            </span>
          </div>
          <span class="attachment-wrapper">
            <label for="ticketDescription" style="font-weight: bold; margin-top: 5px;">Attachments</label>
            <div class="attachment-container" *ngIf="supportAttachments.length">
              <div class="single-attachment" *ngFor="let attachment of supportAttachments"
                (click)="downloadFileAttachment(attachment)">
                <img [src]="getFileIcon(attachment?.fileName)" [alt]="attachment?.fileName" class="file-icon" />

                <div class="file-info">
                  <div class="file-name" [title]="attachment?.fileName">
                    {{ attachment?.fileName }}
                  </div>
                  <div class="file-size">
                    {{ attachment?.fileSize + " (Kb)" }}
                  </div>
                </div>

                <img src="../../../../assets/img/download _image.png" alt="Download" class="download-icon" />
              </div>
            </div>
            <div class="attachment-container" *ngIf="!supportAttachments.length">
              <span>No Attachments Available</span>
            </div>
          </span>
          <div>
            <label for="ticketDescription">{{ "Ticket Description" }}</label>
            <div class="mat-form">
              <div name="MyForm" ng-submit="MyForm.$valid" novalidate style="margin-bottom: 10px">
                <textarea style="cursor: not-allowed" matInput rows="3" class="text-error" ng-model="description"
                  formControlName="description" type="text" name="description" required #description
                  placeholder="Description" autocomplete="off"></textarea>
                <mat-hint> {{ description.value.length }}/1000</mat-hint>
              </div>
            </div>
          </div>
          <div>
            <label for="ticketNumber">{{ "Add Comment" }}<i class="required">&nbsp;*</i></label>
            <div class="mat-form">
              <div name="MyForm" ng-submit="MyForm.$valid" novalidate>
                <textarea matInput rows="3" maxlength="1000" minlength="6" class="text-error" formControlName="comments"
                  type="text" name="Comment" (keypress)="functionForName($event)" required #comment
                  placeholder="Leave your comment..." autocomplete="off"></textarea>
                <mat-hint> {{ comment.value.length }}/1000</mat-hint>
              </div>
            </div>
          </div>
          <div class="button-container">
            <button type="button" class="btn-cancel" (click)="onCloseForm()">
              Cancel
            </button>
            <button type="button" class="btn-submit" (click)="onSubmit()" [disabled]="
              !selectedDialogCategory.length ||
              !selectedStatus.length ||
              formGroup.get('comments')?.invalid
            " [ngClass]="{
              'disable-submit':
                !selectedDialogCategory.length ||
                !selectedStatus.length ||
                formGroup.get('comments')?.invalid
            }">
              Submit
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</ng-template>