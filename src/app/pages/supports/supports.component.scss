@import "../../theme/sass/_auth.scss";
@import "../../../styles.scss";
@import "../../theme/sass/mixins.scss";
@import "../../theme/sass/conf/variables.scss";


// Original supports component styles
.container {
  mat-button-toggle-group {
    flex-wrap: wrap;
  }
  margin: 80px 0px 0px 3px;
  padding: 20px 0px 150px 0px;
  background-color: #fff;
  width: 100%;
  .cdk-focused {
    outline: none;
    border: none;
  }

  .toggle-group {
    .mat-button-toggle {
      vertical-align: middle !important;
      height: 50px;
    }
  }
  .mat-button-toggle-group {
    display: inline-flex;
    flex-direction: row;
    border-radius: 2px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    outline: none;
    flex-wrap: wrap;
    margin: 0px 0px 0px 31px;
    .New_toggle {
      flex-wrap: wrap;
    }
  }

  .mat-row:nth-child(n) {
    background-color: #ffffff !important;
  }

  .mat-row:not(:nth-child(2n)) {
    background-color: #eeeff3 !important;
  }

  .mat-button-toggle {
    color: black;
    border: 1px solid #666666;
    font-weight: 600;
    width: 250px;
    height: 40px;
    text-align: center;
    padding: 10px;
    background-color: white;
    cursor: pointer;
  }

  .mat-button-toggle-checked {
    background-color: #1a3661 !important;
    color: white !important;
    height: 40px;
    width: 250px !important;
  }

  .mat-button-toggle:hover {
    background-color: #e0e0e0;
  }

  .mat-button-toggle-group .mat-button-toggle {
    border-radius: 4px;
    margin: 2px;
  }
}

::ng-deep .date-picker-container .mat-datepicker-toggle .mat-mdc-icon-button {
  display: flex;
  justify-content: center;
  align-items: normal;
  padding: 5px 0px 0px 0px !important;
}