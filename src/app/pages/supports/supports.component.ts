import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { RouterModule } from '@angular/router';
export enum ToggleEnum {
  Option1,
  Option2,
}

@Component({
  selector: 'app-supports',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonToggleModule,
    RouterModule
],
  templateUrl: './supports.component.html',
  styleUrls: ['./supports.component.scss'],
})
export class SupportsComponent implements OnInit {
  constructor() {}

  ngOnInit() {}
}
