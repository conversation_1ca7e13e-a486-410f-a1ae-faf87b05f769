::ng-deep {
  clear: both;
  float: left;
  width: 100%;
  height: 100%;
  background: white;
}

.policy-container {
  text-align: justify;
  margin-top: 20px;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  
  .page-content {
    color: black;
    text-align: left;
    margin-left: 9%;
    margin-right: 9%;
    text-align: justify;
    content-visibility: auto !important;

    .content-heading {
      font-weight: bold;
      text-decoration: underline;
      justify-content: center;
      text-transform: uppercase;
      margin-bottom: 10px;
    }

    p {
      margin-bottom: 15px;
      line-height: 1.5;
    }

    .terms-list {
      list-style-type: disc;
      margin-left: 20px;
      margin-bottom: 20px;
      
      li {
        margin-bottom: 10px;
        line-height: 1.5;
      }
    }

    a {
      color: #0066cc;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .policy-container {
    .page-content {
      margin-left: 5%;
      margin-right: 5%;
    }
  }
}

@media screen and (max-width: 480px) {
  .policy-container {
    .page-content {
      margin-left: 3%;
      margin-right: 3%;
    }
  }
}

.last-updated {
  margin: 15px 0;
  font-style: italic;
  color: #666;
}

.navigation-links {
  margin-bottom: 20px;
  
  a {
    color: #0066cc;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  
  .print-button {
    height: 40px;
    min-width: 120px;
    border: none;
    border-radius: 5px;
    background-color: #FF8033;
    color: #fff;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    
    i {
      margin-right: 8px;
    }
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
  }
}

@media screen and (max-width: 768px) {
  .action-buttons {
    .print-button {
      min-width: 100px;
      font-size: 14px;
    }
  }
}

@media screen and (max-width: 480px) {
  .action-buttons {
    .print-button {
      min-width: 90px;
      font-size: 12px;
    }
  }
}
