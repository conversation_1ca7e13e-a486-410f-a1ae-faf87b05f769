import { Component, OnInit } from "@angular/core";
import { RouterLink } from "@angular/router";
import { CommonModule } from "@angular/common";
import { BaThemeSpinner } from "../../theme/services";

@Component({
  selector: "app-terms-condition",
  templateUrl: "./terms-condition.component.html",
  styleUrls: ["./terms-condition.component.scss"],
  standalone: true,
  imports: [CommonModule, RouterLink],
  providers: [BaThemeSpinner]
})
export class TermsConditionComponent implements OnInit {
  constructor(private spinner: BaThemeSpinner) {
    this.spinner.hide();
  }

  ngOnInit(): void {
    // Scroll to top when the page loads
    window.scrollTo(0, 0);
  }

  currentLanguage = 'es'; // Default to Spanish

  toggleLanguage(): void {
    this.currentLanguage = this.currentLanguage === 'es' ? 'en' : 'es';
    // Implement logic to switch content based on language
  }

  printTerms(): void {
    window.print();
  }
}
