<div class="territories-container">
  <div class="territories-grid-container">
    <nga-app-filter
      (onSearchValue)="onSearch($event)"
      [configurations]="showOtherFilters"
      [addButton]="userButton"
      (onExport)="onExport()"
    >
    </nga-app-filter>

    <div class="territories-grid-data-parent-container">
      <div id="foo" class="territories-grid-data-container">
        <div class="territories-table">
          <dynamic-table
            [tableHeads]="tableHead"
            [tableData]="territories"
            [tableConfiguration]="configurationSettings"
            [tableColName]="tableColName"
            (onRowEdit)="rowDetails($event)"
            (pageChange)="getPageData($event)"
            (onStatusChange)="ChangeStatus($event)"
            [showIndex]="showIndex"
          >
          </dynamic-table>
        </div>
      </div>
    </div>
  </div>
</div>
