import { Component, ViewEncapsulation } from "@angular/core";
import { Router } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { GlobalEvents } from "../../../helpers/global.events";
import { TerritoriesService } from "../../../app-services/territories-service";
import { AuthenticationHelper } from "../../../helpers/authentication";
import { AppConstant } from "../../../constants/app.constant";
import { ngxCsv } from "ngx-csv";
import { DynamicTableComponent } from "../../../shared/data-table/data-table.component";
import { FilterComponent } from "../../../shared/filter/filter.component";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { DatePipe } from "@angular/common";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { DashboardService } from "src/app/app-services/dashboard.service";
@Component({
    selector: "nga-all-territories",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["../territories.component.scss"],
    templateUrl: "all-territories.component.html",
    imports: [FilterComponent, DynamicTableComponent],
    providers: [DatePipe, BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class AllTerritoriesComponent {
  tableHead: any = [];
  tableColName: any = [];
  territories: any = [];
  isAdmin: boolean = false;
  PCCode!: string;
  configurationSettings: any = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    actionsColumnName: "Active Area",
    noDataMessage: "No data found",
    showStatus: true,
    showEdit: false,
    changeStatus: false,
  };
  showIndex: any = { index: null };

  showOtherFilters: any = {
    showRadioFilters: false,
    showSearch: true,
    add: false,
    showdropdown1Filters: true,
    showdropdown1Title: "",
    showdropdown2Filters: false,
    showSearchiconFilters: false,
    showReport: false,
    export: true,
  };
  userButton: any = [];
  searchedValue: string = "";
  isSearch: boolean = false;
  exportData: any = [];

  constructor(
    private router: Router,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private events: GlobalEvents,
    private territoriesService: TerritoriesService,
    private dashboardService : DashboardService
  ) {
    this.isAdmin = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;

    this.showOtherFilters.showdropdown1Title = "territories";
  }
  ngOnInit() {
    this.setTableHeader();
    this.commonAddButton();
    window.scrollTo(0, 0);
  }
  ngAfterViewInit() {
    this.events.onPCSelect.subscribe((item) => {
      this.configurationSettings.currentPage = 0;
      if (item && item.tab == "territories") {
        if (item && item.pcCode != "all") {
          this.PCCode = item.pcCode;
          this.allTerritories();
        } else {
          this.PCCode = "";
          this.allTerritories();
        }
      }
    });
  }

  commonAddButton() {
    this.userButton = [
      {
        path: "/territories/add-territory",
        title: " Add Territory",
      },
    ];
  }

  /**
   * Method to set the headers of the table
   */
  setTableHeader(): void {
    this.tableHead = ["Territory Code", "Territory Name", "Zone Name"];
    this.tableColName = ["code", "name", "zone"];
    const pc = String(localStorage.getItem("profitCenter"));
    if (this.isAdmin) {
      this.PCCode = "";
    } else {
      if (pc) {
        this.PCCode = pc;
      }
    }
    this.allTerritories();
    this.spinner.hide();
  }

  /**
   * API call for getting all territories list
   * @param page number
   */

  allTerritories(page?: number): void {
    this.spinner.show();
    let data = {
      pageLimit: AppConstant.PER_PAGE_ITEMS,
      currentPage: page ? page - 1 : 0,
      searchedValue: encodeURIComponent(this.searchedValue),
      profitCenterCode: this.PCCode ? this.PCCode : "",
    };

    const getAllTerritoriesAdmin =
      this.territoriesService.getAllTerritoriesAdmin(data);

    getAllTerritoriesAdmin.subscribe({
      next: (territories: any) => {
        territories = this.dashboardService.removeSuffix(territories);
        this.territories = [];
        if (territories && territories.content && territories.content.length) {
          territories.content.forEach((res: any) => {
            let data = {
              id: res.id,
              code: res.code,
              name: res.territoryName,
              profit_center_name: res.profitCenterName,
              zone: res.zoneName,
              is_active: res.active,
            };
            this.territories.push(data);
          });
          this.configurationSettings.totalRecordCount =
            territories.totalElements;
        } else {
          this.configurationSettings.totalRecordCount = 0;
        }

        this.events.setChangedContentTopText(
          "Territories (" + this.configurationSettings.totalRecordCount + ")"
        );
        this.spinner.hide();
      },
      error: (errorResponse  : any) => { 
        let errorMsg = errorResponse.status;
        if (+errorMsg ===  401 || +errorMsg ===  404) {  
          localStorage.clear();
          this.router.navigate([""]);
          this.toastr.success("Signed Out Successfully");
        } else{
          this.toastr.error(AppConstant.TERRITORY_FETCHING_ERROR);
        } 
        this.spinner.hide();
      },
    });
  }

  /**
   * To handle the page change event
   * @param page
   */
  getPageData(page: number): void {
    this.configurationSettings.currentPage = page;
    this.allTerritories(this.configurationSettings.currentPage);
  }

  rowDetails(event: any): void {
    this.router.navigate(["territories/edit-customer"], {
      queryParams: { id: event.customerID },
    });
  }

  /**
   * To show the results on the basis of searched query
   * @param event
   */
  onSearch(event: any): void {
    if (event.trim()) {
      this.isSearch = true;
      this.searchedValue = event.trim();
      this.getPageData(1);
    } else {
      if (this.isSearch) {
        this.isSearch = false;
        this.searchedValue = "";
        this.getPageData(1);
      }
    }
  }

  ChangeStatus(event: any) {}

  /**
   * Method for exporting the data in CSV format
   * @param event
   */
  onExport(): void {
    this.getTerritoriesExportData();
  }

  getTerritoriesExportData(): void {
    window.scrollTo(0, 0);
    this.spinner.show();
    let data = {
      searchedValue: encodeURIComponent(this.searchedValue),
      profitCenterCode: this.PCCode ? this.PCCode : "",
    };

    const getAllTerritoriesExportData =
      this.territoriesService.getAllTerritoriesExportData(data);

    getAllTerritoriesExportData.subscribe({
      next: (exportsData: any) => {
        exportsData = this.dashboardService.removeSuffix(exportsData);
        this.exportData = [];
        if (exportsData && exportsData.content.length) {
          exportsData.content.forEach((exportInfo: any) => {
            let exportObj = { 
              territory_code: exportInfo.code
                ? exportInfo.code
                : "---",
              territory_name: exportInfo.territoryName
                ? exportInfo.territoryName
                : "---", 
              zone_name: exportInfo.zoneName ? exportInfo.zoneName : "---",
              is_active: exportInfo.active ?  "Yes" : "No",
            };
            this.exportData.push(exportObj);
          });
          let options = {
            fieldSeparator: ",",
            quoteStrings: '"',
            decimalseparator: ".",
            showLabels: true,
            headers: [ 
              "Territory Code",
              "Territory Name", 
              "Zone Name",
              "Is Active",
            ],
          };
          new ngxCsv(this.exportData, "Territory Details", options);
        }else{
          this.toastr.warning("No data available to export!");
        }
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.EXPORT_ERROR);
        this.spinner.hide();
      },
    });
  }
}
