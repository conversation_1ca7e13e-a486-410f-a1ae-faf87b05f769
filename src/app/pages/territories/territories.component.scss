@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

$font-size: 13px;

.territories-container {
  width: 96%;
  margin: 45px 2.5% 15px 2.5%;
  overflow-y: hidden;
  .territories-grid-container {
    width: 99%;
    float: left;
    border-radius: $border-radius;
    margin-top: 30px;
    position: relative;
    .territories-grid-data-parent-container {
      min-height: 485px;
      background: #ffffff;
      overflow-y: auto;
    }
    .territories-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      // padding: 10px 15px 25px;

      .territories-table {
        font-size: 15px;
        min-width: 100%;
        overflow-y: hidden;
        border-radius: $table-border-radius;
      }
    }
  }
}

.confirmUserActiveContainer {
  @include confirmDialogueActiveInActive();
}

.modal-backdrop.fade {
  opacity: 0.6;
}

.confirmUserActiveContainer .fade {
  opacity: 1 !important;
}

.disable-submit {
  opacity: 0.8;
  cursor: not-allowed;
  &:hover {
    opacity: 0.8;
    cursor: not-allowed;
  }
}
