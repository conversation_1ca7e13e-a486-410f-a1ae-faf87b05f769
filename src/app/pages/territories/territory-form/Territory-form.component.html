<div class="territories-form-container">
  <div class="territories-form-content">
    <form
      [formGroup]="territoriesForm"
      (ngSubmit)="onSubmit(territoriesForm.value)"
    >
      <div class="col-md-12 row territories-form">
        <div class="width-100 float-left">
          <h2 class="territories-form-info-title">Territory Information</h2>
          <div class="territories-form-information-section">
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Profit Center<i class="required">&nbsp;*</i></label
                >
                <select
                  *ngIf="isAdmin"
                  class="form-control"
                  formControlName="profit_center"
                  [(ngModel)]="profitCenterSelected"
                  (change)="selectProfitCenter($event)"
                >
                  <option value="" hidden>Select Profit Center</option>
                  <option value="DO1301">AF</option>
                  <option value="DO1401">SWAL</option>
                </select>
                <input
                  *ngIf="!isAdmin"
                  type="text"
                  class="form-control"
                  formControlName="profit_center"
                  [value]="profitCenterSelectedName"
                  readonly
                />
              </div>
            </div>
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Select Zone<i class="required">&nbsp;*</i></label
                >
                <select
                  class="form-control"
                  formControlName="territory"
                  [(ngModel)]="territorySelected"
                >
                  <option value="" hidden>Select Zone</option>
                </select>
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !territoriesForm.get('territory')?.valid &&
                    territoriesForm.get('territory')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >Territory name is required.</span
                >
              </div>
            </div>

            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Territory Code<i class="required">&nbsp;*</i></label
                >
                <input
                  formControlName="code"
                  type="text"
                  class="form-control"
                  [readonly]="territoriesId"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !territoriesForm.get('code')?.valid &&
                    territoriesForm.get('code')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >territories code is required.</span
                >
              </div>
            </div>
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Name<i class="required">&nbsp;*</i></label
                >
                <input
                  formControlName="name"
                  type="text"
                  class="form-control"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !territoriesForm.get('name')?.valid &&
                    territoriesForm.get('name')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >territories name is required.</span
                >
              </div>
            </div>
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style">Status</label>
                <div>
                  <span class="territories-form-status">
                    <input
                      type="radio"
                      class="btn"
                      [value]="1"
                      name="status"
                      id="active"
                      formControlName="status"
                    />
                    <label for="active" class="territories-form-label"
                      >Active</label
                    >
                  </span>

                  <span class="territories-form-status">
                    <input
                      type="radio"
                      class="btn"
                      [value]="0"
                      name="status"
                      id="inactive"
                      formControlName="status"
                    />
                    <label for="inactive" class="territories-form-label"
                      >Inactive</label
                    >
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="col-md-12 form-group input-button submit-button padding-left-right"
        >
          <button
            type="button"
            class="btn-cancel-style pull-left"
            (click)="location.back()"
          >
            CANCEL
          </button>
          <button
            [ngClass]="{ 'disable-submit': !territoriesForm.valid }"
            [disabled]="!territoriesForm.valid"
            type="submit"
            class="btn-style"
          >
            {{ territoriesId ? "UPDATE" : "ADD TERRITORY" }}
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
