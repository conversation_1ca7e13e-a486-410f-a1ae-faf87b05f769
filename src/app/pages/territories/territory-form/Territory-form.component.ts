import { Component } from "@angular/core";
import { Location, NgIf, NgClass, DatePipe } from "@angular/common";
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormsModule,
  ReactiveFormsModule,
} from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { Router, ActivatedRoute } from "@angular/router";
import { GlobalEvents } from "../../../helpers/global.events";
import { TerritoriesService } from "../../../app-services/territories-service";
import { AppConstant } from "../../../constants/app.constant";
import { AuthenticationHelper } from "../../../helpers/authentication";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";

@Component({
    selector: "nga-add-territory",
    styleUrls: ["Territory-form.component.scss"],
    templateUrl: "Territory-form.component.html",
    imports: [FormsModule, ReactiveFormsModule, NgIf, NgClass],
    providers: [DatePipe, BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class TerritoryFormComponent {
  territoriesForm!: FormGroup;
  territoriesId: any;
  territoriesData!: {};
  terrPCArr: any = [];
  territories: any = [];
  territorySelected: any = "";
  profitCenterSelected: any = "";
  profitCenterSelectedName: any = "";
  isAdmin: boolean = false;
  constructor(
    private fb: FormBuilder,
    public location: Location,
    private router: Router,
    public events: GlobalEvents,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private activeRoute: ActivatedRoute,
    private territoriesService: TerritoriesService
  ) {
    this.territoriesId = this.activeRoute.snapshot.queryParams["id"];
    this.isAdmin = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;
  }

  ngOnInit() {
    this.setUserForm();
    if (this.isAdmin) {
    }
    if (!this.isAdmin) {
      this.territoryProfitCenter();
    }
    if (this.territoriesId) {
      this.territoriesDetails();
    }
    window.scrollTo(0, 0);
    this.spinner.hide();
  }

  territoryProfitCenter(): void {
    let data = {
      userID: AuthenticationHelper.getUserID(),
    };
    this.territoriesService
      .getTerrProfitCenter(data)
      .subscribe((terrPCData: any) => {
        this.terrPCArr = [];
        terrPCData.forEach((res: any) => {
          const terrPCObj = {
            profitCenterCode: res.profitCenterCode,
            profitCenterName: res.profitCenterName,
            territoryCode: res.territoryCode,
            territoryName: res.territoryName,
          };
          this.terrPCArr.push(terrPCObj);
        });
        if (this.terrPCArr.length === 1) {
          this.territorySelected = this.terrPCArr[0].territoryCode;
          this.profitCenterSelected = this.terrPCArr[0].profitCenterCode;
          this.profitCenterSelectedName = this.terrPCArr[0].profitCenterName;
        }
      });
  }

  territoriesDetails(): void {
    let data = {
      custID: this.territoriesId,
    };

    const territoriesDetailByUserID =
      this.territoriesService.territoriesDetailByUserID(data);

    territoriesDetailByUserID.subscribe({
      next: (territoriesDataByID: any) => {
        this.territoriesData = {
          status: territoriesDataByID.active === true ? 1 : 0,
          code: territoriesDataByID.code,
          custID: territoriesDataByID.territoriesId,
          name: territoriesDataByID.name,
          profitCenterCode: territoriesDataByID.profitCenterCode,
          profitCenterName: territoriesDataByID.profitCenterName,
          territoryCode: territoriesDataByID.territoryCode,
          territoryName: territoriesDataByID.territoryName,
        };

        if (this.isAdmin) {
          this.territorySelected = this.territoriesData;
          this.profitCenterSelected = this.territoriesData;
          this.profitCenterSelectedName = this.territoriesData;
          this.selectProfitCenter(this.profitCenterSelected);
        }

        this.setUserForm(this.territoriesData);
        this.spinner.hide();
      },
      error: () => {
        this.spinner.hide();
      },
    });
  }
  ngAfterViewInit() {
    setTimeout(() => {
      this.events.setChangedContentTopText(
        this.territoriesId ? "Edit Territory" : "Add Territory"
      );
    }, 0);
  }

  setUserForm(values?: any): void {
    this.territoriesForm = this.fb.group({
      profit_center: [this.profitCenterSelectedName],
      territory: [
        this.territorySelected,
        Validators.compose([Validators.required]),
      ],
      code: [
        values ? values.code : "",
        Validators.compose([Validators.required]),
      ],
      name: [
        values ? values.name : "",
        Validators.compose([Validators.required]),
      ],
      status: [
        values ? values.status : 1,
        Validators.compose([Validators.required]),
      ],
    });
  }

  selectProfitCenter(val: any): void {
    this.profitCenterSelected = val.target.value;
    this.territoriesService
      .getTerritoriesByProfitCenter(this.profitCenterSelected)
      .subscribe((res: any) => {
        this.terrPCArr = [];
        res.forEach((territory: any) => {
          let data = {
            territoryName: territory.name,
            territoryCode: territory.code,
          };
          this.terrPCArr.push(data);
        });
      });
  }
  onSubmit(values: any): void {
    this.spinner.show();

    if (this.territoriesId) {
      let body = {
        code: values.code.trim(),
        active: values.status === 1 ? true : false,
        name: values.name.trim(),
        profitCenterCode: this.profitCenterSelected,
        territoryCode: values.territory,
        territoriesId: this.territoriesId,
      };
      const territoriesDetails = this.territoriesService.addCustDetails(body);

      territoriesDetails.subscribe({
        next: (data: any) => {
          this.toastr.success(AppConstant.CUST_CREATION_SUCCESS);
          this.router.navigate(["territories"]);
          this.spinner.hide();
        },
        error: (errorRes: any) => {
          this.toastr.error(AppConstant.CUST_CREATION_ERROR);
          this.spinner.hide();
        },
      });
    } else {
      let body = {
        code: values.code.trim(),
        active: values.status === 1 ? true : false,
        name: values.name.trim(),
        profitCenterCode: this.profitCenterSelected,
        territoryCode: values.territory,
      };
      const territoriesDetails = this.territoriesService.addCustDetailss(body);

      territoriesDetails.subscribe({
        next: (data: any) => {
          this.router.navigate(["territories"]);
          this.toastr.success(AppConstant.CUST_ADDITION_SUCCESS);
          this.spinner.hide();
        },
        error: (errRes: any) => {
          if (errRes && errRes.error) {
            this.toastr.error(errRes.error.message);
          } else {
            this.toastr.error(AppConstant.CUST_ADDITION_ERROR);
          }
          this.spinner.hide();
        },
      });
    }
  }
}
