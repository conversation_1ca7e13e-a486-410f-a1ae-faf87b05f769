import {
  Component,
  Inject,
  ViewEncapsulation,
} from "@angular/core";
import { FormGroup, FormBuilder, Validators, FormsModule, ReactiveFormsModule } from "@angular/forms";
import { Router } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { EmailValidator } from "../../theme/validators/email.validator";
import { BlankSpaceValidator } from "../../theme/validators/blank.validator";
import { EqualPasswordsValidator } from "../../theme/validators/equal-passwords.validator";
import { GlobalEvents } from "../../helpers/global.events";
import { UserService } from "../../app-services/user-service";
import { AppConstant } from "../../constants/app.constant";
import { NgClass, NgIf } from "@angular/common";
import { DashboardService } from "src/app/app-services/dashboard.service";
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { AuthenticationHelper } from "src/app/helpers/authentication";
import { Utility } from 'src/app/shared/utility/utility';

@Component({
  selector: "profile",
  encapsulation: ViewEncapsulation.None,
  styleUrls: ["./profile.scss"],
  templateUrl: "./profile.html",
  imports: [NgClass, NgIf, FormsModule, ReactiveFormsModule]
})
export class Profile {
  [x: string]: any;
  form!: FormGroup;
  // changePasswordForm: FormGroup;
  getUserData: any;
  imageUrl!: string;
  showError: boolean = false;
  settingComponent: boolean = true;
  passwordComponent: boolean = false;
  notificationComponent: boolean = false;
  accreditionComponent: boolean = false;
  userId: any;
  currentHide = true;
  confirmPassword = true;
  newPassword = true;
  public showPassword: boolean = false;

  phoneNumberMask = [
    /[1-9]/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
  ];

  formGroup!: FormGroup;
  randomNumber: any;

  constructor(
    private router: Router,
    private fb: FormBuilder,
    public events: GlobalEvents,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private userService: UserService,
    private dashboardService: DashboardService,
    public dialogRef: MatDialogRef<Profile>,
    private utility: Utility,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.randomNumber = Math.floor(1000 + Math.random() * 9000);
    // Try to get userId from multiple possible localStorage keys
    this.userId = localStorage.getItem("userID");
    
    // Check if userId is null or undefined
    if (!this.userId) {
      this.toastr.error("User ID not found. Please log in again.");
      this.dialogRef.close();
      this.router.navigate([""]);
    }
    
    this.formGroup = this.fb.group({
      phoneNumber: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(10)]]
    });
  }

  /**
   *  Called automatically on init
   */
  ngOnInit() {
    this.spinner.show();
    window.scrollTo(0, 0);
    this.form = this.fb.group({
      firstName: ['', [Validators.required, BlankSpaceValidator.validate]],
      lastName: ['', [Validators.required, BlankSpaceValidator.validate]],
      email: ['', [Validators.required, EmailValidator.validate]],
      phoneNumber: ['', [Validators.required, BlankSpaceValidator.validate, Validators.pattern('^[0-9]{10}$')]],
      profile_img: [''],
      chooseImage: [''],
    });
    
    // Check if userId exists before calling getUserDetails
    if (this.userId) {
      this.getUserDetails();
    } else {
      this.spinner.hide();
      this.toastr.error("User ID not found. Please log in again.");
      this.dialogRef.close();
      this.router.navigate([""]);
    }
  }

  /**
   * To set user form values
   */
  userBasicInfo() {
    this.form = this.fb.group({
      firstName: this.fb.control(
        "",
        Validators.compose([Validators.required, BlankSpaceValidator.validate])
      ),
      lastName: this.fb.control(
        "",
        Validators.compose([Validators.required, BlankSpaceValidator.validate])
      ),
      email: this.fb.control(
        "",
        Validators.compose([Validators.required, EmailValidator.validate])
      ),
      phoneNumber: this.fb.control(
        "",
        Validators.compose([Validators.required, BlankSpaceValidator.validate, Validators.pattern("^[0-9]{10}$")])
      ),
      profile_img: this.fb.control(""),
      chooseImage: this.fb.control(""),
    });

    // this.changePasswordForm = this.fb.group({
    //   password: this.fb.control(
    //     "",
    //     Validators.compose([
    //       Validators.required,
    //       Validators.minLength(6),
    //       Validators.maxLength(16),
    //     ])
    //   ),
    //   passwords: this.fb.group(
    //     {
    //       newPassword: this.fb.control(
    //         "",
    //         Validators.compose([Validators.required, Validators.minLength(6)])
    //       ),
    //       confirmPassword: this.fb.control(
    //         "",
    //         Validators.compose([Validators.required, Validators.minLength(6)])
    //       ),
    //     },
    //     {
    //       validators: [EqualPasswordsValidator.validate] // Fixed 'validator' to 'validators'
    //     }
    //   ),
    // });

    // this.changePasswordForm.valueChanges.subscribe((data) => {
    //   if (
    //     data.password &&
    //     data.password.length > 0 &&
    //     data.passwords?.newPassword &&
    //     data.passwords.newPassword.length > 0
    //   ) {
    //     this.showError = data.password === data.passwords.newPassword;
    //   }
    // });
  }


  myFunction() {
    this.currentHide = !this.currentHide;
  }

  confirmNewPasswordFunction() {
    this.newPassword = !this.newPassword;
  }

  confirmPasswordFunction() {
    this.confirmPassword = !this.confirmPassword;
  }


  /**
   * To set user user data initially on page load.
   */
  setUserData(userData: any) {
  // Outputs the current value of firstName field
    if (userData.firstName) {
      this.form.controls["firstName"].setValue(userData.firstName);
    }
    if (userData.lastName) {
      this.form.controls["lastName"].setValue(userData.lastName);
    }
    if (userData.email) {
      this.form.controls["email"].setValue(userData.email);
      this.form.controls["email"].disable();
    }
    if (userData.mobileNumber) {
      this.form.controls["phoneNumber"].setValue(userData.mobileNumber);
      this.form.controls["phoneNumber"].disable();
    }
    // if (this.getUserData.profileImageURL) {
    //   this.imageUrl = this.getUserData.profileImageURL;
    // }
    this.spinner.hide();
      // Outputs the current value of email field


  }

  triggers() { }

  /**
   * Function to load image.
   */
  fileChangeListener($event: any) { }

  previewImage(src: any) {
    this.form.controls["profile_img"].patchValue(src);
    this.imageUrl = src;
  }

  getUserDetails() {
    if (!this.userId) {
      this.spinner.hide();
      this.toastr.error("User ID not found. Please log in again.");
      this.dialogRef.close();
      this.router.navigate([""]);
      return;
    }
    
    this.spinner.show();
    this.userService.getUserDetailsById(this.userId).subscribe({
      next: (data: any) => {
        data = this.utility.decryptString(data);
        data = this.dashboardService.removeSuffix(data);
        this.dashboardService._FirstName.emit(data.firstName);
        AuthenticationHelper.setUsername(data.firstName);

        if (data?.firstName) {
          let userData = {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            mobileNumber: data.mobileNo,
            active: data.active ? data.active : false,
          };
          this.setUserData(userData);
        }
        this.spinner.hide();
      },
      error: (errorResponse) => {
        // this.toastr.error(AppConstant.USER_FETCH_ERROR);
        // this.spinner.hide();

        let errorMsg = errorResponse.status;
        if (+errorMsg === 401 || +errorMsg === 404) {
          localStorage.clear();
          this.router.navigate([""]);
          this.toastr.success("Signed Out Successfully");
        } else {
          this.toastr.error(AppConstant.USER_FETCH_ERROR);
        }
        this.spinner.hide();
      }
    });
  }


  /**
   * Method for updating user profile details
   * @param value
   */

  onSubmit(value: any): void {
    this.spinner.show();
    let profileUser = {
      firstName: value.firstName.trim(),
      lastName: value.lastName.trim(),
      // phoneNo: value.phoneNumber,
      id: this.userId,
      active: true,
    };

    const usersBodyJSON = JSON.stringify(profileUser);
    const usersBodyJSONData = usersBodyJSON;
    let userUpdateRequest = this.userService.updateUser(usersBodyJSONData);
    userUpdateRequest.subscribe({
      next: (newdata: any) => {
        this.toastr.success(AppConstant.USER_UPDATE_SUCCESS);
        this.form.reset();
        this.getUserDetails();
        this.spinner.hide();
        // this.router.navigate(["dashboard"]);
      },
      error: (errorResponse: any) => {
        if (errorResponse && errorResponse.error && errorResponse.error.message) {
          this.toastr.error(errorResponse.error.message);
        } else {
          this.toastr.error(AppConstant.USER_UPDATE_ERROR);
        }
        this.spinner.hide();
      }
    });
    this.dialogRef.close()
  }

  onChangePasswordSubmit(data: any) {
    let updatePass = {
      oldPassword: data.password.trim(),
      newPassword: data.passwords.newPassword.trim(),
      id: this.userId,
    };
    const usersBodyJSON = JSON.stringify(updatePass);
    const usersBodyJSONData = usersBodyJSON + "#" + this.randomNumber;
    let userUpdateRequest = this.userService.changePassword(usersBodyJSONData, this.userId);

    userUpdateRequest.subscribe({
      next: (newdata: any) => {
        this.toastr.success(AppConstant.PASS_UPDATE_SUCCESS);
        this.spinner.hide();
        // this.changePasswordSuccess();
      },
      error: (errorResponse: any) => {
        if (errorResponse && errorResponse.error && errorResponse.error.message) {
          this.toastr.error(errorResponse.error.message);
        } else {
          this.toastr.error(AppConstant.PASS_UPDATE_ERROR);
        }
        this.spinner.hide();
      }
    });
  }


  /**
   * if change password success
   */
  // changePasswordSuccess() {
  //   this.changePasswordForm.reset();
  //   this.showError = false;
  //   this.router.navigate(["dashboard"]);
  // }

  /**
   * Function to make selected tab active and other's inactive.
   * @param id
   */
  changeTab(id: any) {
    if (id == "password") {
      this.passwordComponent = true;
      this.settingComponent = false;
      this.notificationComponent = false;
      this.accreditionComponent = false;
    } else if (id == "notification") {
      this.notificationComponent = true;
      this.settingComponent = false;
      this.passwordComponent = false;
      this.accreditionComponent = false;
    } else {
      this.settingComponent = true;
      this.accreditionComponent = false;
      this.passwordComponent = false;
      this.notificationComponent = false;
    }
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  funRestName(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
      this.toastr.warning('Only characters are allowed');
    }

    var k;
    k = event.charCode;
    if ((k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32) {
    } else {
      this.toastr.warning('Only characters are allowed');
    }

    return (k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32;
  }

  funRestNumber(event: any) {
    var k = event.charCode;

    if ((k >= 48 && k <= 57) || k == 8) {
      // Allow numbers (48-57) and backspace (8)
      return true;
    } else {
      event.preventDefault();
      this.toastr.warning('Only numbers are allowed');
      return false;
    }
  }

  // sanitizeInput(input: string): string { 
  //   let sanitizedValue = input.replace(/<[^>]*>/g, ''); 
  //   sanitizedValue = sanitizedValue.replace(/[&^*#$!@()%]/g, '');

  //   return sanitizedValue;
  // }

  // Event handler for input field
  // onInputChange(event: Event): void {
  //   const inputElement = event.target as HTMLInputElement;
  //   const inputValue = inputElement.value;
  //   const sanitizedValue = this.sanitizeInput(inputValue);

  //   inputElement.value = sanitizedValue;
  // }

  onInputChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const inputValue = inputElement.value;
    const sanitizedValue = this.sanitizeInput(inputValue);

    // Use the FormControl's setValue() method instead of directly setting the value
    this.formGroup.get('email')?.setValue(sanitizedValue, { emitEvent: false });
  }

  sanitizeInput(value: string): string {
    // Customize your sanitization logic here
    // For example, allow @ character but strip unwanted characters
    return value.replace(/[^a-zA-Z0-9@._-]/g, '');
  }


  // Event handler for paste event
  onPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedText = event.clipboardData!.getData('text/plain');
    const sanitizedValue = this.sanitizeInput(pastedText);
    document.execCommand('insertText', false, sanitizedValue);
  }
  onKeyDown(event: KeyboardEvent): void {
    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'];
    if (!/^\d+$/.test(event.key) && !allowedKeys.includes(event.key)) {
      event.preventDefault();
    }
  }

  mobileNumberLength: any;
  mobileValidation(data: any) {
    this.mobileNumberLength = data.target.value.length;
  }

  navigateToDashboard() {
    // this.router.navigate(["dashboard"]);
    this.dialogRef.close()
    this.spinner.hide();
  }
}
