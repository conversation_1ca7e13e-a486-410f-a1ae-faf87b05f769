<div class="profile-container">
  <div class="profile-content divAlignLeft" *ngIf="true">
    <form [formGroup]="form" (ngSubmit)="onSubmit(form.value)">
      <div class="col-md-12 profileForm divAlignLeft">
        <div class="col-md-12 divAlignLeft user-image-container">
          <div class="col-md-12 sectionTitleImage">Profile Picture</div>
          <div class="col-md-3 userImageDiv">
            <div class="userImage">
              <img
                class="userProfileImage"
                [src]="imageUrl || 'assets/img/default-user.svg'"
              />
            </div>
          </div>
        </div>

        <div class="row col-md-12 divAlignLeft">
          <div class="col-md-12 sectionTitle">User Information</div>
          <div class="col-md-6 profileInput">
            <div>
              <label class="label-style"
                >First Name&nbsp;<i class="required">*</i></label
              >
              <input
                (keypress)="funRestName($event)"
                formControlName="firstName"
                type="text"
                class="form-control"
                #firstName
                maxlength="30"
                autocomplete="off"
                (input)="onInputChange($event)" (paste)="onPaste($event)"
              />
            </div>
            <div class="error-message">
              <span
                *ngIf="!form.get('firstName')?.valid && form.get('firstName')?.touched"
                class="help-block sub-little-error"
                >First Name is required.</span
              >
            </div>
          </div>
          <div class="col-md-6 profileInput">
            <div>
              <label class="label-style"
                >Last Name&nbsp;<i class="required">*</i></label
              >
              <input
                (keypress)="funRestName($event)"
                formControlName="lastName"
                type="text"
                class="form-control"
                #lastName
                maxlength="30"
                autocomplete="off"
                (input)="onInputChange($event)" (paste)="onPaste($event)"
              />
            </div>
            <div class="error-message">
              <span
                *ngIf="!form.get('lastName')?.valid && form.get('lastName')?.touched"
                class="help-block sub-little-error"
                >Last Name is required.</span
              >
            </div>
          </div>
        </div>

        <div class="row col-md-12">
          <div class="col-md-6 profileInput">
            <div >
              <label class="label-style"
                >Email&nbsp;<i class="required">*</i></label
              >
              <input
                formControlName="email"
                type="text"
                class="form-control"
                maxlength="60"
                autocomplete="off"
                (input)="onInputChange($event)" 
                (paste)="onPaste($event)"
            />
            
            </div>
            <div class="error-message">
              <span
                *ngIf="!form.get('email')?.valid && form.get('email')?.touched"
                class="help-block sub-little-error"
                >Email is required and must be valid.
              </span>
            </div>
          </div>
          <div class="col-md-6 profileInput">
            <div>
              <label class="label-style">Mobile Number&nbsp;<i class="required">*</i></label>
              <input
              formControlName="phoneNumber"
              type="text" 
              class="form-control" 
              (keyup)="mobileValidation($event)"
              (keypress)="funRestNumber($event)"
              autocomplete="off"
              minlength="10"
              maxlength="10"
              (input)="onInputChange($event)" (paste)="onPaste($event)"
            />
            </div>
            <div class="error-message">
              <span
              *ngIf="!form.get('phoneNumber')?.valid && form.get('phoneNumber')?.touched"
              class="help-block sub-little-error"
            >
              Mobile Number is required.
            </span>
              <span
                *ngIf="0<mobileNumberLength&&mobileNumberLength!=10"
                class="help-block sub-little-error"
              >
                Phone number should have 10 digits.
              </span>
              
            </div>
          </div>
          
        </div>

        <div class="rewards-action container">
          <div class="rewards-action-container row">
            <div class="col-md-3"></div>
            <div class="col-md-3">
              <button  type="button" class="btn-style" (click)="navigateToDashboard()">Cancel</button>
            </div>
            <div class="centered-container col-md-3">
              <button
              [ngClass]="{'disable-submit' : (!form.valid ) }"
              [disabled]="!form.valid"
              type="submit"
              class="btn-style margin-left" style="float: left; margin-left: -16px;">
              Update Profile
            </button>
          </div>
          <div class="col-md-3"></div>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="" *ngIf="notificationComponent">
    <h2>Notification</h2>
  </div>
  <div class="" *ngIf="accreditionComponent">
    <h2>Acc</h2>
  </div>
</div>
