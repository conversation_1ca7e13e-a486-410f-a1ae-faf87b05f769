@import "../../../styles";
@import "../../theme/sass/_auth";

$font-size: 13px;

body .modal-backdrop.fade {
  opacity: 0.6;
}

.main-campaign {
  padding-top: 70px;
  .panel {
    .panel-body {
      .wizard {
        min-width: 95%;
        float: left;
        background: #fff;
        margin: 0 2.5%;
        border: 1px solid #E0E0E0;
        border-radius: $table-border-radius;
        .profile-tab-width {
          width: 50%;
        }
        .profile-tab {
          float: left;
          text-align: center;
          border-right: 1px solid #E0E0E0;
          height: 50px;
          line-height: 50px;
          border-radius:0px;
          cursor: pointer;
          font-size: 12px;
          &:hover {
            background: #ddd9;
          }
          &:last-child {
            border-right: none;
          }
          a {
            text-transform: uppercase;
            .profileIcon {
              margin-right: 10px;
            }
          }
        }
      }
      .current-tab {
        // font-size: 14px !important;
        border-bottom: 3px solid $button-color;
        color: $btn-color;
        background: #ddd9;
      }
    }
  }
}

.profile-container {
  margin: 20px;
  padding: 15px;
  background: white;
  // min-width: 95%;
  height: auto;
  overflow-y: auto;
  border-radius: $border-radius;
  // box-shadow: 0px 0px 2px 2px rgba(0, 0, 0, 0.1);

  .input-button {
    text-align: center;
    margin: 1.9% 0 2% 0;
  }
  .form-control[disabled] {
    color: grey;
    border-color: lightgrey;
  }
  .form-control {
    border-radius: $border-radius;
  }

  .profile-content {
    margin-top: -1%;
    margin-left: 2.5%;
    margin-right: 0%;
    .profileInput {
      padding-bottom: 30px;
      float: left
    }
  }
  .user-image-container {
    position: relative;
    text-align: center;
  }
  .userImage {
    width: 100px;
    height: auto;
    margin: 0 auto;
    .userProfileImage {
      border-radius: $border-radius;
      width: 100%;
      margin-left: 0.4%;
    }
  }
  .userImageDiv {
    padding-bottom: 0px;
    padding-top: 0px;
  }
  .btn-style {
    min-width: 150px;
    width: auto;
    float: right;
    margin-right: 15px;
    height: 40px;
  }
  .btn-center{
    align-items: center;
    display: flex;
    justify-content: center;
  }

  .changePasswordContent {
    text-align: center;
    margin-top: 2%;
    margin-left: 2.5%;
    margin-right: 0%;
    .changePasswordInput {
      height: 53px;
      margin: 1.9% 0 0 0;
      display: inline-block;
    }
    .confirmButtonsContainer {
      display: inline-block;
    }
    .confirmPassword {
      padding-right: 0;
      padding-left: 0
    }
    .right-border {
      border-bottom-right-radius: $border-radius;
      border-top-right-radius: $border-radius;
    }
    .input-group {
      .input-group-addon {
        border-bottom-left-radius: $border-radius;
        border-top-left-radius: $border-radius;
      }
    }
    input::-webkit-input-placeholder {
      color: #666666;
    }

  }
  .changePasswordErrorMessage {
    margin-top: -15px;
    width: 100%;

    #password-not-matched{
      margin-right: 53%;
    }
    #password-not-matched2{
      display: flex;
      justify-content: flex-start;
      margin: 10px 36px;
    }
  }
  .disable-submit {
    opacity: 0.8;
    cursor: not-allowed;
    &:hover {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }
  .small {
    font-size: 100%;
  }

  .disable-submit {
    opacity: 0.8;
    cursor: not-allowed;
    float: left;
    &:hover {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }
  .help-block {
    color: red;
    margin-right: 40%;
  }
  .sub-little-error {
    font-size: 14px;
  }
  .sectionTitle {
    font-weight: 600;
    padding-top: 1%;
    padding-bottom: 2%;
  }
  .sectionTitleImage {
    font-weight: 600;
    padding-top: 1%;
    padding-bottom: 1%;
  }
}

@media only screen and (min-device-width: 310px) and (max-device-width: 320px) {
  .profileForm {
    padding-right: 0 !important;
  }
}

@media only screen and (min-width: 320px) and (max-width: 768px) {
  .main-campaign {
    .panel { //Profile Tab CSS
      .panel-body {
        .wizard {
          .profile-tab {
            a {
              .tabText {
                display: none;
              }
              .profileIcon {
                margin-right: 0;
              }
            }
          }
        }
      }
    }
  }
  .profile-container {
    .profile-content {
      .profileForm {
        .user-image-container {
          margin-bottom: 40px;
        }
      }
    }
    .sub-little-error {
      font-size: 12px;
    }
  }

  .centered-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
  
  .button-container {
    text-align: center;
  }
  
  .margin-left{
    float: left;
    margin-left: -16px;
  }
}
.submit-button{
  margin-top: 10px;
}
#submit-btn{
  float: inherit;
}
#basic-addon1{
  border: 1px solid gray;
  padding: 5px 10px 5px 10px;
  background-color: #FF8033;
  color: #fff;
}

.error-margin{
  margin-right: 53%;
}

.has-error .input-group-addon {
  background-color: #FF8033 !important;
  color: #ffffff;
}

.has-error .form-control {
  border: 1px solid red;
}

.hide-eye {
  position: absolute;
  right: 20px;
  z-index: 13;
  top: 8px;
  font-size: 18px;
  cursor: pointer;
}