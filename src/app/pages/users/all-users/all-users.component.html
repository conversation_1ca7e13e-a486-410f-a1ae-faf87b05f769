<div class="user-container" [ngClass]="{ 'overflow-y-operator': showFileTemplate }">
  <div class="user-grid-container">
    <div class="user-grid-data-parent-container">
      <div id="foo" class="user-grid-data-container">
        <div class="user-filter-container">
          <div class="left-column">
            <div class="wizard">
              <a *ngIf="isAdminRole" class="profile-tab history-tab-width" [ngClass]="{ active: isAdmin }"
                (click)="tabChanged('isAdmin')">
                <!-- <span>Admin</span> -->
                <span>
                  Admin<span *ngIf="isAdmin"> ({{ totalCount }})</span>
                </span>
                <img
                  [src]="isAdmin ? '../../../../assets/img/icon/active-admin-icon.svg' : '../../../../assets/img/icon/admin-icon.svg'"
                  alt="icon">
              </a>
              <a class="profile-tab history-tab-width" [ngClass]="{ active: isUplEmployee }"
                (click)="tabChanged('isUplEmployee')">
                <!-- <span>UPL Employee</span> -->
                <span>
                  UPL Employee<span *ngIf="isUplEmployee"> ({{ totalCount }})</span>
                </span>
                <img
                  [src]="isUplEmployee ? '../../../../assets/img/icon/active-upl-employee-icon.svg' : '../../../../assets/img/icon/upl-employee-icon.svg'"
                  alt="icon">
              </a>
              <a class="profile-tab history-tab-width" [ngClass]="{ active: isAppUser }"
                (click)="tabChanged('isAppUser')">
                <span>
                  Leaders<span *ngIf="isAppUser"> ({{ totalCount }})</span>
                </span>
                <img
                  [src]="isAppUser ? '../../../../assets/img/icon/active-leader-icon.svg' : '../../../../assets/img/icon/leader-icon.svg'"
                  alt="icon">
              </a>
            </div>
          </div>
          <div class="right-column">
            <div class="search-container">
              <div class="input-group">
                <div class="search-input">
                  <span class="input-group-add">
                    <i class="fa fa-search" title="Search" aria-hidden="true"></i>
                    <input #searchBox class="input-fields" placeholder="Type to search" target [(ngModel)]="model"
                      (ngModelChange)="onSearch($event)" (keypress)="funRestSearchPrevent($event)" />
                    <span (click)="clearSearch()" *ngIf="model !== ''">
                      <img title="Clear" src="../../../../assets/img/icons8-cancel-50.png" alt="Example Image" />
                    </span>
                  </span>
                </div>
                <div class="export-button">
                  <button class="add" title="Filter" (click)="filterDropdown($event)" *ngIf="currentUsers === 'leader'">
                    <i class="fa fa-filter export-icon"></i>
                  </button>

                  <!-- <button class="add" title="Filter" (click)="UplEmployeeFilterDropdown($event)"
                    *ngIf="currentUsers === 'upl-employee'">
                    <i class="fa fa-filter export-icon"></i>
                  </button> -->
                </div>
                <div class="export-button">
                  <button class="add" (click)="onExport($event)" title="Export">
                    <i class="fa fa-share-square-o export-icon"></i>
                  </button>
                </div>
                @if (currentUsers === 'leader') {
                <div *ngIf="isAdminRole" class="add-button">
                  <button class="add" title="Add Leader" (click)="onClickAddForm($event, 'leader')"
                    *ngIf="currentUsers === 'leader' ">
                    <img width="100%" src="../../../../assets/img/addButton.png" alt="" />
                  </button>
                </div>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="user-table">
          <dynamic-table [tableHeads]="tableHead" [tableData]="tableData" [tableConfiguration]="configurationSettings"
            [tableColName]="tableColName" (onRowEdit)="userDetails($event)" (onStatusChange)="ChangeStatus($event)"
            (pageChange)="getUsersPageData($event)" (uploadAgreement)="uploadAgreement($event)" [showIndex]="showIndex">
          </dynamic-table>
          <!-- <mat-paginator
            [length]="totalRecordCount"
            [pageSize]="perPage"
            [pageIndex]="currentPage"
            [pageSizeOptions]="[20]"
            [showFirstLastButtons]="true"
            (page)="onPageChange($event)"
          ></mat-paginator> -->
        </div>
      </div>
    </div>
  </div>
</div>

<mat-menu #menu="matMenu" class="app-user-menu">
  <button mat-menu-item (click)="onClickAddForm($event, 'admin')" *ngIf="currentUsers === 'admin'">
    <span>{{ addButtonText }}</span>
  </button>

  <button mat-menu-item (click)="onClickAddForm($event, 'upl-employee')" *ngIf="currentUsers === 'upl-employee'">
    <span>{{ addButtonText }}</span>
  </button>

  <button mat-menu-item (click)="onClickAddForm($event, 'leader')" *ngIf="currentUsers === 'leader'">
    <span>{{ addButtonText }}</span>
  </button>

  <mat-divider></mat-divider>

  <button mat-menu-item (click)="onBulkUpload($event, 'leader')" *ngIf="currentUsers === 'leader'">
    <span>{{ "Bulk Upload" }}</span>
  </button>
</mat-menu>

<ng-template #filterMenuDailog>
  <div class="filter-menu-container">
    <div class="filter-heading">
      <span class="FilterTitleSize">Filter By</span>
    </div>
    <div class="filter-container">
      <!-- <div class="role-filter-container">
        <label for="">State </label>
        <angular2-multiselect [data]="stateDataList"
        [(ngModel)]="stateValue"
        [settings]="stateDropdownSettings"
        (onSelect)="selectState($event)"
        (onDeSelect)="deselectionState($event)"
        (onDeSelectAll)="deselectionAllState($event)"
        [ngModelOptions]="{ standalone: true }">
        </angular2-multiselect>
      </div> -->
      <div class="district-filter-container">
        <label for="">Agreement Status </label>
        <angular2-multiselect [data]="agreementDataList"   [(ngModel)]="agreementValue"
            [settings]="agreementStatusDropdownSettings"   (onSelect)="onAgreementStatusSelect($event)"
            (onDeSelect)="onAgreementStatusDeselection($event)" (onSelectAll)="selectedAllDistrictId($event)"
          (onDeSelectAll)="onAgreementStatusDeselectionAll($event)" [ngModelOptions]="{ standalone: true }">
        </angular2-multiselect>
      </div>
    </div>
    <div class="button-container">
      <button type="button" class="btn-cancel" (click)="appUserCancelFilter()">
        {{ "Clear" }}
      </button>
      <button type="button" class="btn-submit" (click)="filterApply()"
        [disabled]="!stateValue?.length && !agreementValue?.length" [ngClass]="{
          'disable-submit': !stateValue?.length && !agreementValue?.length
        }">
        {{ "Apply" }}
      </button>
    </div>
  </div>
</ng-template>

<ng-template #uplEmployeeFilterMenuDailog>
  <div class="filter-menu-container">
    <div class="filter-heading">
      <span class="FilterTitleSize">Filter By</span>
    </div>
    <div class="filter-container">
      <div class="role-filter-container">
        <label for="">Role </label>
        <angular2-multiselect [data]="uplEmployeeRoleList" [(ngModel)]="uplEmployeeRole"
          [settings]="uplEmployeeDropdownSettings" (onSelect)="onUplEmployeeRoleSelect($event)"
          (onDeSelect)="onUplEmployeeRoleDeselection($event)" (onDeSelectAll)="deSelectedAllUplEmployeeRole($event)"
          [ngModelOptions]="{ standalone: true }">
        </angular2-multiselect>
        <span class="error-message" *ngIf="!role || role.length === 0">Role is required.</span>
      </div>
    </div>
    <div class="button-container">
      <button type="button" class="btn-cancel" (click)="cancelEmployeeFilter()">
        {{ "Clear" }}
      </button>
      <button type="button" class="btn-submit" (click)="employeeFilterApply()"
        [disabled]="!uplEmployeeFilterRole.length" [ngClass]="{ 'disable-submit': !uplEmployeeFilterRole.length }">
        {{ "Apply" }}
      </button>
    </div>
  </div>
</ng-template>

<ng-template #addUserDialog>
  <div *ngIf="currentUsers === 'admin'">
    <form [formGroup]="adminForm">
      <div class="main-container">
        <div class="heading-container">
          <h4>{{ userId ? "Edit Admin" : "Add Admin" }}</h4>
        </div>
        <div class="form-input1-container">
          <div class="row" style="width: 100%">
            <div class="col-md-4">
              <span>
                <label for="firstName">First Name<i class="required">&nbsp;*</i></label>
                <input type="text" class="input-field" id="firstName" formControlName="firstName" autocomplete="off"
                  (keypress)="funRestName($event)" (input)="onInputChange($event)" (paste)="onPaste($event)" />
              </span>
              <span class="error-message" *ngIf="
                  !adminForm.get('firstName')?.valid &&
                  adminForm.get('firstName')?.touched
                ">{{ "First name is required" }}</span>
            </div>

            <div class="col-md-4">
              <span>
                <label for="lastName">Last Name<i class="required">&nbsp;*</i></label>
                <input type="text" class="input-field" id="lastName" formControlName="lastName" autocomplete="off"
                  (keypress)="funRestName($event)" (input)="onInputChange($event)" (paste)="onPaste($event)" />
              </span>
              <span class="error-message" *ngIf="
                  !adminForm.get('lastName')?.valid &&
                  adminForm.get('lastName')?.touched
                ">{{ "Last name is required" }}</span>
            </div>

            <div class="col-md-4">
              <span>
                <label for="email">Email<i class="required">&nbsp;*</i></label>
                <input type="text" class="input-field" id="email" formControlName="email" autocomplete="off" />
              </span>
            </div>

          </div>
        </div>
        <div class="form-input1-container">
          <div class="row" style="width: 100%">
            <div class="col-md-4">
              <span>
                <label for="">UGDN<i class="required">&nbsp;*</i></label>
                <input type="text" class="input-field" id="ugdn" formControlName="ugdn"
                  (keypress)="funRestNumber($event)" autocomplete="off" maxlength="8" minlength="4" />
              </span>
              <span class="error-message" *ngIf="
                  !adminForm.get('ugdn')?.valid &&
                  adminForm.get('ugdn')?.touched
                ">{{ "UGDN is required" }}</span>
            </div>
            <div class="col-md-4">
              <span>
                <label for="">Mobile Number<i class="required">&nbsp;*</i></label>
                <input type="text" class="input-field" id="phoneNumber" maxlength="10" minlength="10"
                  formControlName="phoneNumber" (keypress)="funRestSpace($event)" autocomplete="off" />
              </span>
              <span class="error-message" *ngIf="
                  !adminForm.get('phoneNumber')?.valid &&
                  adminForm.get('phoneNumber')?.touched
                ">
                {{
                adminForm.get("phoneNumber")?.hasError("pattern")
                ? "Mobile number should start 0 - 9."
                : adminForm.get("phoneNumber")?.value?.length < 10 &&adminForm.get("phoneNumber")?.value?.length !=0
                  ? "Incorrect mobile number" : "Mobile number is required" }} </span>
            </div>
            <div class="col-md-4">
              <span *ngIf="!userId">
                <label for="">Role</label>
                <angular2-multiselect [data]="userRoleList" [(ngModel)]="role" [settings]="userRoleDropdownSettings"
                  (onSelect)="onUserRoleSelect($event)" (onDeSelect)="onUserRoleDeselection($event)"
                  (onDeSelectAll)="onUserRoleDeselectionAll($event)" [ngModelOptions]="{ standalone: true }">
                </angular2-multiselect>
                <span class="error-message" *ngIf="!role || role.length === 0">Role is required.</span>
              </span>
              <span *ngIf="userId">
                <label for="">Role<i class="required">&nbsp;*</i></label>
                <angular2-multiselect [data]="userRoleList" [(ngModel)]="role" [settings]="userRoleDropdownSettings"
                  (onSelect)="onUserRoleSelect($event)" (onDeSelect)="onUserRoleDeselection($event)"
                  (onDeSelectAll)="onUserRoleDeselectionAll($event)" [ngModelOptions]="{ standalone: true }">
                </angular2-multiselect>
                <span class="error-message" *ngIf="!role || role.length === 0">Role is required.</span>
              </span>
            </div>
          </div>
        </div>
        <div class="button-container">
          <button type="button" class="btn-cancel" (click)="onCloseAdminForm()">
            Cancel
          </button>
          <button type="button" class="btn-submit" (click)="adminUplEmployeeFormSubmit(adminForm.value)"
            [disabled]="!role || role.length === 0" [ngClass]="{ 'disable-submit': !role || role.length === 0 }">
            Submit
          </button>
        </div>
      </div>
    </form>
  </div>

  <div class="change-status-container" *ngIf="currentUsers === 'upl-employee'">
    <form [formGroup]="adminForm">
      <div class="main-container">
        <div class="heading-container">
          <h4>{{ userId ? "Edit UPL Employee" : "Add UPL Employee" }}</h4>
        </div>
        <div class="form-input1-container">
          <div class="row" style="width: 100%">
            <div class="col-md-4">
              <span>
                <label for="firstName">First Name<i class="required">&nbsp;*</i></label>
                <input type="text" class="input-field" id="firstName" formControlName="firstName" autocomplete="off"
                  (keypress)="funRestName($event)" (input)="onInputChange($event)" (paste)="onPaste($event)" />
              </span>
              <span class="error-message" *ngIf="
                  !adminForm.get('firstName')?.valid &&
                  adminForm.get('firstName')?.touched
                ">{{ "First name is required" }}</span>
            </div>

            <div class="col-md-4">
              <span>
                <label for="lastName">Last Name<i class="required">&nbsp;*</i></label>
                <input type="text" class="input-field" id="lastName" formControlName="lastName" autocomplete="off"
                  (keypress)="funRestName($event)" (input)="onInputChange($event)" (paste)="onPaste($event)" />
              </span>
              <span class="error-message" *ngIf="
                  !adminForm.get('lastName')?.valid &&
                  adminForm.get('lastName')?.touched
                ">{{ "Last name is required" }}</span>
            </div>

            <div class="col-md-4">
              <span>
                <label for="email">Email<i class="required">&nbsp;*</i></label>
                <input type="text" class="input-field" id="email" formControlName="email" autocomplete="off" />
              </span>
              <span class="error-message" *ngIf="
                  !adminForm.get('email')?.valid &&
                  adminForm.get('email')?.touched
                ">{{ "Email is required" }}</span>
            </div>
          </div>
        </div>

        <div class="form-input1-container">
          <div class="row" style="width: 100%">
            <div class="col-md-4">
              <span>
                <label for="">UGDN<i class="required">&nbsp;*</i></label>
                <input type="text" class="input-field" id="ugdn" formControlName="ugdn"
                  (keypress)="funRestNumber($event)" autocomplete="off" maxlength="8" minlength="4" />
              </span>
              <span class="error-message" *ngIf="
                  !adminForm.get('ugdn')?.valid &&
                  adminForm.get('ugdn')?.touched
                ">{{ "UGDN is required" }}</span>
            </div>
            <div class="col-md-4">
              <span>
                <label for="">Mobile Number<i class="required">&nbsp;*</i></label>
                <input type="text" class="input-field" id="phoneNumber" maxlength="10" minlength="10"
                  formControlName="phoneNumber" (keypress)="funRestSpace($event)" autocomplete="off" />
              </span>
              <span class="error-message" *ngIf="
                  !adminForm.get('phoneNumber')?.valid &&
                  adminForm.get('phoneNumber')?.touched
                ">
                <!-- {{
                  adminForm.get("phoneNumber")?.hasError("pattern")
                    ? "Mobile number should start 0 - 9."
                    : "Mobile number is required"
                }} -->

                {{
                adminForm.get("phoneNumber")?.hasError("pattern")
                ? "Mobile number should start 0 - 9."
                : adminForm.get("phoneNumber")?.value?.length < 10 &&adminForm.get("phoneNumber")?.value?.length !=0
                  ? "Incorrect mobile number" : "Mobile number is required" }} </span>
            </div>
            <div class="col-md-4">
              <span *ngIf="userId">
                <label for="">Role<i class="required">&nbsp;*</i></label>
                <angular2-multiselect [data]="userRoleList" [(ngModel)]="role" [settings]="userRoleDropdownSettings"
                  (onSelect)="onUserRoleSelect($event)" (onDeSelect)="onUserRoleDeselection($event)"
                  (onDeSelectAll)="onUserRoleDeselectionAll($event)" [ngModelOptions]="{ standalone: true }">
                </angular2-multiselect>
                <span class="error-message" *ngIf="!role || role.length === 0">Role is required.</span>
              </span>
            </div>
          </div>
        </div>
        <div class="button-container">
          <button type="button" class="btn-cancel" (click)="onCloseAdminForm()">
            Cancel
          </button>
          <button type="button" class="btn-submit" (click)="onSubmit(adminForm.value)"
            [disabled]="!role || role.length === 0 || !adminForm.valid"
            [ngClass]="{ 'disable-submit': !role || role.length === 0 }">
            Submit
          </button>
        </div>
      </div>
    </form>
  </div>

  <div class="change-status-container" *ngIf="currentUsers === 'leader'">
    <form [formGroup]="appUserForm">
      <div class="main-container">
        <div class="heading-container">
          <h4>{{ userId ? "Edit Leader" : "Add Leader" }}</h4>
        </div>
        <div class="form-input1-container">
          <div class="row" style="width: 100%">
            <div class="col-md-4">
              <span>
                <label for="">First Name<i class="required">&nbsp;*</i></label>
                <input (paste)="onPaste($event)" (keypress)="withoutSpace($event)" type="text" class="input-field"
                  formControlName="firstName" />
              </span>
            </div>
            <div class="col-md-4">
              <span>
                <label for="">Last Name<i class="required">&nbsp;*</i></label>
                <input (paste)="onPaste($event)" (keypress)="withoutSpace($event)" type="text" class="input-field"
                  formControlName="lastName" />
              </span>
            </div>

            <div class="col-md-4">
              <span>
                <label for="">Mobile Number<i class="required">&nbsp;*</i></label>
                <div class="phone-input-container" style="display: flex; align-items: center;">
                  <select 
                  class="country-code-dropdown" 
                  formControlName="countryCode" 
                  style="width: 50px; height: 38px; margin-right: 5px; border-radius: 4px; border: 1px solid #ccc;" 
                  *ngIf="!userId">
                  <option value="IN">+91</option>
                  <option value="MX" selected>+52</option>
                </select>
                  <input type="text" class="input-field" id="phoneNumber" maxlength="10" minlength="10"
                    formControlName="mobileNumber" (keypress)="funRestSpace($event)" autocomplete="off" style="flex: 1;" />
                </div>
              </span>
              <span class="error-message" *ngIf="
                  !appUserForm.get('mobileNumber')?.valid &&
                  appUserForm.get('mobileNumber')?.touched && !userId
                ">
                {{
                appUserForm.get("mobileNumber")?.hasError("pattern")
                ? "Mobile number should start 0 - 9."
                : appUserForm.get("mobileNumber")?.value?.length < 10 &&appUserForm.get("mobileNumber")?.value?.length
                  !=0 ? "Incorrect mobile number" : "Mobile number is required" }} </span>
            </div>
          </div>
        </div>

        <div class="form-input1-container">
          <div class="row" style="width: 100%">

            <div class="col-md-4">
              <span>
                <label for="">Email<i class="required">&nbsp;*</i></label>
                <input (keypress)="funEmailValidation($event)" type="text" class="input-field"
                  formControlName="email" />
              </span>
              <span class="error-message"
                *ngIf="appUserForm.get('email')?.invalid && (appUserForm.get('email')?.dirty || appUserForm.get('email')?.touched)">
                <span *ngIf="appUserForm.get('email')?.errors?.['validateEmail']">Please enter a valid email
                  address</span>
              </span>
            </div>
            <div class="col-md-4">
              <span>
                <label for="">Company name<i class="required">&nbsp;*</i></label>
                <input (keypress)="funRestName($event)" type="text" class="input-field" formControlName="companyname" />
              </span>
            </div>
            <div class="col-md-4">
              <span>
                <label for="">Address<i class="required">&nbsp;*</i></label>
                <textarea class="input-field" formControlName="address" rows="3">
                </textarea>
              </span>
            </div>

          </div>
        </div>
        <div class="form-input1-container">
          <div class="row app-user" style="width: 100%" display flex>
            <div class="col-md-4">
              <span>
                <label for="">Region<i class="required">&nbsp;*</i></label>
                <angular2-multiselect [data]="regionDataList" [(ngModel)]="regionValue"
                  [settings]="regionDropdownSettings" (onSelect)="selectRegion($event)"
                  (onDeSelect)="deselectionRegion($event)" (onDeSelectAll)="deselectionAllRegion($event)"
                  [ngModelOptions]="{ standalone: true }">
                </angular2-multiselect>
              </span>
            </div>
            <div class="col-md-4">
              <span>
                <label for="">Zone<i class="required">&nbsp;*</i></label>
                <angular2-multiselect [data]="zoneDataList" [(ngModel)]="zoneValue" [settings]="zoneDropdownSettings"
                  (onSelect)="selectZone($event)" (onDeSelect)="deselectionZone($event)"
                  (onDeSelectAll)="deselectionAllZone($event)" [ngModelOptions]="{ standalone: true }">
                </angular2-multiselect>
              </span>
            </div>
            <div class="col-md-4">
              <span>
                <label for="">State<i class="required">&nbsp;*</i></label>
                <angular2-multiselect [data]="stateDataList" [(ngModel)]="stateValue" [settings]="stateDropdownSettings"
                  (onSelect)="selectState($event)" (onDeSelect)="deselectionState($event)"
                  (onDeSelectAll)="deselectionAllState($event)" [ngModelOptions]="{ standalone: true }">
                </angular2-multiselect>
              </span>
            </div>

          </div>
        </div>
        <div class="form-input1-container">
          <div class="row app-user" style="width: 100%" display flex>

            <div class="col-md-6">
              <span>
                <label for="">City<i class="required">&nbsp;*</i></label>
                <angular2-multiselect [data]="cityDataList" [(ngModel)]="cityValue" [settings]="cityDropdownSettings"
                  (onSelect)="selectCity($event)" (onDeSelect)="deselectionCity($event)"
                  (onDeSelectAll)="deselectionAllCity($event)" [ngModelOptions]="{ standalone: true }">
                </angular2-multiselect>
              </span>
            </div>

            <div class="col-md-6">
              <span>
                <label for="">Main Crop</label>
                <angular2-multiselect [data]="cropDataList" [(ngModel)]="cropValue" [settings]="cropDropdownSettings"
                  (onSelect)="selectCrop($event)" (onDeSelect)="deselectionCrop($event)"
                  (onDeSelectAll)="deselectionAllCrop($event)" [ngModelOptions]="{ standalone: true }">
                </angular2-multiselect>
              </span>
            </div>
          </div>
        </div>
        <h3>Upload Document <span *ngIf="userId"></span></h3>

        <div class="upload-section">
          <div class="upload-box" [class.disabled]="userId" style="cursor: pointer;"
            [style.cursor]="userId ? 'not-allowed' : 'pointer'" [style.opacity]="userId ? '0.6' : '1'"
            (click)="!userId && addressProofInput.click()">
            <div class="icon">
              <img src="../../../../assets/img/cloud_upload.png" alt="Cloud Icon" />
            </div>
            <div *ngIf="getFilesByType('addressDoc').length === 0">Upload Address Proof, Browse<i
                class="required">&nbsp;*</i><br /><small>upload png, jpeg, pdf</small></div>
            <div><small *ngFor="let file of getFilesByType('addressDoc')">{{ file.name }}</small></div>
            <input #addressProofInput type="file" accept=".png, .jpeg, .jpg, .pdf"
              (change)="onFileSelected1($event, 'addressDoc')" hidden [disabled]="userId" />
          </div>
          <div class="upload-box" [class.disabled]="userId" style="cursor: pointer;"
            [style.cursor]="userId ? 'not-allowed' : 'pointer'" [style.opacity]="userId ? '0.6' : '1'"
            (click)="!userId && taxIdInput.click()">
            <div class="icon">
              <img src="../../../../assets/img/cloud_upload.png" alt="Cloud Icon" />
            </div>
            <div *ngIf="getFilesByType('rfcDoc').length === 0">Upload Tax ID, Browse<i
                class="required">&nbsp;*</i><br /><small>upload png, jpeg, pdf</small>
            </div>
            <div><small *ngFor="let file of getFilesByType('rfcDoc')">{{ file.name }}</small></div>
            <input #taxIdInput type="file" accept=".png, .jpeg, .jpg, .pdf" (change)="onFileSelected1($event, 'rfcDoc')"
              hidden [disabled]="userId" />
          </div>

          <div class="upload-box" [class.disabled]="userId" style="cursor: pointer;"
            [style.cursor]="userId ? 'not-allowed' : 'pointer'" [style.opacity]="userId ? '0.6' : '1'"
            (click)="!userId && idProofInput.click()">
            <div class="icon">
              <img src="../../../../assets/img/cloud_upload.png" alt="Cloud Icon" />
            </div>
            <div *ngIf="getFilesByType('idDoc').length === 0">Upload ID Proof, Browse<i
                class="required">&nbsp;*</i><br /><small>upload png, jpeg, pdf</small></div>
            <div><small *ngFor="let file of getFilesByType('idDoc')">{{ file.name }}</small></div>
            <input #idProofInput type="file" accept=".png, .jpeg, .jpg, .pdf"
              (change)="onFileSelected1($event, 'idDoc')" hidden [disabled]="userId" />
          </div>
        </div>
        <div class="multiple-login-section">
          <div class="enable-multiple-login">
            <input type="checkbox" id="enableMultipleLogin" [(ngModel)]="enableMultipleLogin"
              [ngModelOptions]="{standalone: true}" [disabled]="disableMultipleLoginToggle"
              (change)="onMultipleLoginToggle()"
              [style.cursor]="disableMultipleLoginToggle ? 'not-allowed' : 'pointer'">
            <label for="enableMultipleLogin" class="enable-login-label">Enable multiple login</label>
          </div>
          <div class="login-users-container" *ngIf="enableMultipleLogin">
            <div *ngFor="let user of loginUsers; let i = index" class="login-user-row">
              <div class="main-form">
                <div class="form-group">
                  <label>First Name<i class="required">&nbsp;*</i></label>
                  <input (paste)="onPaste($event)" (keypress)="withoutSpace($event)" type="text" class="input-field"
                    [(ngModel)]="user.multiFirstName" [ngModelOptions]="{standalone: true}" #multiFirstName="ngModel"
                    required>
                </div>
                <span class="error-message" *ngIf="multiFirstName.invalid && multiFirstName.touched">
                  First Name is required.
                </span>
              </div>

              <div class="main-form">
                <div class="form-group">
                  <label>Last Name<i class="required">&nbsp;*</i></label>
                  <input (paste)="onPaste($event)" (keypress)="withoutSpace($event)" type="text" class="input-field"
                    [(ngModel)]="user.multiLastName" [ngModelOptions]="{standalone: true}" #multiLastName="ngModel"
                    required>
                </div>
                <span class="error-message" *ngIf="multiLastName.invalid && multiLastName.touched">
                  Last Name is required.
                </span>
              </div>

              <div class="main-form">
                <div class="form-group">
                  <label>Mobile Number<i class="required">&nbsp;*</i></label>
                  <div class="mobile-section">
                    <input type="text" (keypress)="funRestSpace($event)" maxlength="10" minlength="10"
                      class="input-field" [(ngModel)]="user.phoneNumber" [ngModelOptions]="{standalone: true}"
                      #phoneNumber="ngModel" required pattern="^[0-9]{10}$">
                  </div>
                  <span class="error-message" *ngIf="phoneNumber.invalid && phoneNumber.touched">
                    <span *ngIf="phoneNumber.errors?.['required']">Mobile Number is required.</span>
                    <span *ngIf="phoneNumber.errors?.['pattern']">Mobile Number must be 10 digits.</span>
                  </span>
                </div>
              </div>
              <div class="main-form">
                <div class="form-group">
                  <label>Email<i class="required">&nbsp;*</i></label>
                  <input (keypress)="funEmailValidation($event)" type="email" class="input-field"
                    [(ngModel)]="user.email" [ngModelOptions]="{standalone: true}" #email="ngModel" required email>
                </div>
                <span class="error-message" *ngIf="email.invalid && email.touched">
                  <span *ngIf="email.errors?.['required']">Email is required.</span>
                  <span *ngIf="email.errors?.['email']">Enter valid email.</span>
                </span>
              </div>

              <div class="main-button-section">
                <div class="action-btns">
                  <button *ngIf="i === loginUsers.length - 1" type="button" (click)="addLoginUser()"
                    class="add-user-btn"  [disabled]="!isLastRowFilled()"
                    [style.cursor]="!isLastRowFilled() ? 'not-allowed' : 'pointer'"
                    [style.opacity]="!isLastRowFilled() ? '0.5' : '1'">
                    <i class="fa fa-plus"></i>
                  </button>

                  <button *ngIf="i > 0" type="button" (click)="removeLoginUser(i)" class="remove-user-btn"
                    [disabled]="user.fromApi" [style.cursor]="user.fromApi ? 'not-allowed' : 'pointer'"
                    [style.opacity]="user.fromApi ? '0.5' : '1'">
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="button-container">
          <button type="button" class="btn-cancel" (click)="onCloseAppUserForm()">
            {{ "Cancel" }}
          </button>
          <button *ngIf="!userId" type="button" class="btn-submit" (click)="onSubmit(appUserForm.value)"
            [disabled]="!checkFormValidity()" [ngClass]="{ 'disable-submit': !checkFormValidity() }">
            {{ "Submit" }}
          </button>
          <button *ngIf="userId" type="button" class="btn-submit" (click)="onSubmit(appUserForm.value)"
            [disabled]="!checkFormValidity()" [ngClass]="{ 'disable-submit': !checkFormValidity() }">
            {{ "Submit" }}
          </button>
        </div>
      </div>
    </form>
  </div>
</ng-template>

<!-- bulk upload -->
<ng-template #bulkPreviewBox>
  <div class="change-status-container" *ngIf="currentUsers === 'leader'">
    <div class="main-container">
      <div [ngClass]="{
          'eyebrow-heading-container': !isPreview,
          preview: isPreview
        }">
        <div class="top-container">
          <div></div>
          <div class="cursor">
            <div (click)="downloadTemplate()">
              <i class="fa fa-download"></i>
              <p>{{ "Filetemplate.xlsx" }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="body-container">
        <h4>{{ "Bulk Upload" }}</h4>
      </div>
      <div *ngIf="!isPreview" class="msg-container">
        <p>{{ "Please Download the bulk upload template" }}</p>
      </div>
      <div *ngIf="isPreview" class="msg-container">
        <p>{{ "Bulk Preview" }}</p>
      </div>

      <div *ngIf="!isPreview" class="container-fluid">
        <div class="upload-container" (click)="fileInput.click()">
          <div class="upload-icon">
            <i class="fa fa-upload" aria-hidden="true"></i>
          </div>
          <div class="upload-text">
            <input readonly hidden type="file" #fileInput id="importFile"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel.sheet.macroEnabled.12"
              style="width: 300px" (change)="
                onFilePreviewFile($event); onFileSelected(fileInput.files)
              " />
            {{ "Choose a File" }}
          </div>
        </div>
      </div>

      <div *ngIf="isPreview" class="preview-fluid">
        <div class="preview-fluid-container">
          <div class="upload-button">
            <span class="name-container">{{
              fileName + "(" + fileSizeInKB + "kb)"
              }}</span>
            <span class="add" title="Re-Upload" (click)="fileInput.click()">
              <img class="fa" src="../../../../assets/img/re-upload_image.png" aria-hidden="true" />
              <input readonly hidden type="file" #fileInput id="importFile"
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel.sheet.macroEnabled.12"
                style="width: 300px" (change)="
                  onFilePreviewFile($event); onFileSelected(fileInput.files)
                " />
            </span>
          </div>
          <div class="container-preview">
            <div id="tableHost"></div>
          </div>
        </div>
      </div>

      <div class="button-container">
        <button type="button" class="btn-cancel" [ngClass]="{ 'button-width': isPreview }" (click)="clearFileInput()">
          {{ "Cancel" }}
        </button>
        <button type="button" class="btn-submit" [disabled]="isSubmitDisabled" [ngClass]="{
            'btn-submit-width': isPreview,
            'disable-submit': isSubmitDisabled
          }" (click)="submitBulkUploadFile($event)">
          <div class="upload-btn-container"></div>
          <div class="upload-button">
            {{ "Submit" }}
          </div>
        </button>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #uploadAggrement>
  <div class="invoice-container">
    <div class="add-btn-container">
      <h4>Upload Agreement</h4>
      <button class="addSchemeButton" title="Add" [disabled]="isAgreementActive" (click)="openAgreement()">
        <img width="20px" height="20px" src="../../../assets/img/White_cloud_upload.png" alt="Add Button" />
        {{ "Upload Agreement" }}
      </button>
    </div>
    <div class="reward-points-history-table" style="padding: 10px;">
      <dynamic-table [tableHeads]="uploadAgreementHeader" [tableData]="uploadTableData"
        [tableConfiguration]="uploadAggrementSettings" [tableColName]="uploadAgreementColumn"
        (downloadAgreement)="downloadAgreementPdf($event)" (deleteRow)="deleteRowData($event)"
        (pageChange)="getUsersPageData($event)" [showIndex]="showIndex">
      </dynamic-table>
    </div>
    <div class="button-section">
      <button class="close-btn" (click)="onClose()">Close</button>
    </div>
  </div>
</ng-template>
<ng-template #uploadAggrementFile>
  <form [formGroup]="targetForm">
    <div class="upload-container">
      <h2>Upload Agreement</h2>
      <div class="date-section">
        <div class="form-section target-duration-section">
          <div class="date-inputs">
            <div class="date-group">
              <label>Start Date</label>
              <div class="date-picker">
                <input [matDatepicker]="startPicker" formControlName="startDate" [min]="minDate" readonly>

                <mat-datepicker-toggle [for]="startPicker">
                  <mat-icon>calendar_today</mat-icon>
                </mat-datepicker-toggle>
                <mat-datepicker #startPicker></mat-datepicker>
              </div>
            </div>
            <div class="date-group">
              <label>End Date</label>
              <div class="date-picker">
                <input [matDatepicker]="endPicker" formControlName="endDate" [min]="targetForm.get('startDate')?.value"
                  readonly (focus)="endPicker.open()">
                <mat-datepicker-toggle [for]="endPicker">
                  <mat-icon>calendar_today</mat-icon>
                </mat-datepicker-toggle>
                <mat-datepicker #endPicker></mat-datepicker>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="file-upload-section">
        <label>Upload File</label>
        <div class="file-upload-box" (click)="pdfInput.click()">
          <span class="file-icon">
            <img width="18px" class="pdf-image" src="../../../assets/img/pdf-icon.svg" alt="PDF" />
          </span>
          <span class="file-name">
            {{
            getFilesByType('pdf').length > 0
            ? getFilesByType('pdf')[0].name
            : 'Upload PDF'
            }}
          </span>
        </div>
        <input #pdfInput type="file" accept=".pdf" (change)="onFileSelect($event, 'pdf')" hidden />
      </div>
      <div class="button-section">
        <button class="cancel-btn" (click)="onCancel()">Cancel</button>
        <button class="submit-btn" (click)="onSubmited()" [disabled]="!getFilesByType('pdf').length">Submit</button>
      </div>
    </div>
  </form>
</ng-template>