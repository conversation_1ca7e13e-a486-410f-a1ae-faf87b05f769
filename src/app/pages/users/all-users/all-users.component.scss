@import "../../../theme/sass/_auth";
@import "../../../../styles";
@import "../../../theme/sass/mixins";

.upload-section {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.upload-box {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #CCC;
  text-align: left;
  padding: 1.2rem;
  border-radius: 8px;
  color: #FF8033;
  font-weight: 500;
  background: #fff7f0;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed !important;
    pointer-events: none !important;
  }
  
  .icon {
    font-size: 2rem;
    margin-right: 1rem;
  }
  
  small {
    font-size: 0.8rem;
    color: #999;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
  }
  
  div small {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    display: inline-block;
  }
}

.error-message {
  color: red;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}