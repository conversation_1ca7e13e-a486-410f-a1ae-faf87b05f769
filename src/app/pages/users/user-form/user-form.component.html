<div class="common-container">
  <div class="profile-content">
    <div>
      <label>OTP</label>
      <input [max]="1" type="text" />
      <input [max]="1" type="text" />
      <input [max]="1" type="text" />
      <input [max]="1" type="text" />
      <input [max]="1" type="text" />
      <input [max]="1" type="text" />
    </div>

    <form [formGroup]!="userForm" (ngSubmit)="onSubmit(userForm.value)">
      <div class="col-md-12 row profileForm">
        <div class="float-left width-100">
          <h2 class="info-title">User Information</h2>
          <div class="personal-information-section">
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >First Name<i class="required">&nbsp;*</i></label
                >
                <input
                  (keypress)="funRestName($event)"
                  formControlName="first_name"
                  type="text"
                  class="form-control"
                  autocomplete="off"
                  (input)="onInputChange($event)"
                  (paste)="onPaste($event)"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !userForm.get('first_name')?.valid &&
                    userForm.get('first_name')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >First Name is required.</span
                >
              </div>
            </div>

            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Last Name<i class="required">&nbsp;*</i></label
                >
                <input
                  (keypress)="funRestName($event)"
                  formControlName="last_name"
                  type="text"
                  class="form-control"
                  (input)="onInputChange($event)"
                  (paste)="onPaste($event)"
                  autocomplete="off"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !userForm.get('last_name')?.valid &&
                    userForm.get('last_name')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >Last Name is required.</span
                >
              </div>
            </div>

            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Email<i class="required">&nbsp;*</i></label
                >
                <input
                  formControlName="email"
                  type="text"
                  class="form-control"
                  #emailid
                  autocomplete="off"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    emailid.value == '' &&
                    userForm.get('email')?.touched &&
                    userForm.get('email')?.invalid
                  "
                  class="help-block sub-little-error confpass"
                  >Email Id is required.</span
                >
                <span
                  *ngIf="
                    emailid.value != '' &&
                    userForm.get('email')?.invalid &&
                    userForm.get('email')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >Invalid Email ID</span
                >
              </div>
            </div>

            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >UGDN<i class="required">&nbsp;*</i></label
                >
                <input
                  (keypress)="funRestNumber($event)"
                  formControlName="code"
                  [pattern]="'^[6-9][0-9]{9}$'"
                  type="text"
                  class="form-control"
                  maxlength="8"
                  minlength="4"
                  autocomplete="off"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !userForm.get('code')?.valid &&
                    userForm.get('code')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >UGDN is required.</span
                >
              </div>
            </div>

            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Phone Number<i class="required">&nbsp;*</i></label
                >
                <input
                  (keypress)="funRestSpace($event)"
                  formControlName="phone"
                  [pattern]="'^[6-9][0-9]{9}$'"
                  type="text"
                  class="form-control"
                  maxlength="10"
                  autocomplete="off"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !userForm.get('phone')?.valid &&
                    userForm.get('phone')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >Phone Number is required.</span
                >
              </div>
            </div>

            <div
              class="col-md-12 bottom-padding bottom-padding-user-role-status"
              [class.border-bottom]="isAfSwal"
            ></div>
          </div>
        </div>
      </div>
    </form>

    <div class="col-md-12 row profileForm">
      <div
        class="col-md-12 form-group input-button submit-button padding-left-right"
      >
        <button
          type="button"
          class="btn-cancel-style pull-left"
          (click)="location.back()"
        >
          Cancel
        </button>
        <button
          (click)="onSubmit(userForm.value)"
          [class.disable-submit]="!userForm.valid || !selectedRoles || (Array.isArray(selectedRoles) && selectedRoles.length === 0)"
          [disabled]="!userForm.valid || !selectedRoles || (Array.isArray(selectedRoles) && selectedRoles.length === 0)"
          type="submit"
          class="btn-style"
        >
          {{ userId ? "Update" : "Add User" }}
        </button>
      </div>
    </div>
  </div>
</div>
