import { Component } from '@angular/core';
import { Location } from '@angular/common';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BaThemeSpinner } from '../../../theme/services/baThemeSpinner/baThemeSpinner.service';
import { Router, ActivatedRoute } from '@angular/router';
import { GlobalEvents } from '../../../helpers/global.events';
import { UserService } from '../../../app-services/user-service';
import { ZonesService } from '../../../app-services/zones-service';
import { RegionsService } from '../../../app-services/regions-service';
import { AppConstant } from '../../../constants/app.constant';
import { EmailValidator } from '../../../theme/validators/email.validator';
import { TerritoriesService } from '../../../app-services/territories-service';
import { BlankSpaceValidator } from '../../../theme/validators/blank.validator';
import { Utility } from 'src/app/shared/utility/utility';
import { SupportService } from 'src/app/app-services/support.service';

@Component({
  selector: 'add-user',
  styleUrls: ['user.component.scss'],
  templateUrl: 'user-form.component.html',
})
export class UserForm {
  userForm!: FormGroup;
  userId: any;
  userData: any;
  profitCenter: string = '';
  zone: string = '';
  region: string = '';
  territory: string = '';
  isAF: boolean = false;
  isSWAL: boolean = false;
  zoneDetails: any = [];
  sbuDetails: any = [];
  regionsDetails: any = [];
  territoriesDetails: any = [];
  selectedRoles: any = [];
  sbuChanged: boolean = false;
  zonalChanged: boolean = false;
  regionChanged: boolean = false;
  territoryChanged: boolean = false;
  profitCenterRole: any;
  zoneValue: any;
  pushPopRoles: any = [1];
  profitCenterRoleAfOptions = [
    { id: 5, role: 'SBO' },
    { id: 4, role: 'Zonal Manager' },
    { id: 2, role: 'Territory Manager' },
  ];
  profitCenterRoleSwalOptions = [
    { id: 4, role: 'Zonal Manager' },
    { id: 3, role: 'Regional Manager' },
    { id: 2, role: 'Territory Manager' },
  ];
  isAdmin: boolean = false;
  isAfSwal: boolean = false;
  phoneNumberMask = [
    /[1-9]/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
  ];
  textMask = [/[a-zA-Z]/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/];
  userIsActive!: boolean;
  constructor(
    private fb: FormBuilder,
    public location: Location,
    private router: Router,
    public events: GlobalEvents,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private activeRoute: ActivatedRoute,
    private userService: UserService,
    private zonesService: ZonesService,
    private regionsService: RegionsService,
    private territoriesService: TerritoriesService,
    private utility: Utility,
    private supportService: SupportService
  ) {
    // this.userId = this.activeRoute.snapshot.queryParams['id'];
    this.userId = localStorage.getItem('id');
    this.supportService.setButtonState('new');
  }

  /**
   *  Called automatically on init
   */
  roleName: any;
  ngOnInit() {
    this.userIsActive = this.userService.getUserActive();
    this.roleName = 'users/' + this.userService.getRoleName();
    this.setUserForm();
    if (this.userId) {
      this.getUserByID(this.userId);
    } else {
      this.spinner.hide();
    }
    window.scrollTo(0, 0);
  }

  ngOnDestroy() {
    localStorage.removeItem('id');
    if (this.userId == localStorage.getItem('userID')) {
    }
  }

  /**
   * Method for getting specific user details by id
   * @param id
   */
  getUserByID(id: any) {
    this.spinner.show();
    this.userService.getUserDetailsById(this.userId).subscribe({
      next: (userData: any) => {
        userData = this.utility.decryptString(userData)
        userData = (userData);
        let data = JSON.parse(userData);
        if (data && data.content && data.content[0]) {
          this.userData = {
            code: (data.content[0].user.code),
            first_name: (data.content[0].user.firstName),
            last_name: (data.content[0].user.lastName),
            email: (data.content[0].user.email),
            phone: (data.content[0].user.phoneNo),
            role: (data.content[0].user.role[0].id),
            id: data.content[0].id,
            profitCenter: data.content[0].profitCenter
              ? data.content[0].profitCenter.code
              : '',
            sbu: data.content[0].sbu ? data.content[0].sbu.code : '',
            zone: data.content[0].zone ? data.content[0].zone.code : '',
            region: data.content[0].region ? data.content[0].region.code : '',
            territory: data.content[0].territory
              ? data.content[0].territory.code
              : '',
          };
          this.zone = this.userData.zone;
          this.region = this.userData.region;
          this.territory = this.userData.territory;
          this.selectedRoles = '';
          if (this.userData.profitCenter) {
            switch (this.userData.profitCenter) {
              case 'DO1301':
                this.profitCenter = this.userData.profitCenter;
                this.isAF = true;
                this.isSWAL = false;
                break;
              case 'DO1401':
                this.profitCenter = this.userData.profitCenter;
                this.isAF = false;
                this.isSWAL = true;
                break;
            }
          }
          if (this.userData.role == 1) {
            this.selectedRoles = 1;
            this.isAdmin = true;
            this.isAfSwal = false;
          } else {
            this.selectedRoles = 'afswal';
            this.isAfSwal = true;
            this.isAdmin = false;
          }

          switch (this.userData.role) {
            case 1:
              this.pushPopRoles.push(1);
              break;
            case 2:
              this.pushPopRoles.push(2);
              break;
            case 3:
              this.pushPopRoles.push(3);
              break;
            case 4:
              this.pushPopRoles.push(4);
              break;
            case 5:
              this.pushPopRoles.push(5);
              break;
          }
          this.setUserForm(this.userData);
          this.spinner.hide();
        }
        this.events.setChangedContentTopText(
          this.userId
            ? this.selectedRoles == '1'
              ? 'Edit Admin User'
              : 'Edit User'
            : 'Add User'
        );
      },
      error: (errorResponse: any) => {
        this.events.setChangedContentTopText('Edit User');
        this.toastr.error(AppConstant.USER_FETCH_ERROR);
        this.router.navigate(['users']);
        this.spinner.hide();
        let errorMsg = errorResponse.status;
        if (+errorMsg === 401 || +errorMsg === 404) {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        } else {
          this.toastr.error(AppConstant.USER_FETCH_ERROR);
        }
      },
    });
  }

  /**
   *  Called automatically after view init
   */
  ngAfterViewInit() {
    if (!this.userId) {
      this.events.setChangedContentTopText('Add User');
    }
  }

  /**
   * Method to set the profit center of the edited users
   * @param event
   */
  profitCenterFunc(event: any) {
    switch (event.value) {
      case 'DO1301':
        this.isAF = true;
        this.isSWAL = false;
        this.userForm.controls['profit-center-role-AF'].setValidators([
          Validators.required,
        ]);
        if (this.userForm.controls['profit-center-role-SWAl']) {
          this.userForm.controls['profit-center-role-SWAl'].setValidators(null);
        }
        this.sbuChanged = false;
        this.zonalChanged = false;
        this.territoryChanged = false;
        break;
      case 'DO1401':
        this.isAF = false;
        this.isSWAL = true;
        this.userForm.controls['profit-center-role-SWAL'].setValidators([
          Validators.required,
        ]);
        this.userForm.controls['profit-center-role-AF'].setErrors(null);
        this.userForm.controls['profit-center-role-AF'].setValidators(null);
        this.zonalChanged = false;
        this.regionChanged = false;
        this.territoryChanged = false;
        break;
    }
  }

  /**
   * Method for setting the selected roles
   * @param event
   */
  selectedRolesFunc(event: any) {
    this.pushPopRoles = [];

    if (event.target.value && event.target.value == 'afswal') {
      this.isAdmin = false;
      this.isAfSwal = true;
      this.profitCenter = 'DO1301';
      this.pushPopRoles.push('afswal');
      this.profitCenterFunc(this.profitCenter);
    } else if (event.target.value) {
      this.userForm.controls['profit-center-role-AF'].setErrors(null);
      this.userForm.controls['profit-center-role-AF'].setValidators(null);
      this.userForm.controls['sbu'].setValidators(null);
      this.userForm.controls['zone'].setValidators(null);
      this.userForm.controls['region'].setValidators(null);
      this.userForm.controls['territory'].setValidators(null);
      this.userForm.controls['profit_center'].setValidators(null);
      this.userForm.controls['profit-center-role-SWAL'].setValidators(null);
      this.pushPopRoles.push(event.target.value);
      this.isAfSwal = false;
      this.isAdmin = true;
    } else {
      // Handle empty role selection
      this.selectedRoles = '';
      this.isAfSwal = false;
      this.isAdmin = false;
      this.toastr.error('Role selection is required');
    }
  }

  /**
   * To handle the change event of SBU dropdown
   * @param event
   */
  sbuSelection(event: any) {
    if (this.profitCenterRole !== 'SBO') {
      this.zoneDetails = [];
      this.territoriesDetails = [];
      this.zone = '';
      this.userForm.controls['zone'].setValue('');
      if (this.userForm.controls['territory']) {
        this.userForm.controls['territory'].setValue('');
      }
    }
  }

  /**
   * To handle the zone changed event of zone dropdown
   * @param event
   * @param isType
   */
  zoneSelection(event: any, isType?: any) {
    if (event && event.target.value) {
      this.zoneValue = event.target.value;
      if (this.profitCenterRole == 'Territory Manager' && this.isAF) {
        this.userForm.controls['territory'].setValue('');
      } else if (
        this.profitCenterRole == 'Regional Manager' ||
        this.profitCenterRole == 'Territory Manager'
      ) {
        this.userForm.controls['region'].setValue('');
        this.regionsDetails = [];
        this.getAllRegionsByZone(this.zoneValue);
      }
    }
  }

  /**
   * To handle click event of region dropdown
   * @param event
   * @param region
   */
  regionSelection(event: any, region?: any) {
    if (event && event.target.value) {
      if (this.profitCenterRole == 'Territory Manager') {
        this.userForm.controls['territory'].setValue('');
        this.territoriesDetails = [];
        this.getAllTerritoriesByRegion(event.target.value);
      }
    }
  }

  /**
   * To get the territories by region
   * @param code
   */
  getAllTerritoriesByRegion(code: any) {}

  /**
   * To get the regions by zone
   * @param code
   */
  getAllRegionsByZone(code: any) {
    this.spinner.show();
    const allRegions = this.regionsService.getAllRegionsByZoneCode(code);
    allRegions.subscribe({
      next: (regionsDetails: any) => {
        this.regionsDetails = [];
        if (
          regionsDetails &&
          regionsDetails.content &&
          regionsDetails.content.length
        ) {
          regionsDetails.content.forEach((regionInfo: any) => {
            const zoneInfoObj = {
              code: regionInfo.code,
              id: regionInfo.id,
              name: regionInfo.name,
            };
            this.regionsDetails.push(zoneInfoObj);
          });
        }
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
        let error:any =(errorResponse.error)
        error= JSON.parse(error);
        if('Full authentication is required to access this resource'==error.message){
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  /**
   *  To set user form values
   */
  setUserForm(values?: any) {
    this.userForm = this.fb.group({
      code: [
        values ? values.code : '',
        Validators.compose([Validators.required, BlankSpaceValidator.validate]),
      ],
      phone: [
        values ? values.phone : '',
        Validators.compose([
          Validators.required,
          BlankSpaceValidator.validate,
          Validators.maxLength(10),
          Validators.minLength(10),
        ]),
      ],
      first_name: [
        values ? values.first_name : '',
        Validators.compose([Validators.required, BlankSpaceValidator.validate]),
      ],
      last_name: [
        values ? values.last_name : '',
        Validators.compose([Validators.required, BlankSpaceValidator.validate]),
      ],
      email: [
        values ? values.email : '',
        Validators.compose([
          Validators.required,
          EmailValidator.validate,
          BlankSpaceValidator.validate,
        ]),
      ],

      profit_center: [values ? values.profitCenter : ''],
      zone: [values ? values.zone : ''],
      region: [values ? values.region : ''],
      territory: [values ? values.territory : ''],
      sbu: [values ? values.sbu : ''],
    });

    if (this.userId) {
      this.userForm.controls['code'].disable();
      this.userForm.controls['email'].disable();
    }
  }

  /**
   * submission of user form to add or update user.
   */
  onSubmit(values: any): void {}

  /**
   * Triggered when the profit center role changed to AF is changed
   * @param event
   * @returns {boolean}
   */
  onProfitCenterRoleChangeAf(event: any) {
    if (this.isAF) {
      this.sbuDetails = [];
      this.userForm.controls['sbu'].setValue('');
    } else if (this.isSWAL) {
      this.zoneDetails = [];
    }
    if (event.value) {
      this.pushPopRoles = [];
      this.profitCenterRole = event.value;
      this.userForm.controls['region'].setValidators(null);
      this.region = '';
      if (this.profitCenterRole === 'SBO') {
        this.sbuChanged = true;
        this.zonalChanged = false;
        this.territoryChanged = false;
        this.pushPopRoles.push(5);
        this.userForm.controls['sbu'].setValidators([Validators.required]);
        this.userForm.controls['zone'].setValidators(null);
        this.userForm.controls['territory'].setValidators(null);
        this.zone = '';
      } else if (this.profitCenterRole === 'Zonal Manager') {
        this.sbuChanged = true;
        this.zonalChanged = true;
        this.territoryChanged = false;
        this.userForm.controls['sbu'].setValue('');
        this.pushPopRoles.push(4);
        this.userForm.controls['sbu'].setValidators([Validators.required]);
        this.userForm.controls['zone'].setValidators([Validators.required]);
        this.userForm.controls['territory'].setValidators(null);
        this.territory = '';
      } else if (this.profitCenterRole === 'Territory Manager') {
        this.sbuChanged = true;
        this.zonalChanged = true;
        this.territoryChanged = true;
        this.userForm.controls['sbu'].setValue('');
        this.userForm.controls['zone'].setValue('');
        this.pushPopRoles.push(2);
        this.zone = '';
        this.userForm.controls['sbu'].setValidators([Validators.required]);
        this.userForm.controls['zone'].setValidators([Validators.required]);
        this.userForm.controls['territory'].setValidators([
          Validators.required,
        ]);
      }
    }
    return false;
  }

  /**
   * Triggered when the profit center role changed to SWAL is changed
   * @param event
   * @returns {boolean}
   */
  onProfitCenterRoleChangeSwal(event: any) {
    if (event.value) {
      this.pushPopRoles = [];
      this.profitCenterRole = event.value;
      this.userForm.controls['sbu'].setValidators(null);
      if (this.profitCenterRole === 'Zonal Manager') {
        this.zonalChanged = true;
        this.regionChanged = false;
        this.territoryChanged = false;
        this.pushPopRoles.push(4);
        this.userForm.controls['zone'].setValidators([Validators.required]);
        this.userForm.controls['territory'].setValidators(null);
        this.userForm.controls['territory'].setValue('');
        this.userForm.controls['region'].setValidators(null);
        this.userForm.controls['region'].setValue('');
        this.region = '';
        this.territory = '';
      } else if (this.profitCenterRole === 'Regional Manager') {
        this.zonalChanged = true;
        this.regionChanged = true;
        this.territoryChanged = false;
        this.userForm.controls['zone'].setValue('');
        this.userForm.controls['region'].setValue('');
        this.pushPopRoles.push('3');
        this.userForm.controls['zone'].setValidators([Validators.required]);
        this.userForm.controls['region'].setValidators([Validators.required]);
        this.userForm.controls['territory'].setValidators(null);
        this.zone = '';
        this.territory = '';
      } else if (this.profitCenterRole === 'Territory Manager') {
        this.zonalChanged = true;
        this.regionChanged = true;
        this.territoryChanged = true;
        this.userForm.controls['zone'].setValue('');
        this.userForm.controls['region'].setValue('');
        this.userForm.controls['territory'].setValue('');
        this.regionsDetails = [];
        this.pushPopRoles.push(2);
        this.userForm.controls['zone'].setValidators([Validators.required]);
        this.userForm.controls['region'].setValidators([Validators.required]);
        this.userForm.controls['territory'].setValidators([
          Validators.required,
        ]);
        this.region = '';
        this.zone = '';
      }
    }
    return false;
  }

  /**
   * Method for displaying specific user's sbu, zone, region, territory
   */
  getDropDownsData() {
    if (this.userData.zone && this.isSWAL) {
      this.getAllRegionsByZone(this.userData.zone);
    }

    if (this.userData.region) {
      this.getAllTerritoriesByRegion(this.userData.region);
    }
  }

  /**
   * Method for displaying specific user's role
   */
  getPCRole(role: any) {
    this.sbuChanged = false;
    this.zonalChanged = false;
    this.regionChanged = false;
    this.territoryChanged = false;
    if (role && role == 5) {
      this.sbuChanged = true;
      this.profitCenterRole = 'SBO';
      return 'SBO';
    } else if (role == 4) {
      this.sbuChanged = true;
      this.zonalChanged = true;
      this.profitCenterRole = 'Zonal Manager';
      return 'Zonal Manager';
    } else if (role == 3) {
      this.sbuChanged = false;
      this.zonalChanged = true;
      this.regionChanged = true;
      this.profitCenterRole = 'Regional Manager';
      return 'Regional Manager';
    } else if (role == 2) {
      this.sbuChanged = true;
      this.zonalChanged = true;
      this.regionChanged = true;
      this.territoryChanged = true;
      this.profitCenterRole = 'Territory Manager';
      return 'Territory Manager';
    } else {
      return '';
    }
  }

  funRestSpace(event: any) {
    if (event.code === 'Space') {
      event.preventDefault();
    }
    var k;
    k = event.charCode;
    if (k >= 48 && k <= 57) {
    } else if (event.key == ' ') {
    } else {
    }

    return k >= 48 && k <= 57;
  }

  funRestEmail(event: any) {
    var k;
    k = event.charCode;
    if (event.code === 'Space') {
      event.preventDefault();
    }

    if (
      (k > 64 && k < 91) ||
      (k > 96 && k < 123) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57) ||
      event.key == '.' ||
      event.key == '-' ||
      event.key == '_' ||
      event.key == '+' ||
      event.key == '@' ||
      event.key == ''
    ) {
    } else {
    }

    return (
      (k > 64 && k < 91) ||
      (k > 96 && k < 123) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57) ||
      event.key == '.' ||
      event.key == '-' ||
      event.key == '_' ||
      event.key == '+' ||
      event.key == '@' ||
      event.key == ''
    );
  }

  funRestName(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k;
    k = event.charCode;
    if ((k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32) {
    } else {
    }

    return (k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32;
  }

  specialSymbols(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k;
    k = event.charCode;
    if (
      (k > 64 && k < 91) ||
      (k > 96 && k < 123) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
    } else {
    }

    return (
      (k > 64 && k < 91) ||
      (k > 96 && k < 123) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }
  funRest(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }
  }

  funRestNumber(event: any) {
    var k = event.charCode || event.keyCode;
    if ((k >= 48 && k <= 57) || k == 8) {
      return true;
    } else {
      event.preventDefault();
      return false;
    }
  }

  sanitizeInput(input: string): string {
    let sanitizedValue = input.replace(/<[^>]*>/g, '');
    sanitizedValue = sanitizedValue.replace(/[&^*#$!@()%]/g, '');
    return sanitizedValue;
  }

  // Event handler for input field
  onInputChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const inputValue = inputElement.value;
    const sanitizedValue = this.sanitizeInput(inputValue);
    inputElement.value = sanitizedValue;
  }

  // Event handler for paste event
  onPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedText = event.clipboardData!.getData('text/plain');
    const sanitizedValue = this.sanitizeInput(pastedText);
    // Check if the pasted text contains special characters
    if (!/^[a-zA-Z\s]*$/.test(sanitizedValue)) {
      return;
    }
    document.execCommand('insertText', false, sanitizedValue);
  }
}
