@import "../../../../styles";
@import "../../../theme/sass/auth";
@import "../../users/users";

.form-control[readonly] {
  color: #464a4c;
  cursor: not-allowed;
  border: 1px solid rgba(0, 0, 0, 0.15);
}

select:hover {
  cursor: pointer;
}
.bottom-padding {
  padding-bottom: 30px;
  padding-right: 15px;
  padding-left: 15px;
  .user-role-label {
    margin: 5px 26px 0 5px;
    cursor: pointer;
  }
  .user-role {
    .btn:disabled {
      cursor: not-allowed;
      opacity: 0.9;
    }
  }
  .select-note {
    font-size: 12px;
    position: relative;
    top: 10px;
    left: 2px;
  }
}

.common-container {
  margin: 60px 2.5% 50px 2.5%;
  padding: 15px;
  background: white;
  min-width: 95%;
  border-radius: $border-radius;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.1);
  .profile-images {
    font-size: 15px;
    color: #777777;
  }
  .input-button {
    text-align: center;
    margin: 1.9% 0 2% 0;
  }
  .padding-left-right {
    padding-left: 30px;
    padding-right: 30px;
  }
  .form-control {
    border-radius: $border-radius;
  }
  .form-control[disabled] {
    color: grey;
    border-color: lightgrey;
  }
  .disable-submit {
    opacity: 0.8;
    cursor: not-allowed;
    &:hover {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }
  .profile-content {
    margin-top: 2%;
    margin-right: 0;
    .profileForm {
      margin-left: 0;
      .width-100 {
        width: 100%;
      }
      .personal-information-section {
        width: 100%;
        float: left;
        padding: 15px;
        margin: 15px 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        .bottom-padding {
          padding-bottom: 20px;
          div {
            .role-dropdown {
              height: 35px;
            }
          }
        }
        .bottom-padding-user-role-status {
          padding-bottom: 20px;
        }
        .top-padding {
          padding-top: 30px;
        }
        .border-bottom {
          border-bottom: 1px solid rgba(0, 0, 0, 0.3);
        }
      }
      .padding-top-0 {
        padding-top: 0;
      }
      .margin-top-0 {
        margin-top: 0;
      }
    }
    .info-title {
      font-weight: 700;
      color: #374767;
      float: left;
      width: 100%;
      margin: 0 0 0 30px;
      padding: 0;
      font-size: 15px;
      text-transform: uppercase;
      opacity: 0.9;
    }
    .disable-dropdown {
      cursor: not-allowed;
    }
  }
  .btn-style {
    float: right;
    width: auto;
    font-size: 12px;
    min-width: 150px;
    margin-right: 10px;
    height: auto;
    @media screen and (max-width: 767px) {
      width: 80%;
    }
  }
  .small {
    font-size: 100%;
  }
  .profileDiv {
    padding-bottom: 25px;
  }
  .disable-submit {
    opacity: 0.7;
    cursor: not-allowed;
    &:hover {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
  .help-block {
    color: red;
  }
  .sub-little-text {
    font-size: 12px;
  }
}

.confirmUserActiveContainer {
  .confirmButtonsContainer {
    width: 100%;
    padding: 10px 0 0 0;
    float: left;
    .image-section {
      height: 100%;
      overflow: scroll;
      img {
        width: 100%;
      }
    }
  }
  .confirmButtonsContainer.confirmationModalPopUp {
    height: 300px;
  }
  .floating-preview-buttons {
    float: left;
    width: 30%;
    margin: 0 35%;
    position: absolute;
    bottom: -60px;
    left: 0;
    display: flex;
    justify-content: space-evenly;
    .cross {
      position: relative;
      border: 3px solid #fff;
      height: 30px;
      width: 30px;
      right: -12px;
      top: -11px;
      border-radius: 25px;
      background: #fff;
      .fa {
        position: absolute;
        right: 3px;
        font-size: 22px;
        cursor: pointer;
        color: #102d69;
        font-style: normal;
      }
    }
  }
}

@media only screen and (min-device-width: 310px) and (max-device-width: 320px) {
  .profileForm {
    padding-right: 0 !important;
  }
}

@media only screen and (min-width: 320px) and (max-width: 767px) {
  .profileForm {
    .upload-main-document {
      .upload-section {
        .col-md-4 {
          &:last-child {
            padding-right: 15px;
          }
        }
      }
      .file-upload {
        .drag-text {
          h3 {
            margin: 0 20px;
          }
        }
      }
    }
  }
  .common-container {
    .input-button {
      display: flex;
      .btn-cancel-style {
        min-width: 120px;
        margin-right: 10px !important;
      }
    }
    .confirmUserActiveContainer {
      .confirmButtonsContainer {
        height: 250px;
      }
    }
  }
}
