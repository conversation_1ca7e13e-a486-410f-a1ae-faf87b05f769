import { ComponentFixture, TestBed} from '@angular/core/testing';
import {Users} from './users.component';
import { waitForAsync } from '@angular/core/testing';

describe('Users', () => {
    let component: Users;
    let fixture: ComponentFixture<Users>;

    beforeEach( waitForAsync(() => {
        TestBed.configureTestingModule({
    imports: [Users]
}).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(Users);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});