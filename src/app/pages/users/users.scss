@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";
@import "../../theme/sass/conf/variables";

$font-size: 13px;

.user-tab-width {
  width: 33.33%;
}

.report-tab-width {
  width: 50%;
}

.overflow-y-operator {
  overflow-y: -webkit-paged-x !important;
}

.user-container {
  width: 100%;
  padding-left: 15px;
  overflow-y: hidden;

  .width-dd {
    width: 27%;
  }

  .user-grid-container {
    width: 98%;
    float: left;
    border-radius: $border-radius;
    margin-top: 60px;

    .user-grid-data-parent-container {
      background: #ffffff;
      overflow-y: auto;
    }

    .user-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;

      .left-align {
        float: left;
        width: 100%;
      }

      .main-campaign {
        padding-top: 20px;

        .panel {
          .panel-body {
            .wizard {
              min-width: 80%;
              float: left;
              background: #fff;
              margin: 0;
              margin-top: -22px;
              font-size: 14px;

              @media screen and (max-width: 500px) {
                font-size: 8px;
              }

              @media screen and (max-width: 768px) {
                font-size: 12px;
              }

              .profile-tab {
                float: left;
                text-decoration: none;
                text-align: center;
                border-bottom: 3px solid #c6c6c6;
                height: 50px;
                line-height: 50px;
                cursor: pointer;
                font-size: 14px;
                color: #c6c6c6;

                &:last-child {
                  border-right: none;
                }

                a {
                  text-transform: uppercase;
                  text-decoration: none;

                  .profile-icon {
                    margin-right: 10px;
                    color: #195c94;
                  }
                }
              }
            }

            .current-tab {
              font-size: 14px;
              border-bottom: 3px solid $button-color;
              color: #195c94;

              @media screen and (max-width: 500px) {
                font-size: 8px !important;
              }

              @media screen and (max-width: 768px) {
                font-size: 12px !important;
              }
            }
          }
        }
      }

      .user-table {
        font-size: 15px;
        min-width: 100%;
        overflow-y: hidden;

        .custyle {
          width: 100%;
          margin-top: 15px;

          .custab {
            width: 100%;

            .table-head {
              color: #0059b3;
              font-size: 13px;
              font-weight: bold;
              text-align: center;

              .head-row {
                font-weight: bold;
                text-align: left;
                height: 40px;
                line-height: 18px;
              }

              th {
                &:nth-child(2) {
                  width: 160px;
                }
              }
            }
          }

          h2 {
            font-weight: 700;
            color: #4d5a77;
            float: left;
            padding: 0;
            font-size: 14px;
            text-transform: uppercase;
            margin-bottom: 15px;
          }
        }

        .table-row-client {
          font-size: 12px;
          color: #0059b3;
          height: 40px;
          cursor: pointer;

          &:hover {
            background: rgba(4, 160, 222, 0.23);
          }
        }

        .table-row {
          font-size: 12px;
          color: $table-color;
          height: 40px;
        }

        .border-row {
          border: 1px solid $table-border-color;
          text-align: center;
          padding: 10px;

          .add-edit {
            margin: 0 5px;
            cursor: pointer;
            font-size: 16px;

            .fa-trash {
              font-size: 16px;
              color: $trash-color;
            }

            .fa-window-close-o {
              font-size: 16px;
              color: $trash-color;
            }

            .fa-check-square {
              font-size: 16px;
              color: #7ab675;
            }
          }

          .show-cursor {
            cursor: pointer;
            text-decoration: underline;

            &:hover {
              font-weight: bold;
            }
          }
        }

        .min-action-width {
          min-width: 110px;
        }

        .align-text-left {
          text-align: left;
          max-width: 150px;
          word-wrap: break-word;
        }
      }

      .user-filter-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 13px;

        .left-column {
          flex: 1;

          .wizard {
            display: flex;
            min-width: 70%;
            background: #fff;

            .profile-tab {
              display: flex;
              align-items: center;
              text-decoration: none;
              border-bottom: 3px solid #c6c6c6;
              padding: 10px 15px;
              cursor: pointer;

              &:last-child {
                border-right: none;
              }

              img {
                margin-left: 5px;
                height: 17px;
                width: 22px;
              }

              span {
                font-size: 14px;
                font-weight: 500;
                color: #C6C6C6;

                @media screen and (max-width: 768px) {
                  font-size: 12px;
                }
  
                @media screen and (max-width: 500px) {
                  font-size: 8px;
                }
              }
            }

            .active {
              span {
                font-weight: 600;
                color: #FF8033;

              }
              border-bottom: 3px solid $button-color;
            }
          }

          .wizard .active {
            border-bottom: 3px solid #FF8033;
            color: #FF8033;
          }

          .current-tab {
            font-size: 14px;
            border-bottom: 3px solid $button-color;
            color: #195c94;

            @media screen and (max-width: 500px) {
              font-size: 8px !important;
            }

            @media screen and (max-width: 768px) {
              font-size: 12px !important;
            }
          }
        }

        .right-column {
          flex: 1;
          display: flex;
          justify-content: flex-end;

          .search-container {
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: flex-end;

            .export-button button {
              width: 38px;
              height: 37px;
              // background-color: #fff;
              border-radius: 0.25rem;
              border: 1px solid #FF8033;
              background: #FF8033;
              color: #fff;
            }

            .add-button button {
              width: 38px;
              height: 37px;
              background-color: #FF8033;
              border-radius: 0.25rem;
              border: 1px solid #FF8033;
              color: #fff;
            }

            .input-group {
              display: flex;
              justify-content: flex-end;
              margin-bottom: 0;

              .search-input {
                display: flex;
                align-items: center;
                width: 50%;

                .input-group-add {
                  padding: 9px 10px;
                  margin-bottom: 0;
                  font-size: 1rem;
                  font-weight: 400;
                  line-height: 1;
                  color: #464a4c;
                  text-align: center;
                  background-color: #fff;
                  border: 1px solid rgba(0, 0, 0, 0.15);
                  border-radius: 0.25rem;
                  width: 85%;
                  margin-left: 11%;
                  border-color: #FF8033;
                  display: flex;

                  img {
                    height: 18px;
                    cursor: pointer;
                  }

                  i {
                    float: left;
                  }

                  input {
                    border: none;
                    width: 85%;
                    outline: none;
                    font-size: 14px;
                  }

                  input:focus {
                    border: none;
                  }
                }

                input {
                  margin-right: 10px;
                  margin-left: 10px;
                }
              }

              .export-button {
                margin-right: 10px;
              }
            }
          }

          // Media query for tablets (if needed)
          @media screen and (max-width: 768px) {
            .search-container {
              justify-content: center;
            }
          }
        }
      }
    }
  }
}

.panel-body[_ngcontent-c10] .wizard[_ngcontent-c10] .user-tab-width[_ngcontent-c10] {
  width: 25% !important;
}

.no-decoration {
  text-decoration: none;
}

.tractor-icon {
  padding-left: 5px;

  img {
    height: 14px;
    margin-bottom: 4px;
  }
}

.main-container {
  width: 100%;
  padding: 26px;
  max-height: 85vh;
  overflow-y: auto;
  .eyebrow-heading-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: -6% 0% 16% 0%;

    .top-container {
      position: relative;
      bottom: 8px;
      left: 10px;

      .cursor {
        cursor: pointer;
      }
    }

    .top-container i {
      margin-right: 5px;
      color: #0070ff;
    }

    .top-container p {
      display: inline;
      color: #0070ff;
    }

    .top-container-bottom {
      position: relative;
      bottom: 20px;

      .cursor {
        cursor: pointer;
      }
    }

    .top-container-bottom i {
      margin-right: 5px;
      color: #0070ff;
    }

    .top-container-bottom p {
      display: inline;
      color: #0070ff;
    }
  }

  .preview {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 0px;

    .top-container {
      position: relative;
      bottom: 20px;

      .cursor {
        cursor: pointer;
      }
    }

    .top-container i {
      margin-right: 5px;
      color: #0070ff;
    }

    .top-container p {
      display: inline;
      color: #0070ff;
    }
  }

  .container-fluid {
    height: 200px;
    overflow-y: scroll;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;

    .upload-container {
      width: 100%;
      height: 200px;
      display: grid;
      justify-content: center;
      text-align: center;
      background-color: #efefef;
      border-style: dotted;
      border-width: 1px;
      cursor: pointer;

      .upload-icon {
        display: grid;
        align-items: end;
      }
    }
  }

  .preview-fluid {
    .preview-fluid-container {
      margin: -55px 0px 0px 0px;
    }

    height: 300px;
    display: flex;
    justify-content: center;
    align-items: start;
    text-align: center;

    .upload-container {
      width: 50%;
      height: 250px;
      display: grid;
      justify-content: center;
      text-align: center;
      background-color: #efefef;
      cursor: pointer;

      .upload-icon {
        display: grid;
        align-items: end;
      }
    }

    .upload-button {
      display: flex;
      justify-content: space-between;

      .add {
        width: 38px;
        height: 37px;
        background-color: #fff;
        border-radius: 0.25rem;
        border: none;
        margin: 0px 0px 10px 560px;

        img {
          height: 28px;
          margin: 16px 0px 0px -8px;
          cursor: pointer;
        }
      }

      .name-container {
        margin-top: 24px;
        color: #0070ff;
      }
    }

    .container-preview {
      height: 344px;
      overflow-y: scroll;
    }
  }

  .custom-menu {
    width: 25% !important;
    height: 300px !important;
  }

  .body-container {
    margin-top: -30px;

    h4 {
      display: flex;
      justify-content: center;
      color: #FF8033;
      font-family: $sans-font-family;
      font-weight: 600;
    }
  }

  .msg-container {
    p {
      display: flex;
      justify-content: center;
      color: #000000;
      font-family: $sans-font-family;
    }
  }

  .heading-container {
    color: #FF8033;
    font-family: $sans-font-family;

    h4 {
      font-weight: 600;
      font-family: $sans-font-family;
    }
  }

  .form-input1-container {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-bottom: 15px;

    span {
      .input-field {
        font-size: 14px;
        font-weight: 400;
        line-height: 1;
        color: #464a4c;
        padding-left: 10px;
        background-color: #fff;
        border: 1px solid #CCC;
        border-radius: 0.25rem;
        height: 38px;
        width: 100%;
      }
      .input-field:disabled {
        border-color: #ccc;
        color: #a7a4a4;
        cursor: not-allowed !important;  
      }
    }

    /* Media query for smaller screens, adjust as needed */
    @media screen and (max-width: 768px) {
      .form-input1-container {
        flex-direction: column;
        align-items: center;
      }
    }

    .selected-list .countplaceholder {
      position: absolute;
      right: 50px;
      top: 50%;
      transform: translateY(-50%);
      opacity: 0;
    }
    .label {
       font-size: 14px;
       font-weight: 500;
      }
  }

  .button-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 5%;

    button {
      height: 40px;
      width: 30%;
      border: none;
      border-radius: 5px;
    }

    .btn-cancel {
      background-color: #fff;
      color: #FF8033;
      font-family: sans-serif;
      font-weight: 600;
      border: 1px solid #FF8033;
    }

    .button-width {
      background-color: #fff;
      color: #FF8033;
      font-family: sans-serif;
      font-weight: 600;
      border: 1px solid #FF8033;
      width: 18%;
    }

    .btn-submit {
      background-color: #FF8033;
      color: #fff;
      font-family: $sans-font-family;
      font-weight: 600;
    }

    .btn-submit-width {
      background-color: #FF8033;
      color: #fff;
      font-family: $sans-font-family;
      font-weight: 600;
      width: 18%;
    }
  }
}

.brands-multi-select .cuppa-dropdown .selected-list .c-btn {
  border-radius: 5px;
  padding: 8px;
  border-color: #FF8033;
}

.error-message {
  color: red;
}

.disable-submit {
  opacity: 0.7;

  cursor: not-allowed;

  &:hover {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.mat-paginator-container .mat-paginator {
  margin-right: auto;
}

::ng-deep .selected-list .c-list .c-token {
  width: 75%;
  list-style: none;
  border-radius: 2px;
  margin-right: 4px;
  float: left;
  position: relative;
  padding: 4px 22px 4px 8px;
}

::ng-deep .selected-list .c-list .c-token .c-label {
  display: block;
  float: left;
  position: relative;
  top: 0;
}

::ng-deep .c-btn {
  border-color: #CCC !important;
}

.upload-btn-container {
  opacity: 0;
}

.filter-menu-container {
  .filter-heading {
    margin: 10px 15px;
    font-family: $sans-font-family;
  }

  .filter-container {
    width: 90%;
    margin: 0 14px;

    .district-filter-container {
      margin-top: 10px;
      // .customer-dd .c-btn .c-remove.clear-all {
      //   display: none !important;
      // }  
    }

    .selected-list .countplaceholder {
      position: absolute;
      right: 3px;
      top: -34%;
      transform: translateY(-50%);
    }
  }

  .button-container {
    display: flex;
    justify-content: center;
    gap: 10px;
    width: 100%;
    padding: 10% 6px 0px 13px;

    button {
      height: 35px;
      border: none;
      border-radius: 5px;
    }

    .btn-cancel {
      background-color: #fff;
      color: #FF8033;
      font-family: sans-serif;
      font-weight: 600;
      width: 100%;
      border: 1px solid #FF8033;
    }

    .btn-submit {
      background-color: #FF8033;
      color: #fff;
      font-family: $sans-font-family;
      font-weight: 600;
      width: 100%;
    }

    .sbt-button-width {
      background-color: #808080;
      color: #fff;
      font-family: $sans-font-family;
      font-weight: 600;
      width: 18%;
    }
  }
}

:host ::ng-deep .selected-list .c-list .c-token {
  font-size: 11px;
}

:host ::ng-deep .selected-list .c-btn {
  width: 100%;
}

.FilterTitleSize {
  font-size: 20px;
  font-family: sans-serif;
  color: #222222;
  font-weight: bold;
}

.fixed-header {
  position: sticky;
  top: 0;
  z-index: 1;
}

.mat-menu-item {
  justify-content: center !important;
  align-items: center !important;
  display: flex !important;
  height: 34px !important;
}
h3 {
  margin: 2rem 0 1rem;
  font-size: 1.3rem;
  font-weight: 600;
  color: #222;
}

.upload-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  // padding: 0% 10%; // You might adjust padding for better layout control

  .upload-box {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #CCC;
    text-align: left;
    padding: 1.2rem;
    border-radius: 8px;
    color: #FF8033;
    font-weight: 500;
    background: #fff7f0;
    cursor: pointer;
    transition: all 0.3s ease;
    
    .icon {
      font-size: 2rem;
      margin-right: 1rem;
    }
    
    small {
      font-size: 0.8rem;
      color: #999;
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px;
    }
    
    div small {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px;
      display: inline-block;
    }
  }
}
.multiple-login-section {
  margin-top: 20px;
  margin-bottom: 20px;
  padding-right: 6px;
  
  .enable-multiple-login {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 22px;

    input[type="checkbox"] {
      width: 20px;
      height: 20px;
      bottom: 6px;
      margin-right: 6px;
      cursor: pointer;
      appearance: none;
      border: 2px solid gray;
      border-radius: 3px;
      background-color: #fff;
      position: relative;
    }
    

    input[type="checkbox"]:checked {
      background-color: #FF8033;
      border: 2px solid #FF8033;
    }

    input[type="checkbox"]:checked::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 6px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }

    .enable-login-label {
      margin-top: -5px;
      font-size: 16px;
      font-weight: bold;
      color: #FF8033;
      cursor: pointer;
    }
  }
  
  .login-users-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .login-user-row {
    display: flex;
    gap: 15px;

    .main-form{
      display:inline;
      .form-group {
        flex: 1;
        margin-bottom: 1px !important;
        flex-direction: column;
        .mobile-section {
          display: flex;
        }
        label {
          margin-bottom: 5px;
          font-size: 14px;
          color: #000000;
          
          .required {
            color: red;
          }
        }
        
        .input-field {
          width: 100%;
          padding: 8px;
          border: 1px solid #ccc;
          border-radius: 4px;
        }
      }
    }  
    // .error-message  .add-btn, .action-btns {
    //   margin-top: 0px !important;
    // }
    
    .main-button-section{
      display: flex;
      align-items: center;
      justify-content: center;
      max-height: 67px;
      .add-btn, .action-btns {
        display: flex;
        gap: 5px;
        align-self: center;
        margin-top: 24px;
      }
      
      .add-user-btn, .remove-user-btn {
        width: 38px;
        height: 38px;
        border-radius: 4px;
        border: 2px solid #FF8033;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #FF8033;
        background: white;
        transition: all 0.3s ease;
        margin: auto;
      }
      
      .add-user-btn:hover {
        background-color: #FFF7F0;
      }
  
      .remove-user-btn:hover {
        background-color: #FFE9E9;
      }
    }
  }
    }
   

::ng-deep .app-user .col-md-4 {
  flex: 0 0 33% !important;
}
.add-btn-container{
display: flex;
height: 40px;
margin: 10px;
.addSchemeButton {
  height: 37px;
  background-color: #ff8033;
  border-radius: 0.25rem;
  border: 1px solid #ff8033;
  color: #fff;
  padding: 0px 14px 0 14px;
  width: 30%;
}
.addSchemeButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
}

.button-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  .close-btn {
    width: 10%;
    padding: 8px 16px;
    margin-bottom: 10px;
    background-color: #ff6200;
    color: white;
    border: none;
    border-radius: 3px;
    font-size: 14px;
    cursor: pointer;
  }
}

.upload-container {
  // max-width: 400px;
  // margin: 20px auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: Arial, sans-serif;

  h2 {
    font-size: 18px;
    margin-bottom: 20px;
  }

  .date-section {
    margin-bottom: 20px;

    .date-input {
      label {
        display: block;
        font-size: 14px;
        margin-bottom: 5px;
      }

      .date-row {
        display: flex;
        gap: 10px;

        .date-field {
          flex: 1;

          label {
            font-size: 12px;
            color: #666;
          }

          input {
            width: 100%;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .file-upload-section {
    margin-bottom: 20px;

    label {
      display: block;
      font-size: 14px;
      margin-bottom: 5px;
    }

    .file-upload-box {
      max-width: 175px;
      display: flex;
      align-items: center;
      padding: 10px;
      background-color: #ffe6e6;
      border: 1px solid #ddd;
      border-radius: 3px;

      .file-icon {
        margin-right: 10px;
        font-size: 10px;
        background: #fff;
        padding: 5px;
        border-radius: 50px
      }
      .file-name {
        cursor: pointer;
      }
      .pdf-image{
        background: #ffffff;
        border-radius: 10;
      }

      .file-name {
        font-size: 14px;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis; 
        max-width: 150px;
        display: inline-block;   
        vertical-align: middle;
      }
    }

    .file-input {
      display: none;
    }
  }

  .button-section {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;

    .cancel-btn {
      width: 30%;
      padding: 8px 16px;
      background-color: transparent;
      border: 1px solid #ddd;
      border-radius: 3px;
      font-size: 14px;
      cursor: pointer;
    }

    .submit-btn {
      width: 30%;
      padding: 8px 16px;
      background-color: #ff6200;
      color: white;
      border: none;
      border-radius: 3px;
      font-size: 14px;
      cursor: pointer;
    }
  }
}

.modal-bottom-section {
  padding: 20px 50px 0 50px;
  margin-bottom: 40px;
}
.form-section {
  .section-header {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      border-bottom: 1px solid #ddd;
  }

  &.grower-section {
      margin: 25px 0;

      .checkbox-container {
          display: flex;
          align-items: center;
          gap: 12px;
          width: max-content;
          position: relative;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          cursor: pointer;

          input {
              position: absolute;
              opacity: 0;
              cursor: pointer;
              height: 0;
              width: 0;

              &:checked~.checkmark {
                  background-color: #FF8033;
                  border: none;
              }

              &:checked~.checkmark:after {
                  display: block;
              }
          }

          .checkmark {
              height: 20px;
              width: 20px;
              border: 3px solid #1C1B1F;
              border-radius: 3px;

              &:after {
                  content: "";
                  position: absolute;
                  display: none;
                  left: 6px;
                  top: 2px;
                  width: 8px;
                  height: 12px;
                  border: 3px solid white;
                  border-width: 0 3px 3px 0;
                  -webkit-transform: rotate(45deg);
                  -ms-transform: rotate(45deg);
                  transform: rotate(45deg);
              }
          }

          .grower-text {
              font-size: 16px;
              font-weight: 500;
              line-height: 100%;
              color: #222222;
          }
      }
  }

  &.input-grid {
      display: flex;
      gap: 20px;

      .form-group {
          display: flex;
          flex-direction: column;

          label {
              font-size: 18px;
              color: #222222;
              font-weight: 400;
          }

          input[type="text"] {
              padding: 10px;
              border: 1px solid #1A3661;
              border-radius: 4px;
              font-size: 18px;
              font-weight: 500;
              color: #CACACA;
              width: 286px;
          }
      }
  }

  &.set-target-section {
      .target-values {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          margin-top: 28px;

          .target-item {
              display: flex;
              align-items: center;
              gap: 1rem;

              label {
                  width: 160px;
                  font-weight: 500;
                  font-size: 16px;
                  display: inline-block;
                  color: #222222;
              }

              input[type="text"],
              input[type="number"] {
                  padding: 0.4rem 0.6rem;
                  border: 1px solid #1A3661;
                  border-radius: 4px;
                  width: 110px;
              }

              .percentage-input {
                  display: flex;
                  align-items: center;
                  gap: 0.2rem;
              }

              .amount-display {
                  display: flex;
                  align-items: center;
                  gap: 10px;
                  margin-left: 15px;

                  span {
                      font-size: 16px;
                      font-weight: 400;
                      color: #222222;
                  }

                  input[type="text"] {
                      width: 80px;
                  }
              }
          }
      }
  }

  &.target-duration-section {
      .date-inputs {
          display: flex;
          gap: 20px;
          margin-top: 16px;

          .date-group {
              display: flex;
              flex-direction: column;
              gap: 8px;

              label {
                  font-size: 16px;
                  color: #000000;
                  font-weight: 500;
                  line-height: 100%;
                  letter-spacing: 0%;
              }

              .date-picker {
                  position: relative;

                  input {
                      padding: 10px;
                      border: 1px solid #1A3661;
                      border-radius: 4px;
                      font-size: 14px;
                      color: #333;
                      width: 175px;
                      cursor: pointer;
                  }

                  mat-datepicker-toggle {
                      position: absolute;
                      right: 0;
                      top: 50%;
                      transform: translateY(-50%);
                      color: #FF6B00;
                  }
              }
          }
      }
  }
}
button.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
}
