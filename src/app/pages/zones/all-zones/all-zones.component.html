<div class="zone-container">
  <div class="zone-grid-container">
    <nga-app-filter
      (onSearchValue)="onSearch($event)"
      [configurations]="showOtherFilters"
      [addButton]="userButton"
      (onExport)="onExport()"
    >
    </nga-app-filter>

    <div class="zone-grid-data-parent-container">
      <div id="foo" class="zone-grid-data-container">
        <div class="zone-table">
          <dynamic-table
            [tableHeads]="tableHead"
            [tableData]="zoneData"
            [tableConfiguration]="configurationSettings"
            [tableColName]="tableColName"
            (pageChange)="getZonesPageData($event)"
            [showIndex]="showIndex"
          >
          </dynamic-table> 
<!-- <table class="table">
  <thead>
    <tr>
      <th *ngFor="let header of tableHead">{{ header }}</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let rowData of zoneData"> 
      <td>{{ rowData.id }}</td>
      <td>{{ rowData.name }}</td>
      <td class="actionColumn"><i 
        class="fa fa-check check-icon"
        title="Active"
        aria-hidden="true" ></i></td> 
    </tr>
  </tbody>
</table> -->

        </div>
      </div>
    </div>
  </div>
</div>
