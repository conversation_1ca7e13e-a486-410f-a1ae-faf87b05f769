import {
  Component,
  ViewEncapsulation,
  OnInit,
  AfterViewInit,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";

import { GlobalEvents } from "../../../helpers/global.events";
import { ZonesService } from "../../../app-services/zones-service";
import { AppConstant } from "../../../constants/app.constant";
import { AuthenticationHelper } from "../../../helpers/authentication";
import { ngxCsv } from "ngx-csv";
import { DynamicTableComponent } from "../../../shared/data-table/data-table.component";
import { FilterComponent } from "../../../shared/filter/filter.component";
import { BaThemePreloader } from "src/app/theme/services/baThemePreloader/baThemePreloader.service";
import { BaMenuService } from "src/app/theme/services/baMenu/baMenu.service";
import { DatePipe } from "@angular/common";
import {MatIconModule} from '@angular/material/icon';
import { DashboardService } from "src/app/app-services/dashboard.service";

@Component({
    selector: "nga-all-zones",
    encapsulation: ViewEncapsulation.None,
    styleUrls: ["./../zones.scss"],
    templateUrl: "all-zones.component.html",
    imports: [FilterComponent, DynamicTableComponent, MatIconModule],
    providers: [DatePipe, BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class AllZonesComponent implements OnInit, AfterViewInit {
  zoneData: any = [];
  exportData: any = [];
  tableHead: any = [];
  tableColName: any = [];
  showIndex: any = { index: null };
  configurationSettings: any = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    actionsColumnName: "Active Area",
    noDataMessage: "No data found",
    showStatus: true,
    showEdit: false,
    changeStatus: false,
  };
  isSearch: boolean = false;
  searchedValue: string = "";
  PCCode!: string;

  showOtherFilters: any = {
    showRadioFilters: false,
    showSearch: true,
    add: false,
    showdropdown1Filters: true,
    showdropdown1Title: "",
    showdropdown2Filters: false,
    showSearchiconFilters: false,
    showReport: false,
    export: true,
  };
  userButton: any = [];
  isAdmin: boolean = false;
  constructor(
    private routes: ActivatedRoute,
    private spinner: BaThemeSpinner,
    public toastr: ToastrService,
    private events: GlobalEvents,
    private zonesService: ZonesService,
    private router: Router,
    private dashboardService  :DashboardService
  ) {
    this.isAdmin = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;

    this.showOtherFilters.showdropdown1Title = "zones";
  }
  ngOnInit() {
    this.setTableHeader();
    this.commonAddButton();
    window.scrollTo(0, 0);
    this.spinner.hide();
  }
  ngAfterViewInit(): void {
    this.events.setChangedContentTopText("Zones");
    this.events.onPCSelect.subscribe((item) => {
      this.configurationSettings.currentPage = 0;
      if (item && item.tab == "zones") {
        if (item && item.pcCode != "all") {
          this.PCCode = item.pcCode;
          this.getAllZones();
        } else {
          this.PCCode = "";
          this.getAllZones();
        }
      }
    });
  }
  setTableHeader(): void {
    this.tableHead = ["Zone Code", "Zone Name" ];
    this.tableColName = ["code", "name"];
    this.zoneData = [];
    const pc = String(localStorage.getItem("profitCenter"));
    if (this.isAdmin) {
      this.PCCode = "";
    } else {
      if (pc) {
        this.PCCode = pc;
      }
    }
    this.getAllZones();
  }

  /**
   * Method for getting the list of zones
   * @param page
   */

  getAllZones(page?: number): void {
    let data = {
      pageLimit: AppConstant.PER_PAGE_ITEMS,
      currentPage: page ? page - 1 : 0,
      searchedValue: encodeURIComponent(this.searchedValue),
      profitCenterCode: this.PCCode ? this.PCCode : "",
    };
    this.spinner.show();
    this.zonesService.getAllZones(data).subscribe({
      next: (zoneData: any) => { 
        zoneData = this.dashboardService.removeSuffix(zoneData); 
        this.zoneData = [];
        if (zoneData && zoneData.content && zoneData.content.length) {
          zoneData.content.forEach((zoneInfo: any) => {
            const zoneInfoObj = {
              code: zoneInfo.code,
              id: zoneInfo.id,
              name: zoneInfo.name,
              profit_center: zoneInfo.profitCenterName,
              sbu: zoneInfo.sbuName,
              is_active: zoneInfo.active = true,
            };
            this.zoneData.push(zoneInfoObj);
          });
          this.configurationSettings.totalRecordCount = zoneData.totalElements;
        } else {
          this.configurationSettings.totalRecordCount = 0;
        }
        this.spinner.hide();
        this.events.setChangedContentTopText(
          "Zones (" + this.configurationSettings.totalRecordCount + ")"
        );
      },
      error: (errorResponse) => { 
        let errorMsg = errorResponse.status;
        if (+errorMsg ===  401 || +errorMsg ===  404) {  
          localStorage.clear();
          this.router.navigate([""]);
          this.toastr.success("Signed Out Successfully");
        } else if(errorResponse.message){ 
          this.toastr.error(AppConstant.ZONE_FETCH_ERROR);
        } 
        this.spinner.hide();
      },
    });
  }

  /**
   * To display the result on basis of search query
   * @param event
   */
  onSearch(event: any): void {
    if (event.trim()) {
      this.isSearch = true;
      this.searchedValue = event.trim();
      this.getZonesPageData(1);
    } else {
      if (this.isSearch) {
        this.isSearch = false;
        this.searchedValue = "";
        this.getZonesPageData(1);
      }
    }
  }

  /**
   * Zones page change event
   * @param page
   */
  getZonesPageData(page: number): void {
    this.configurationSettings.currentPage = page;
    this.getAllZones(this.configurationSettings.currentPage);
  }

  commonAddButton(): void {
    this.userButton = [
      {
        path: "/zones/add-zone",
        title: " Add Zone",
      },
    ];
  }

  /**
   * Method for exporting the data in CSV format
   * @param event
   */
  onExport() {
    this.getZonesExportData();
  }
  getZonesExportData(): void {
    window.scrollTo(0, 0);
    let data = {
      searchedValue: encodeURIComponent(this.searchedValue),
      profitCenterCode: this.PCCode ? this.PCCode : "",
    };
    this.spinner.show();
    this.zonesService.getAllZonesExportData(data).subscribe({
      next: (exportsData: any) => {  
        exportsData = this.dashboardService.removeSuffix(exportsData)
        this.exportData = [];
        if (exportsData && exportsData.content.length) {
          exportsData.content.forEach((exportInfo: any) => {
            let exportObj = { 
              code: exportInfo.id ? exportInfo.id : "---",
              name: exportInfo.name ? exportInfo.name : "---", 
              is_active: exportInfo.active ? 'No' : "Yes",
            };
            this.exportData.push(exportObj);
          });
          let options = {
            fieldSeparator: ",",
            quoteStrings: '"',
            decimalseparator: ".",
            showLabels: true,
            headers: [ 
              "Zone Code",
              "Zone Name", 
              "Is Active",
            ],
          };
          new ngxCsv(this.exportData, "Zone Details", options);
        }else{
          this.toastr.warning("No data available to export!");
        }
        this.spinner.hide();
      },
      error: () => {
        this.toastr.error(AppConstant.EXPORT_ERROR);
        this.spinner.hide();
      },
    });
    this.spinner.hide();

  }
  
}
