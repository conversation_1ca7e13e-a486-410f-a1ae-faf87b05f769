<div class="zone-form-container">
  <div class="zone-content">
    <form [formGroup]="zoneForm" (ngSubmit)="onSubmit(zoneForm.value)">
      <div class="col-md-12 row zone-form">
        <div>
          <h2 class="zone-info-title">Zone Information</h2>
          <div class="zone-information-section">
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Profit Center<i class="required">&nbsp;*</i></label
                >
                <select
                  class="form-control"
                  formControlName="profit_center"
                  [(ngModel)]="profitCenter"
                >
                  <option value="" hidden>Select Profit Center</option>
                  <option value="af">AF</option>
                  <option value="swal">SWAL</option>
                </select>
              </div>
            </div>
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Name<i class="required">&nbsp;*</i></label
                >
                <input
                  formControlName="name"
                  type="text"
                  class="form-control"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !zoneForm.get('name')?.valid &&
                    zoneForm.get('name')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >Zone name is required.</span
                >
              </div>
            </div>
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style"
                  >Code<i class="required">&nbsp;*</i></label
                >
                <input
                  formControlName="code"
                  type="text"
                  class="form-control"
                />
              </div>
              <div class="error-message">
                <span
                  *ngIf="
                    !zoneForm.get('code')?.valid &&
                    zoneForm.get('code')?.touched
                  "
                  class="help-block sub-little-error confpass"
                  >Zone code is required.</span
                >
              </div>
            </div>
            <div class="col-md-6 bottom-padding">
              <div>
                <label class="label-style">Status</label>
                <div>
                  <span class="zone-status">
                    <input
                      type="radio"
                      class="btn"
                      value="1"
                      name="status"
                      id="active"
                      formControlName="status"
                    />
                    <label for="active" class="zone-status-label">Active</label>
                  </span>

                  <span class="zone-status">
                    <input
                      type="radio"
                      class="btn"
                      value="0"
                      name="status"
                      id="inactive"
                      formControlName="status"
                    />
                    <label for="inactive" class="zone-status-label"
                      >Inactive</label
                    >
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="col-md-12 form-group input-button submit-button padding-left-right"
        >
          <button
            type="button"
            class="btn-cancel-style pull-left"
            (click)="location.back()"
          >
            CANCEL
          </button>
          <button
            [ngClass]="{ 'disable-submit': !zoneForm.valid }"
            [disabled]="!zoneForm.valid"
            type="submit"
            class="btn-style"
          >
            {{ zoneId ? "UPDATE" : "ADD ZONE" }}
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
