@import "../../../../styles";
@import "../../../theme/sass/auth";
@import "../../users/users";

.bottom-padding {
  padding-bottom: 30px;
  .zone-status-label {
    margin: 5px 26px 0 5px;
    cursor: pointer;
  }
  .zone-status {
    .btn:disabled {
      cursor: not-allowed;
      opacity: 0.9;
    }
  }
}

.zone-form-container {
  margin: 100px 2.5% 50px 2.5%;
  padding: 15px;
  background: white;
  min-width: 95%;
  border-radius: $border-radius;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.1);
  .input-button {
    text-align: center;
    margin: 1.9% 0 2% 0;
  }
  .padding-left-right {
    padding-left: 30px;
    padding-right: 30px;
  }
  .form-control {
    border-radius: $border-radius;
  }
  .form-control[disabled] {
    color: grey;
    border-color: lightgrey;
  }
  .disable-submit {
    opacity: 0.8;
    cursor: not-allowed;
    &:hover {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }
  .zone-content {
    margin-top: 2%;
    margin-right: 0;
    .zone-form {
      margin-left: 0;
      .zone-information-section {
        width: 100%;
        float: left;
        padding: 15px;
        margin: 15px 0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        .bottom-padding {
          padding-bottom: 20px;
        }
      }
    }
    .zone-info-title {
      font-weight: 700;
      color: #374767;
      float: left;
      width: 100%;
      margin: 0;
      padding: 0;
      font-size: 15px;
      text-transform: uppercase;
      opacity: 0.9;
    }
  }
  .btn-style {
    float: right;
    width: auto;
    font-size: 12px;
    min-width: 150px;
    height: auto;
    @media screen and (max-width: 767px) {
      width: 80%;
    }
  }
  .disable-submit {
    opacity: 0.8;
    cursor: not-allowed;
    &:hover {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }
  .help-block {
    color: red;
  }
}

@media only screen and (min-device-width: 310px) and (max-device-width: 320px) {
  .zone-form {
    padding-right: 0 !important;
  }
}

@media only screen and (min-width: 320px) and (max-width: 767px) {
  .zone-form-container {
    .input-button {
      display: flex;
      .btn-cancel-style {
        min-width: 120px;
        margin-right: 10px;
      }
    }
  }
}
