import { Component } from "@angular/core";
import { Location, NgIf, NgClass } from "@angular/common";
import { FormGroup, FormBuilder, Validators, FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { BaThemeSpinner } from "../../../theme/services/baThemeSpinner/baThemeSpinner.service";
import { Router, ActivatedRoute } from "@angular/router";
import { GlobalEvents } from "../../../helpers/global.events";
import { ZonesService } from "../../../app-services/zones-service";
interface ZoneFormValues {
  code: string;
  name: string;
  profit_center: string;
  status: string;
}
@Component({
    selector: "nga-add-zone",
    styleUrls: ["zone-form.component.scss"],
    templateUrl: "zone-form.component.html",
    imports: [FormsModule, ReactiveFormsModule, NgIf, NgClass,]
})
export class ZoneFormComponent {
  zoneForm!: FormGroup;
  zoneId: any;
  zoneData!: ZoneFormValues;
  profitCenter: string = "";
  constructor(
    private fb: FormBuilder,
    public location: Location,
    private router: Router,
    public events: GlobalEvents,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private activeRoute: ActivatedRoute,
    private zonesService: ZonesService
  ) {
    this.zoneId = this.activeRoute.snapshot.queryParams["id"];
  }
  ngOnInit() {
    this.setUserForm();
    if (this.zoneId) {
      this.getZoneByID();
    }
    window.scrollTo(0, 0);
    this.spinner.hide();
  }
  getZoneByID():void {
    this.zoneData = {
      code: "Z-101",
      name: "East Zone",
      profit_center: "swal",
      status: "1",
    };
    this.setUserForm(this.zoneData);
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.events.setChangedContentTopText(
        this.zoneId ? "Edit Zone" : "Add Zone"
      );
    }, 0);
  }


  setUserForm(values?: ZoneFormValues):void {
    this.zoneForm = this.fb.group({
      code: [
        values ? values.code : "",
        Validators.compose([Validators.required]),
      ],
      name: [
        values ? values.name : "",
        Validators.compose([Validators.required]),
      ],
      profit_center: [
        values ? (this.profitCenter = values.profit_center) : "",
        Validators.compose([Validators.required]),
      ],
      status: [
        values ? values.status : "",
        Validators.compose([Validators.required]),
      ],
    });
  }

  onSubmit(values: ZoneFormValues): void {
    this.spinner.show();
    if (this.zoneId) {
      let body = {
        code: values.code.trim(),
        name: values.name.trim(),
        profit_center: values.profit_center.trim(),
        status: values.status.trim(),
        zoneId: this.zoneId,
      };
      const addUpdateZone = this.zonesService.addUpdateZone(body);
      addUpdateZone.subscribe({
        next: () => {
          this.toastr.success("Updated Successfully");
          this.router.navigate(["zones"]);
          this.spinner.hide();
        },
        error: (errMsg: any) => {
          if (errMsg && errMsg.error) {
            this.toastr.error(errMsg.error.message);
          } else {
            this.toastr.error("Error while Updating");
          }
          this.spinner.hide();
        }
      });
    } else {
      let body = {
        code: values.code.trim(),
        name: values.name.trim(),
        profit_center: values.profit_center.trim(),
        status: values.status.trim(),
      };
      const addUpdateZone = this.zonesService.addZoneForm(body);
  
      addUpdateZone.subscribe({
        next: () => {
          this.toastr.success("Added successfully");
          this.router.navigate(["zones"]);
          this.spinner.hide();
        },
        error: (errMsg: any) => {
          if (errMsg && errMsg.error) {
            this.toastr.error(errMsg.error.message);
          } else {
            this.toastr.error("Error while adding data");
          }
          this.spinner.hide();
        }
      });
    }
    this.location.back();
  }
  
}
