@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";

$font-size: 13px;

.zone-container {
  width: 96%;
  margin: 45px 2.5% 15px 2.5%;
  overflow-y: hidden;
  .zone-grid-container {
    width: 99%;
    float: left;
    border-radius: $border-radius;
    margin-top: 30px;
    position: relative;
    .zone-grid-data-parent-container {
      min-height: 485px;
      background: #ffffff;
      overflow-y: auto;
    }
    .zone-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      // padding: 10px 15px 25px;

      .zone-table {
        font-size: 15px;
        min-width: 100%;
        overflow-y: hidden;
        border-radius: $table-border-radius;
      }
    }
  }
}

.confirmUserActiveContainer {
  @include confirmDialogueActiveInActive();
}

.modal-backdrop.fade {
  opacity: 0.6;
}

.confirmUserActiveContainer .fade {
  opacity: 1 !important;
}

.disable-submit {
  opacity: 0.8;
  cursor: not-allowed;
  &:hover {
    opacity: 0.8;
    cursor: not-allowed;
  }
}
.actionColumn{
  cursor: not-allowed;
}


// .zone-table {
//   table {
//     border: 1px solid #ddd;

//     tr {
//       td {
//         &:nth-child(1) {
//           width: 15%;
//           text-align: center;
//         }

//         &:nth-child(3) {
//           width: 15%;
//           text-align: center;
//         }

//         border: 1px solid #ddd;
//         text-align: center;
//         word-wrap: break-word;
//         font-size: 12px;
//         color: #0059b3;
//       }

//       /* Apply background color to odd rows */
//       &:nth-child(odd) {
       
//         background-color: #ddd;
//       }
//     }

//     th {
//       border: 1px solid #ddd;
//       text-align: center;
//       color: #0059b3;
//       font-size: 13px;
//       font-weight: 700;
//     }

//     /* Apply background color to even rows */
//     tr:nth-child(even) {
//       background-color: rgba(0, 0, 0, 0.1);
//     }
//   }
// }

.check-icon{
  color: #00cc3a;
  font-size: 16px;
}