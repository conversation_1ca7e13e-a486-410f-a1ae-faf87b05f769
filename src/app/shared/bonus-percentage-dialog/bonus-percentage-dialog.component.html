<div class="popup-container">
  <h2>Add Bonus %</h2>
  
  <div class="input-field">
    <input 
      type="text" 
      min="0"
      step="any"
      placeholder="Write Bonus % here" 
      [ngModel]="bonusPercentage"
      (ngModelChange)="bonusPercentage = $event"
      (keydown)="validateNumberInput($event)"
      (input)="validateRange($event)"
      required>
  </div>

  <div class="date-section">
    <h3>Select Date</h3>
    <div class="date-fields">
      <div class="date-field">
        <label>Start Date</label>
        <div class="date-input">
          <input 
            [matDatepicker]="startPicker" 
            [(ngModel)]="startDate" 
            [min]="minDate" 
            readonly
            (dateChange)="onStartDateChange($event)">
          <mat-datepicker-toggle [for]="startPicker">
            <mat-icon>calendar_today</mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker #startPicker></mat-datepicker>
        </div>
      </div>

      <div class="date-field">
        <label>End Date</label>
        <div class="date-input">
          <input [matDatepicker]="endPicker" [(ngModel)]="endDate" [min]="startDate" readonly>
          <mat-datepicker-toggle [for]="endPicker">
            <mat-icon>calendar_today</mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker #endPicker></mat-datepicker>
        </div>
      </div>
    </div>
  </div>

  <div class="button-group">
    <button class="btn-cancel" (click)="onCancel()">Cancel</button>
    <button class="btn-add" [disabled]="!bonusPercentage || bonusPercentage <= 0" (click)="onAdd()">Add</button>
  </div>
</div>
