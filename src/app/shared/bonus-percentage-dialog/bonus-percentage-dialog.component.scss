.popup-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;  // Reduced from 500px
  font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif;

  h2 {
    font-size: 18px;  // Reduced from 20px
    color: #333;
    margin: 0 0 20px 0;  // Reduced from 24px
    font-weight: 500;
  }

  .input-field {
    margin-bottom: 20px;  // Reduced from 24px
    
    input {
      width: 100%;
      padding: 10px;  // Reduced from 12px
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 14px;
      
      &::placeholder {
        color: #999;
      }

      &:focus {
        outline: none;
        border-color: #FF6B00;
        box-shadow: 0 0 0 1px #FF6B00;
      }
    }
  }

  .date-section {
    margin-bottom: 24px;

    h3 {
      font-size: 16px;
      color: #333;
      margin: 0 0 16px 0;
      font-weight: 500;
    }

    .date-fields {
      display: flex;
      gap: 16px;

      .date-field {
        flex: 1;

        label {
          display: block;
          font-size: 14px;
          color: #333;
          margin-bottom: 8px;
        }

        .date-input {
          position: relative;
          
          input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background: white;
          }

          mat-datepicker-toggle {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            color: #FF6B00;
          }
        }
      }
    }
  }

  .button-group {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;  // Reduced from 30px

    button {
      width: 160px;  // Reduced from 150px
      height: 40px;  // Reduced from 44px
      border-radius: 8px;
      font-size: 14px;  // Reduced from 16px
      font-weight: 500;
      cursor: pointer;
      
      &.btn-cancel {
        background-color: transparent;
        color: #FF8033;
        border: 1.5px solid #FF8033;
      }

      &.btn-add {
        background-color: #FF8033;
        color: white;
        border: none;
        &:disabled {
          cursor: not-allowed;
          opacity: 0.7;
        }
      }
    }
  }
}

// Remove Material Design specific styles
::ng-deep {
  .mat-datepicker-toggle {
    .mat-icon-button {
      width: 40px !important;
      height: 40px !important;
    }
  }

  .mat-calendar-body-selected {
    background-color: #FF6B00 !important;
  }
}

::ng-deep .bonus-dialog-container {
  // Ensure dialog stays in viewport
  max-height: 90vh;
  overflow-y: auto;
  
  // Add some margin from viewport edges
  margin: 20px;
  
  // Optional: smooth animation
  animation: dialogFadeIn 0.2s ease-out;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
::ng-deep .cdk-overlay-pane {
  max-width: 90vw !important;  // prevent overflow
  // overflow-x: auto !important; // allow scroll if needed
  transform: translateX(-40px) !important; // adjust position from right edge
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

