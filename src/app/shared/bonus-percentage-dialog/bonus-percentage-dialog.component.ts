// File: add-bonus-popup.component.ts
import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-add-bonus-popup',
  standalone: true,
  imports: [CommonModule, FormsModule, MatDatepickerModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatNativeDateModule, MatIconModule],
  templateUrl: './bonus-percentage-dialog.component.html',
  styleUrls: ['./bonus-percentage-dialog.component.scss']
})
export class AddBonusPopupComponent implements OnInit {
  bonusPercentage: number = 0; // Default value
  startDate: Date = new Date(new Date().getFullYear(), 3, 1);
  endDate: Date = this.calculateFinancialYearEnd(this.startDate);
  minDate: Date = new Date(new Date().getFullYear(), 3, 1); // Month is 0-based, so 3 = April

  constructor(
    private dialogRef: MatDialogRef<AddBonusPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    if (this.data?.currentBonus) {
      // Remove the '%' symbol and parse the numeric value
      const sanitizedBonus = this.data.currentBonus.replace('%', '').trim();
      this.bonusPercentage = parseFloat(sanitizedBonus) || 0; // Default to 0 if parsing fails
    }
  }

  calculateFinancialYearEnd(startDate: Date): Date {
    return new Date(startDate.getFullYear() + 1, 2, 31); // March 31st of next year
  }

  validateNumberInput(event: KeyboardEvent): boolean {
    const inputChar = event.key;
    const currentValue = (event.target as HTMLInputElement).value;

    // Allow control keys
    if (
      inputChar === 'Backspace' ||
      inputChar === 'Delete' ||
      inputChar === 'Tab' ||
      inputChar === 'ArrowLeft' ||
      inputChar === 'ArrowRight'
    ) {
      return true;
    }

    // Allow only one '.'
    if (inputChar === '.' && currentValue.includes('.')) {
      event.preventDefault();
      return false;
    }

    // Allow digits and dot
    if (!/^[0-9.]$/.test(inputChar)) {
      event.preventDefault();
      return false;
    }

    return true;
  }

  validateRange(event: Event): void {
    const input = event.target as HTMLInputElement;
    let valueStr = input.value;

    // Remove the '%' symbol for validation purposes
    valueStr = valueStr.replace('%', '').trim();

    // Allow intermediate states like ".", "12."
    if (valueStr === '' || valueStr === '.' || valueStr.endsWith('.')) {
      return;
    }

    let value = parseFloat(valueStr);

    if (isNaN(value)) {
      input.value = '0'; // Default to 0 if the input is invalid
      this.bonusPercentage = 0;
      return;
    }

    value = Math.round(value * 100) / 100;

    if (value < 0) {
      value = 0;
    } else if (value > 100) {
      value = 100;
    }

    // Update the input value without adding the '%' symbol
    input.value = value.toString();
    this.bonusPercentage = value;
  }

  onStartDateChange(event: any): void {
    // Ensure the date is not before April 1st
    const selectedDate = event.value;
    const aprilFirst = new Date(new Date().getFullYear(), 3, 1);

    if (selectedDate < aprilFirst) {
      this.startDate = aprilFirst;
    }

    // Update end date if needed
    if (this.endDate && this.endDate < this.startDate) {
      this.endDate = this.calculateFinancialYearEnd(this.startDate);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onAdd(): void {
    const bonus = parseFloat(String(this.bonusPercentage));

    if (!isNaN(bonus) && bonus > 0 && this.startDate && this.endDate) {
      const result = {
        active: true,
        bonusPercentage: bonus,
        startDate: this.startDate.toISOString(),
        endDate: this.endDate.toISOString()
      };
      this.dialogRef.close(result);
    }
  }
}
