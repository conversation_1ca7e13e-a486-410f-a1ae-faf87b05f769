<div class="custom-dropdown">
  <div class="selected-option" (click)="toggleDropdown($event)">
    {{ getSelectedOptionText() }}
    <div class="dropdown-icon">
      <i class="fa fa-chevron-down"></i>
    </div>
  </div>
  <div class="options-container" [class.show]="isOpen">
    <div class="search-box" *ngIf="enableSearch">
      <input 
        type="text" 
        placeholder="Search..." 
        [(ngModel)]="searchTerm" 
        (input)="filterOptions()"
        (click)="onSearchClick($event)"
        (keypress)="preventSpecialCharacters($event)"
      >
      <i class="fa fa-search search-icon"></i>
    </div>
    <div class="options-list">
      <div class="option" 
           *ngFor="let option of filteredOptions" 
           [class.selected]="selectedId === option.id"
           (click)="selectOption(option)">
        {{ option.name }}
      </div>
      <div class="no-results" *ngIf="filteredOptions.length === 0">
        No results found
      </div>
    </div>
  </div>
</div>