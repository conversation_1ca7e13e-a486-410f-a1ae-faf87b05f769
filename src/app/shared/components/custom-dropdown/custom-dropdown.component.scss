.custom-dropdown {
  position: relative;
  width: 100%;
  z-index: 20;
}

.selected-option {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  position: relative;
  z-index: 5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-icon {
  color: #ff7f50;
  background-color: #fff0eb;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-icon i {
  font-size: 12px;
}

.options-container {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #ddd;
  border-radius: 0 0 8px 8px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
  z-index: 30;
  box-shadow: 0 5px 10px rgba(0,0,0,0.1);
  opacity: 0;
  visibility: hidden;
  
  /* Hide scrollbar initially but reserve space for it */
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }
}

.options-container.show {
  max-height: 250px;
  overflow-y: auto;
  opacity: 1;
  visibility: visible;
  
  /* Show scrollbar when dropdown is open */
  scrollbar-width: thin;
  -ms-overflow-style: auto;
  &::-webkit-scrollbar {
    width: 6px;
    background: #f1f1f1;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #ff7f50;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }
  
  /* Add a small delay before showing scrollbar */
  animation: showScrollbar 0.3s 0.1s forwards;
}

@keyframes showScrollbar {
  from {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  to {
    scrollbar-width: thin;
    -ms-overflow-style: auto;
  }
}

/* Search box styling */
.search-box {
  padding: 10px;
  position: relative;
  border-bottom: 1px solid #eee;
}

.search-box input {
  width: 100%;
  padding: 8px 30px 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.search-box input:focus {
  border-color: #ff7f50;
}

.search-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 14px;
}

.options-list {
  max-height: 160px;
  overflow-y: auto;
}

.option {
  padding: 10px 15px;
  cursor: pointer;
  height: 40px;
  display: flex;
  align-items: center;
}

.option:hover {
  background-color: #ff7f50;
  color: white;
}

.option.selected {
  background-color: #fff0eb;
  color: #ff7f50;
}

.no-results {
  padding: 15px;
  text-align: center;
  color: #999;
  font-style: italic;
}