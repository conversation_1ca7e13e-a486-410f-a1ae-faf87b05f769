import { Component, Input, Output, EventEmitter, OnInit, On<PERSON>estroy, ElementRef, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DropdownService } from '../../services/dropdown.service';
import { Subscription } from 'rxjs';
import { Utility } from '../../utility/utility';

export interface DropdownOption {
  id: string;
  name: string;
  [key: string]: any; // For any additional properties
}

@Component({
  selector: 'app-custom-dropdown',
  templateUrl: './custom-dropdown.component.html',
  styleUrls: ['./custom-dropdown.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class CustomDropdownComponent implements OnInit, OnDestroy {
  @Input() options: DropdownOption[] = [];
  @Input() selectedId: string = '';
  @Input() placeholder: string = 'Select';
  @Input() dropdownType: string = '';
  @Input() enableSearch: boolean = true;
  @Input() dropdownId: string = ''; // Unique identifier for this dropdown
  
  @Output() selectionChange = new EventEmitter<{type: string, value: string, name: string}>();
  
  isOpen: boolean = false;
  searchTerm: string = '';
  filteredOptions: DropdownOption[] = [];
  private subscription: Subscription = new Subscription();
  
  constructor(
    private elementRef: ElementRef,
    private dropdownService: DropdownService,
    private utility: Utility
  ) {}
  
  ngOnInit() {
    this.filteredOptions = [...this.options];
    
    // Generate a unique ID if not provided
    if (!this.dropdownId) {
      this.dropdownId = `dropdown_${Math.random().toString(36).substring(2, 9)}`;
    }
    
    // Subscribe to open dropdown notifications
    this.subscription = this.dropdownService.openDropdown$.subscribe(openDropdownId => {
      // Close this dropdown if another one was opened
      if (openDropdownId !== this.dropdownId && this.isOpen) {
        this.isOpen = false;
      }
    });
  }
  
  ngOnDestroy() {
    // Clean up subscriptions when component is destroyed
    this.subscription.unsubscribe();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    // Close dropdown if clicked outside of this component
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.isOpen = false;
    }
  }
  
  toggleDropdown(event: Event): void {
    event.stopPropagation();
    this.isOpen = !this.isOpen;
    
    if (this.isOpen) {
      // Notify service that this dropdown is now open
      this.dropdownService.setOpenDropdown(this.dropdownId);
      
      // Reset search when opening
      this.searchTerm = '';
      this.filteredOptions = [...this.options];
    }
  }
  
  selectOption(option: DropdownOption): void {
    this.selectedId = option.id;
    this.selectionChange.emit({
      type: this.dropdownType,
      value: option.id,
      name: option.name
    });
    this.isOpen = false;
  }
  
  getSelectedOptionText(): string {
    const selected = this.options.find(option => option.id === this.selectedId);
    return selected ? selected.name : this.placeholder;
  }
  
  filterOptions(): void {
    if (!this.searchTerm) {
      this.filteredOptions = [...this.options];
    } else {
      const term = this.searchTerm.toLowerCase();
      this.filteredOptions = this.options.filter(
        option => option.name.toLowerCase().includes(term)
      );
    }
  }
  
  onSearchClick(event: Event): void {
    // Prevent dropdown from closing when clicking in search input
    event.stopPropagation();
  }

  preventSpecialCharacters(event: KeyboardEvent): void {
    this.utility.preventSpecialCharacters(event); // Call the utility function
  }
}
