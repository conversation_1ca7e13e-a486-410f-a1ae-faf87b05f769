@import "../../../styles";

.grid-container {
  width: 100%;
  margin: 0;
  overflow-y: hidden;

  .pagination-direction {
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 20px;
  }

  .total-count {
    font-size: 14px;
    color: #555;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    margin-left: 15px;
  }

  .fixed-min-width {
    min-width: 110px;
  }

  .custom-data-table {
    font-size: 15px;
    min-width: 100%;
    overflow-y: hidden;

    .custyle {
      width: 100%;
      margin-top: 0px;

      .custab {
        width: 100%;

        .table-head {
          // color: #0059b3;
          // font-size: 13px;
          // font-weight: bold;
          // text-align: center;
          color: #222222;
          font-size: 15px;
          font-weight: bold;
          text-align: center;
          height: 50px;
          background-color: #FBE3D4;
          .actions-column {
            min-width: 135px !important;
          }
          .action-col-width {
            width: 200px;
          }

          .head-row {
            font-weight: bold;
            text-align: center;
            height: 40px;
            line-height: 18px;
          }

          .table-width-custom {
            min-width: 95px;
          }

          .min-width-150 {
            min-width: 150px;
          }

          .min-width-15 {
            min-width: 50px;
            max-width: 100px;
            width: 18%;
          }

          .bg-color {
            background-color: rgba(0, 0, 0, 0.1);
          }
        }

        .effects-on-inner-table {
          transition: all 0.2s ease;
        }
      }

      h2 {
        font-weight: 700;
        color: #4d5a77;
        float: left;
        padding: 0;
        font-size: 14px;
        text-transform: uppercase;
        margin-bottom: 15px;
      }

      .font-size-11 {
        font-size: 11px;
      }
    }

    .table-row-client {
      font-size: 12px;
      color: #0059b3;
      height: 40px;
      cursor: pointer;

      &:hover {
        background: rgba(4, 160, 222, 0.23);
      }
    }

    .table-striped tbody tr:nth-of-type(odd) {
      background-color: #F8F8F8;
    }

    .table-row {
      font-size: 12px;
      color: #000;
      height: 40px;
      .actions-column {
        min-width: 135px !important;
      }
      .overflow-property-table {
        overflow: hidden;
        max-width: 95px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .text-action {
        background-color: #0059b3;
      }
    }

    .bg-color-white {
      background: #ffffff;
    }

    .border-row {
      border: 1px solid $table-border-color;
      text-align: center !important;

      .toggleEdit {
        cursor: pointer;
      }

      .add-edit {
        margin: 0 5px;
        cursor: pointer;
        font-size: 16px;

        .fa-trash {
          font-size: 16px;
          color: $trash-color;
        }

        .fa-window-close-o {
          font-size: 16px;
          color: $trash-color;
        }

        .fa-check-square {
          font-size: 16px;
          color: #7ab675;
        }
      }
      .add-edit-user {
        margin: 0 2px;
        cursor: pointer;
        font-size: 16px;

        .fa-trash {
          font-size: 16px;
          color: $trash-color;
        }

        .fa-window-close-o {
          font-size: 16px;
          color: $trash-color;
        }

        .fa-check-square {
          font-size: 16px;
          color: #7ab675;
        }
      }
      .add-edit-user.disabled {
          pointer-events: none;
          opacity: 0.4;
          cursor: not-allowed;
        }

      // Disabled toggle styles for finance review
      .add-edit img.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
        filter: grayscale(50%);
      }

      // Disabled style but functional toggle for finance review
      .add-edit img.disabled-style {
        opacity: 0.6 !important;
        filter: grayscale(30%) !important;
        // Keep cursor and pointer-events enabled so toggle remains functional
      }

      // More specific selector for finance review toggle
      span.add-edit img.disabled-style {
        opacity: 0.5 !important;
        filter: grayscale(50%) !important;
        transform: scale(0.95) !important;
        border: 2px solid red !important; // Temporary debug border
        border-radius: 50% !important;
      }

      // Debug class to make it obvious
      .debug-red-border {
        border: 3px solid red !important;
        background-color: yellow !important;
      }

      .show-status {
        cursor: inherit !important;
      }

      .show-cursor {
        cursor: pointer;
        text-decoration: underline;

        &:hover {
          font-weight: bold;
        }
      }

      .update-icon {
        color: #00cc3a;
      }

      .close-icon {
        color: red;
      }
    }

    .min-action-width {
      min-width: 110px;
    }

    .table-header {
      max-width: 150px;
      word-wrap: break-word;
    }

    .input-box-skus[readonly] {
      background-color: #fff !important;
      color: #464a4c !important;
      cursor: not-allowed;
    }
  }

  .addQnhand {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 10px;
  }
}

.change-status-header {
  background: #6784da;
  height: 61px;
}


.img-field {
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    height: 90px;
    margin-top: 10px;
  }
}


::ng-deep .mat-slide-toggle-bar {
  height: 27px !important;
  width: 46px !important;
  border-radius: 36px !important;
}

::ng-deep .mat-slide-toggle-thumb {
  position: relative;
  top: 6px !important;
  right: -6px !important;
}

.fa-pencil-square-o:before {
  content: "\f044";
}

.edit-icon {
  font-weight: 600;
  /* Lighter weight */
  font-size: medium;
  /* Reduce opacity for a thinner effect */
}

.key-value-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;

  th,
  td {
    border: 1px solid #ddd;
    padding: 10px;
    font-size: 14px;
    text-align: center;
    white-space: nowrap;
  }

  th {
    background-color: #FFF1E9;
    font-weight: normal;
  }

  td {
    background-color: #fff;
    font-weight: bold;
  }
}

.change-status {
  font-family: inherit;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 380px;
  width: 502px;
  zoom: 0.8;

  &-inner-section {
    display: flex;
    gap: 20px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;

    .image-container {
      img {
        height: 163px;
      }
    }

    .status-fields {
      .text-fields {
        font-size: 20px;
        line-height: 100%;
        width: 500px;
        color: #000000;
        font-weight: 500;
      }
    }
  }

  .change-status-action-section {
    display: flex;
    gap: 15px;

    .action-btn {
      font-size: 18px;
      font-weight: 500;
      width: 110px;
      background: #FF8033;
      border: 1px solid #FF8033;
      border-radius: 5px;
      color: #fff;
      padding: 10px 0;
    }

    .cancel-status {
      color: #FF8033 ;
      border: 1px solid #FF8033;
      background-color: white;
      
    }
  }
}

.view-pdf-icon {
  cursor: pointer;
}

.status-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
}


.add-approve {
  display: inline-flex;
  gap: 10px;
  align-items: center;
  height: 20px;
  line-height: 20px;
  vertical-align: middle;
  cursor: pointer;
  
  .icon-wrapper {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    cursor: pointer;
    transition: transform 0.2s;
    
    &:hover {
      transform: scale(1.1);
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
      
      &:hover {
        transform: none;
      }
    }
  }
  
  .approve-wrapper {
    .approve-icon {
      color: #FF8033; /* Orange/red color for approval tick */
      font-size: 20px;
      font-weight: normal; /* Match edit icon weight */
      line-height: 1;
      vertical-align: middle;
    }
  }
  
  .reject-wrapper {
    .reject-icon {
      color: #B4B4B4; /* Grey color for rejection cross */
      font-size: 20px;
      font-weight: normal; /* Match edit icon weight */
      line-height: 1;
      vertical-align: middle;
    }
  }
}

/* Ensure consistent alignment with other action buttons */
.border-row {
  .add-approve {
    cursor: pointer;
    display: inline-flex;
    vertical-align: middle;
    margin: 0 5px;
  }
}

::ng-deep .inline-tooltip-style {
  font-size: 16px !important;
  // padding: 10px 16px !important;
  width: 100px !important;
  background-color: #333;
  color: white;
  border-radius: 4px;
}

::ng-deep .tooltip-style {
  border-radius: 4px;
  font-size: 16px;
  background-color: #333 !important;
  color: #fff;
  width: max-content;
}

.confirm-dialog {
  padding: 20px;
  text-align: center;
  
  .image-container {
    margin-bottom: 20px;
    
    img {
      max-width: 100px;
      height: auto;
    }
  }
  
  .confirm-content {
    margin-bottom: 20px;
    
    .text-fields {
      font-size: 16px;
      color: #333;
    }
  }
  
  .action-button-section {
    display: flex;
    justify-content: center;
    gap: 15px;
    
    .action-btn {
      padding: 8px 25px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      border: none;
      
      &.submit-btn {
        background-color: #FF8033;
        color: white;
        
        &:hover {
          background-color: darken(#FF8033, 5%);
        }
      }
      
      &.cancel-btn {
        background-color: #f5f5f5;
        color: #333;
        border: 1px solid #ddd;
        
        &:hover {
          background-color: #e5e5e5;
        }
      }
    }
  }
}
.iframe-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
  background-color: white;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.iframe-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998;
}

.iframe-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  
  h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
  
  .close-iframe {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    
    &:hover {
      color: #343a40;
    }
  }
}

.document-frame {
  width: 100%;
  height: calc(100% - 50px);
  border: none;
}

.image-container {
  width: 100%;
  height: calc(100% - 50px);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
  padding: 20px;
  background-color: #f5f5f5;
  
  .document-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .iframe-container {
    width: 95%;
    height: 90%;
  }
}

/* Document button styles */
.document-button {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
  height: 32px; /* Consistent height */
  line-height: 32px; /* Center text vertically */
  padding: 0 12px;
  background-color: #FF8033;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  margin: 5px 0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.document-button:hover {
  background-color: darken(#FF8033, 5%);
}

.cdk-overlay-pane.bonus-dialog-container{
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

.sortable {
  cursor: pointer;
  
  .clickable {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    
    i {
      margin-left: 8px;
      padding: 1px 2px;
      font-weight: normal;
    }
  }
  
  &:hover {
    background-color: rgba(255, 128, 51, 0.1);
  }
}

/* Custom arrow styles with less weight */
.fa-sort-asc:before {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 10px solid #222;
  opacity: 0.8;
}

.fa-sort-desc:before {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 10px solid #222;
  opacity: 0.8;
}

/* Fix for table headers to display in one line */
.table-head th {
  white-space: nowrap !important;
  padding: 0 16px !important;
}

.header-content {
  display: inline-flex !important;
  align-items: center !important;
}

.fa-sort-asc, .fa-sort-desc {
  margin-left: 5px !important;
  color: #ff7f50 !important;
}

/* Ensure the table headers are properly aligned */
.border-row.head-row {
  text-align: center !important;
  vertical-align: middle !important;
  height: 50px !important;
}