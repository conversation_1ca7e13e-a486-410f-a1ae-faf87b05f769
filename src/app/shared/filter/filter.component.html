<div class="user-grid-action">
  <div class="user-grid-search-container" *ngIf="!showErrorBlockSection">
    <div
      class="input-group user-grid-search-input"
      *ngIf="configurations?.showSearch"
    >
      <span class="input-group-addon">
        <i class="fa fa-search" aria-hidden="true"></i>
      </span>
      <input
        #searchBox
        class="search input-fields right-border searchbox"
        placeholder="Search"
        [(ngModel)]="model"
        (ngModelChange)="changed($event)"
        (keypress)="funRestCharacterNumber($event)"
      />
    </div>
    <div class="radio-user-role" *ngIf="configurations?.showRadioFilters">
      <span class="radio-margin">
        <input
          type="radio"
          class="btn btn-primary radio-button"
          value="active"
          name="RadioSelect"
          id="active"
          (change)="selectList($event)"
          [(ngModel)]="selectedOption"
        />
        <label for="active" class="radio-label">Active</label>
      </span>
      <span class="radio-margin">
        <input
          type="radio"
          class="btn btn-primary radio-button"
          value="inactive"
          name="RadioSelect"
          id="inactive"
          (change)="selectList($event)"
          [(ngModel)]="selectedOption"
        />
        <label for="inactive" class="radio-label">Inactive</label>
      </span>
    </div>
  </div>

  <div
    class="user-grid-action-container"
    [ngClass]="{
      'section-grid-container': toggleFileUploadButton,
      'reports-grid-wrapper': configurations?.showReport,
      'distributor-grid-action-container':
        configurations?.showDistributorSignedUpDropdownFilters
    }"
  >
    <div
      class="float-left width-25 dropdown-adjustment margin-right-5 distributor-dropdown"
      *ngIf="configurations?.showDistributorSignedUpDropdownFilters && isAdmin"
    >
      <label class="profit-center-title"
        ><b>Distributor Signed Up&nbsp;</b></label
      >
      <select
        class="form-control role-pc-dd"
        [ngClass]="{
          'distributor-signedUp-dropdown':
            configurations?.showDistributorSignedUpDropdownFilters
        }"
        #distributorSignedupDropdown
        [(ngModel)]="distributorSignedup"
        (ngModelChange)="distributorSignedUpDropdownChange($event)"
      >
        <option value="all">All</option>
        <option value="yes">Yes</option>
        <option value="no">No</option>
      </select>
    </div>

    <div
      class="user-grid-action-add"
      [ngClass]="{
        'export-btn': configurations?.showDistributorSignedUpDropdownFilters
      }"
    >
      <button
        class="export-data"
        *ngIf="configurations?.export"
        (click)="onExportClick()"
        title="Export"
      >
        <i class="fa fa-share-square-o"></i>
      </button>

      <button
        class="add"
        *ngIf="configurations?.add && isAdmin"
        [routerLink]="commonButton[0]?.path"
        [ngClass]="{ 'add-btn-width': toggleFileUploadButton }"
      >
        <i class="fa fa-plus"></i>&nbsp;{{ commonButton[0]?.title }}
      </button>
    </div>
  </div>
</div>
