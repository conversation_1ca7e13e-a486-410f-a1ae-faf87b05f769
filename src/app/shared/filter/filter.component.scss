@import "../../theme/sass/_auth";
@import "../../../styles";
@import "../../theme/sass/mixins";
@import "../../pages/reports/reports.component.scss";

$font-size: 13px;
.user-grid-action {
  padding: 0 15px;
  width: 100%;
  position: relative;
  margin-bottom: 10px;
  overflow-y: auto;
  background: #fff;
  border-radius: $border-radius;
  box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.1);
  .user-grid-search-container {
    width: 40%;
    float: left;
    padding: 1% 0;
    .user-grid-search-input {
      flex-wrap: inherit;
      @include gridSearchInput();
      width: 50%;
      .input-fields {
        padding: 0.5rem 0.75rem;
        font-size: 14px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        line-height: 1.25;
      }
    }
    @include inputRightBorder($border-radius);
    .input-group {
      margin-bottom: 0;
      @include inputAddOnBorder($border-radius);
    }
    @include placeholderColor(#666, 0.7);
    .radio-user-role {
      margin: 10px 0 0 10px;
      float: left;
      @media screen and (max-width: 767px) {
        padding: 10px 0px;
        margin-left: 0;
      }
      .radio-margin {
        margin-right: 15px;
        .radio-label {
          text-align: center;
          padding-left: 5px;
          color: #666;
          position: relative;
          top: 2px;
          cursor: pointer;
        }
        .radio-button {
          margin: 0;
          font-size: 11px;
        }
      }
    }
  }

  .user-grid-action-container {
    width: 60%;
    float: right;
    padding: 1% 0;
    display: block;
    position: relative;
    .reports-grid-action {
      box-shadow: initial;
    }
    .profit-center-dropdown {
      width: 40%;
      float: left;
      .profit-center-title {
        float: left;
        padding: 7px;
        margin: 0;
      }
    }
    .profit-center-dropdown-full-width {
      width: 32%;
    }
    .distributor-dropdown {
      width: 45%;
      float: left;
      .profit-center-title {
        float: left;
        padding: 7px;
        margin: 0;
      }
    }
    .dropdown-adjustment {
      position: relative;
      top: 5px;
      .role-pc-dd {
        float: left;
        height: 35px;
        border-radius: 3px;
        width: 40%;
      }
      .distributor-signedUp-dropdown {
        min-width: 35% !important;
        width: auto !important;
      }
    }
    .margin-right-5 {
      margin-right: 5px;
    }
    .margin-left-5 {
      margin-left: 5px;
    }
    .user-grid-action-add {
      @include addButtonContainer();
      width: 41.7%;
      .add-btn-width {
        width: 100%;
      }
    }
    .export-btn {
      width: 20% !important;
      .show-export-full-width {
        width: 28%;
      }
      .show-export-retailer-width {
        width: 28% !important;
      }
    }
    .user-grid-action-choose-file {
      @include addButtonContainer();
      float: left;
    }

    .export-data {
      float: right !important;
      width: 40px;
      height: 40px;
      background: #FF8033;
      color: #fff;
      border: none;
      border-radius: 5px;
      margin-left: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      
      i {
        font-size: 18px;
        color: white;
      }
      
      &:hover {
        background: #e67730;
      }
      
      &:focus {
        outline: none;
      }
    }

    .export-icon {
      padding: 0 5px;
    }
  }

  .distributor-grid-action-container {
    width: 60% !important;
  }
  .reports-grid-wrapper {
    width: 100%;
  }
  .section-grid-container {
    width: 60%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

.confirmUserActiveContainer {
  @include confirmDialogueActiveInActive();
}

.modal-backdrop.fade {
  opacity: 0.6;
}

.confirmUserActiveContainer .fade {
  opacity: 1 !important;
}

.disable-submit {
  opacity: 0.8;
  cursor: not-allowed;
  &:hover {
    opacity: 0.8;
    cursor: not-allowed;
  }
}

@media only screen and (max-width: 1100px) {
  .user-grid-action {
    .user-grid-search-container,
    .distributor-grid-action-container,
    .user-grid-action-container {
      width: 40% !important;
      // padding: 0;
    }
    .user-grid-action-container {
      .user-grid-action-add {
        width: 100%;
        margin: 0 0 0 0;
        .export-data {
          width: auto;
        }
        .add {
          width: 40%;
          float: right;
        }
      }
    }
  }
}

@media only screen and (max-width: 767px) {
  .user-grid-action {
    .user-grid-action-container {
      .profit-center-dropdown-full-width,
      .distributor-dropdown {
        width: 100%;
        padding: 10px 0 0 0;
      }
      .profit-center-dropdown {
        width: 100%;
      }
      .export-btn {
        width: auto;
      }
    }
    .user-grid-search-container {
      .user-grid-search-input {
        width: 100%;
      }
    }
  }
}
