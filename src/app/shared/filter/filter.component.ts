import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  SimpleChanges,
  OnChang<PERSON>,
  inject,
  ViewEncapsulation
} from "@angular/core";
import { Router, RouterLink } from "@angular/router";
import { Subject } from "rxjs";
import { GlobalEvents } from "../../helpers/global.events";
import { AuthenticationHelper } from "../../helpers/authentication";
import { debounceTime } from "rxjs/operators";
import { FormsModule } from "@angular/forms";
import { NgIf, NgClass } from "@angular/common";
import { ToastrService } from "ngx-toastr";
import { MatDialog } from "@angular/material/dialog";
import { BrandFormComponent } from "src/app/pages/brands/brand-form";
import {MatTabsModule} from '@angular/material/tabs';

@Component({
    selector: "nga-app-filter",
    templateUrl: "./filter.component.html",
    styleUrls: ["./filter.component.scss"],
    imports: [NgIf, FormsModule, NgClass, RouterLink, MatTabsModule]
})
export class FilterComponent implements OnInit, OnChanges {
  toggleFileUploadButton: boolean = false;
  selectedOption: string = "active";
  showErrorBlockSection: boolean = false;
  model!: string;
  PCCode: string = "all";
  distributorSignedup: string = "all";
  commonButton: any = [];

  modelChanged: Subject<string> = new Subject<string>();

  @Input() configurations: any;
  @Input() addButton: any = [];
  @Output() onSearchValue = new EventEmitter();
  @Output() onExport = new EventEmitter();
  @Output() onStatusFiltersChange = new EventEmitter();
  @Output() onDistributorSignedUpDropdownChange = new EventEmitter();
  isAdmin: boolean = false;
  activeTab: string = "";

  constructor(private router: Router, private events: GlobalEvents,public toastr: ToastrService,private dialog:MatDialog) {
    this.isAdmin = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;
  }
  ngOnInit() {
    this.modelChanged.pipe(debounceTime(400)).subscribe((model) => {
      this.onSearchValue.emit(model);
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes["addButton"] && changes["addButton"].currentValue) {
      this.commonButton = changes["addButton"].currentValue;
    }
  }

  PCDropDownChange(event: any): void {
    if (event) {
      let selectedPC = {
        pcCode: event,
        tab: this.configurations.showdropdown1Title,
      };
      this.events.onPCSelect.emit(selectedPC);
    }
    this.PCCode = "all";
  }
  ngAfterViewInit() {
    this.events.onUserGridTabChange.subscribe((item) => {
      if (item) {
        this.model = "";
        this.selectedOption = "active";
      }
    });

    this.events.clearPCSelect.subscribe((item: any) => {
      if (item) {
      }
    });
  }

  selectList(event: any): void {
    this.model='';
    let selectedStatus = {
      isActive: event.target.value === "active" ? true : false,
      tab: this.configurations.showdropdown1Title,
    };
    this.onStatusFiltersChange.emit(selectedStatus);
  }

  changed(text: string): void {
    this.modelChanged.next(text);
  }

  onExportClick(): void {
    this.onExport.emit("export");
  }

  distributorSignedUpDropdownChange(event: any): void {
    if (event) {
      this.onDistributorSignedUpDropdownChange.emit(event);
    }
    this.distributorSignedup = "all";
  }

  funRestCharacterNumber(event: any) {
    const inputValue = event.target.value;
    const key = event.key;
  
    const isNumberOrCharacter = /^[0-9a-zA-Z]+$/.test(key);
  
    if (inputValue.length === 0 && key === ' ') { 
      event.preventDefault();
    } else if (!isNumberOrCharacter && key !== ' ') { 
      event.preventDefault();
      this.toastr.warning('Only numbers and characters are allowed');
    }
  }

  handleAddClick(){
    this.dialog.open(BrandFormComponent).afterClosed().subscribe((result)=>{})
  }
  
  
}
