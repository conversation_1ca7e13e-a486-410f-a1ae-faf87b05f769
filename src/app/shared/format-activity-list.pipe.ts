import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'formatActivityList'
})
export class FormatActivityListPipe implements PipeTransform {

  transform(activityList: any[]): any[] {
    if (!activityList || !Array.isArray(activityList)) {
      return [];
    }

    return activityList.map(item => ({
      ...item,
      name: item.name
        ? item.name.split('_').map((s: any) => s.toUpperCase()).join(' ')
        : ''
    }));
  }

}
