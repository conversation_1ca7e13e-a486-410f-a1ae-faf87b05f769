import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'formatNumber',
  standalone: true
})
export class FormatNumberPipe implements PipeTransform {
  transform(value: unknown): string | number {
    if (typeof value !== 'number' || isNaN(value)) return '';

    // If it's an integer, return the number as-is
    if (Number.isInteger(value)) return value;

    // If it's a float, return it with 2 decimal places
    return parseFloat(value.toFixed(2));
  }
}
