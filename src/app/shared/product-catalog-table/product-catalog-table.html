<div class="grid-container">
  <div class="custom-data-table">
    <div class="custyle">
      <table [ngClass]="{ 'font-size-11': path == '/product-details' }" class="table-striped custab">
        <tbody>
          <ng-container *ngFor="let data of tableData; let i = index">

            <tr *ngIf="i !== 0">
              <td
                [attr.colspan]="tableColNameGenerated.length + (tableConfiguration.showActionsColumn ? 1 : 0) + (path === '/product-catalog' ? 1 : 0)">
                <div style="height: 16px;"></div>
              </td>
            </tr>

            <!-- Repeated table head -->
            <tr class="table-head repeat-head-row">
              <ng-container *ngFor="let tableHead of tableHeads">
                <th class="border-row head-row fixed-min-width">{{ tableHead }}</th>
              </ng-container>
              <th *ngIf="path === '/product-catalog'" class="border-row head-row fixed-min-width"></th>
            </tr>

            <!-- Data row -->
            <tr [ngClass]="{ 'bg-color-white': path == '/product-details', 'font-size-11': path == '/product-details' }"
              class="table-row" style="background-color: #FFF1E9;">
              <ng-container *ngFor="let colName of tableColNameGenerated">
                <td [attr.title]="path == '/product-details' ? data[colName] : ''" [ngClass]="{
                    'text-right': colName == 'percentage_growth' || (path == '/product-details' && ['tm_budget_volume','apr','may','jun','jul','aug','sep','oct','nov','dec','jan','feb','mar'].includes(colName)),
                    'text-left': colName != 'percentage_growth',
                    'overflow-property-table': path == '/product-details'
                  }" class="border-row table-header" (click)="colName == 'sku_code' ? colData($event) : ''">

                  <span *ngIf="i != showIndex.index">{{ data[colName] }}</span>
                  <input *ngIf="tableConfiguration.editBox && i == showIndex.index" class="form-control"
                    [ngClass]="{ 'input-box-skus': colName == 'sku' }" [value]="data[colName]"
                    (change)="valueCol($event, colName)" [readonly]="colName == 'sku'" />
                </td>
              </ng-container>

              <!-- Action column -->
              <td *ngIf="tableConfiguration.showActionsColumn" class="border-row fixed-min-width">
                <span *ngIf="tableConfiguration.productIcon" title="Brand Details" (click)="productDetails(data)"
                  class="add-edit"></span>

                <!-- Show status without toggle -->
                <span *ngIf="tableConfiguration.showStatus && !tableConfiguration.changeStatus" class="add-edit">
                  <i *ngIf="data?.is_active" class="fa fa-check update-icon" title="Active"></i>
                  <i *ngIf="!data?.is_active" class="fa fa-close close-icon" title="Inactive"></i>
                </span>

                <!-- Show status with toggle -->
                <span *ngIf="tableConfiguration.showStatus && tableConfiguration.changeStatus" class="add-edit">
                  <i *ngIf="data?.is_active" class="fa fa-check update-icon" title="Active"
                    (click)="onStatusClick(data)"></i>
                  <i *ngIf="!data?.is_active" class="fa fa-close close-icon" title="Inactive"
                    (click)="onStatusClick(data)"></i>
                </span>

                <!-- Edit actions -->
                <ng-container *ngIf="i != showIndex.index && tableConfiguration.showEdit">
                  <span *ngIf="!path.includes('/product-catalog') || (path.includes('product-catalog') && isAdmin)"
                    [attr.title]="typeof tableConfiguration.showEdit !== 'string' ? 'Edit' : null"
                    (click)="editRowData(data, i)" class="add-edit">
                    <i *ngIf="typeof tableConfiguration.showEdit !== 'string'" class="fas fa-pencil-alt edit-icon"></i>
                    <img *ngIf="typeof data.showEdit === 'string'" [src]="data.showEdit" alt="Status" width="42px"
                      height="42px" class="status-icon" (click)="toggleEditImage(i, $event)">
                  </span>
                </ng-container>

                <!-- Update / Cancel icons -->
                <span *ngIf="tableConfiguration.editBox && i == showIndex.index" title="Cancel" (click)="updateFalse()"
                  class="add-edit">
                  <i class="fa fa-close close-icon"></i>
                </span>
                <span *ngIf="tableConfiguration.editBox && i == showIndex.index" title="Update"
                  (click)="updateRowData()" class="add-edit">
                  <i class="fa fa-check-square-o update-icon"></i>
                </span>
              </td>

              <!-- Expand/Collapse -->
              <td *ngIf="path === '/product-catalog'" class="border-row fixed-min-width">
                <i class="fa"
                  [ngClass]="{ 'fa-chevron-down': expandedRowIndex !== i, 'fa-chevron-up': expandedRowIndex === i }"
                  style="cursor: pointer; padding: 12px; border-radius: 50%; background: white;"
                  (click)="toggleRowExpansion(i)" title="Expand/Collapse">
                </i>
              </td>
            </tr>

            <!-- Expanded Row -->
            <tr *ngIf="expandedRowIndex === i && path === '/product-catalog'">
              <td [attr.colspan]="tableColNameGenerated.length + 2" class="expanded-data-row"
                style="height: 30px; background-color: #FFF1E9; padding:0 20px 20px;">
                <div class="expanded-content">
                  <table class="key-value-table">
                    <thead>
                      <tr>
                        <th *ngFor="let header of innerTableHeaders">{{ header }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td *ngFor="let key of innerTableKeys">
                          <strong>{{ data.expandedDetails?.[key] }}</strong>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </td>
            </tr>
          </ng-container>

          <!-- No Data -->
          <tr *ngIf="tableData.length == 0" class="center no-data">
            <td [attr.colspan]="tableColNameGenerated.length + 2">
              {{ tableConfiguration.noDataMessage }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="tableConfiguration?.totalRecordCount && tableConfiguration?.showPagination" class="pagination-direction">
    <div *ngFor="let data of tableData | paginate : {
        itemsPerPage: tableConfiguration?.perPage,
        currentPage: tableConfiguration?.currentPage,
        id: 'pagination',
        totalItems: tableConfiguration?.totalRecordCount
      }">
    </div>
    <pagination-controls class="addQnhand" (pageChange)="getPageData($event)" id="pagination" maxSize:any="5"
      autoHide:any="true" directionLinks:any="true" autoHide:any="true" previousLabel="Previous" nextLabel="Next">
    </pagination-controls>
  </div>
</div>