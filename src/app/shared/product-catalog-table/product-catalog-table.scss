@import "../../../styles";

// =============================
// Base Containers & Layout
// =============================
.grid-container {
  width: 100%;
  margin: 0;
  overflow-y: hidden;

  .pagination-direction {
    display: flex;
    justify-content: flex-end;
  }

  .fixed-min-width {
    min-width: 50px;
  }
}

// =============================
// Table Styles
// =============================
.custom-data-table {
  font-size: 16px;
  min-width: 100%;

  .custyle {
    h2 {
      font-size: 14px;
      font-weight: $font-bold;
      color: #4d5a77;
      text-transform: uppercase;
      margin-bottom: 15px;
    }

    .custab {
      width: 100%;

      .table-head {
        font-size: 15px;
        color: #222;
        background: #FFF1E9;
        height: 50px;

        th {
          padding: 0 16px;
        }

        .action-col-width {
          width: 200px;
        }

        .table-width-custom {
          min-width: 95px;
        }

        .min-width-150 {
          min-width: 150px;
        }

        .min-width-15 {
          min-width: 50px;
          max-width: 100px;
          width: 18%;
        }

        .bg-color {
          background: rgba(0, 0, 0, 0.1);
        }
      }

      .effects-on-inner-table {
        transition: all $default-animation-duration $default-animation-style;
      }
    }
  }

  .table-row-client,
  .table-row {
    font-size: 12px;
    cursor: pointer;
    height: 40px;

    &:hover {
      background: rgba(4, 160, 222, 0.23);
    }

    td {
      padding-left: 16px;
      padding-bottom: 8px;
      font-weight: $font-bold;

      i {
        position: relative;
        bottom: 25px;
      }
    }
  }

  .table-row-client {
    color: $table-color;
  }

  .table-row {
    background-color: #FFF1E9;
    color: #000;

    .overflow-property-table {
      max-width: 95px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .text-action {
      background: $table-color;
    }
  }

  .table-striped tbody tr:nth-of-type(odd) {
    background-color: $white-bg;
  }

  .bg-color-white {
    background: $white-bg;
  }

  .border-row {
    padding: 0;

    .toggleEdit,
    .show-cursor,
    .add-edit {
      cursor: pointer;
    }

    .show-cursor {
      text-decoration: underline;

      &:hover {
        font-weight: bold;
      }
    }

    .add-edit {
      margin: 0 5px;
      font-size: 16px;

      .fa-trash,
      .fa-window-close-o {
        color: $trash-color;
      }

      .fa-check-square {
        color: #7ab675;
      }
    }

    .update-icon {
      color: #00cc3a;
    }

    .close-icon {
      color: red;
    }

    .show-status {
      cursor: not-allowed !important;
    }
  }

  .min-action-width {
    min-width: 110px;
  }

  .table-header {
    max-width: 150px;
    word-wrap: break-word;
  }

  .input-box-skus[readonly] {
    background: $white-bg !important;
    color: $addon-color !important;
    cursor: not-allowed;
  }
}

// =============================
// Modal & Status Components
// =============================
.addQnhand {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.change-status-container {
  .preview-modal {
    padding-bottom: 10px;

    .change-status-header {
      position: relative;
      padding: 20px 0;
      border-bottom: 1px solid gainsboro;

      .change-status-modal-header {
        margin: 0;
      }

      .close-status {
        position: absolute;
        right: 10px;
        top: 5px;
        font-size: 17px;
        cursor: pointer;
      }
    }

    .status-fields {
      padding: 10px 20px;

      .img-field {
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          height: 90px;
        }
      }
    }

    .change-status-action {
      .change-status-action-container {
        padding: 10px;

        .action-btn {
          border-radius: $border-radius;
          height: 40px;
          background: grey;
          color: $white-bg;
          font-weight: $font-bold;
          width: 35%;
        }

        .submit-status {
          background: $button-color;
          float: right;
        }
      }
    }
  }
}

.change-status-container-modal {
  .changeText {
    border-bottom: 1px solid #707070;
    padding: 1rem 1rem 1rem 0;
    font-size: 26px;
    color: $white-bg;
  }

  .text-fields {
    font-size: 18px;
    color: $button-color;
  }

  .change-status-action {
    margin-top: 10px;

    .action-btn,
    .submit-status,
    .cancel-status {
      margin: 0 0.5rem 1rem 0.5rem;
      font-weight: $font-bold;
    }

    .action-btn,
    .submit-status {
      width: 11%;
      color: $white-bg;
    }

    .submit-status {
      background: #707070;
    }

    .cancel-status {
      background: $button-color;
    }
  }
}

.change-status-header {
  background: #6784da;
  height: 61px;
}

// =============================
// Tables & Icons
// =============================
.key-value-table,
.inner-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;

  td,
  th {
    border: 1px solid $table-border-color;
    font-size: 14px;
    padding: 20px 8px 3px 16px;
    white-space: nowrap;
  }

  th {
    background: $white-bg;
    font-weight: $font-normal;
  }

  td {
    background: rgb(246, 248, 248);
    font-weight: $font-bold;
  }
}

.inner-table {
  width: 96%;
  margin-bottom: 10px;

  td {
    padding: 8px 10px;
  }
}

.key {
  width: 40%;
  background: #f9f9f9;
  font-weight: $font-bold;
}

.value {
  width: 60%;
}

.status-icon {
  position: relative;
  bottom: 30px;
}

.edit-icon {
  font-size: medium;
  font-weight: $font-normal;
}

.fa-pencil-square-o:before {
  content: "\f044";
  position: relative;
  bottom: 33px;
}

.action-icons,
.expand-icon {
  display: flex;
  gap: 10px;
  margin-top: 8px;
}

// =============================
// Angular Material Override
// =============================
::ng-deep {
  .mat-slide-toggle-bar {
    height: 27px !important;
    width: 46px !important;
    border-radius: 36px !important;
  }

  .mat-slide-toggle-thumb {
    top: 6px !important;
    right: -6px !important;
    position: relative;
  }
}