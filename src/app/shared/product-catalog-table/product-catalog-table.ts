import {
  Component,
  OnInit,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  EventEmitter,
  ViewChild,
  TemplateRef,
  input,
} from "@angular/core";
import { Router } from "@angular/router";
import { EditSKUData } from "../../app-services/edit-sku-data-service";
import { AuthenticationHelper } from "../../helpers/authentication";
import { MatDialog } from "@angular/material/dialog";

import { GlobalEvents } from "../../helpers/global.events";
import { NgxPaginationModule } from "ngx-pagination";
import { NgClass, NgFor, NgIf } from "@angular/common";
import { MatButtonModule } from '@angular/material/button';
import { DashboardService } from "src/app/app-services/dashboard.service";
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ThemePalette } from "@angular/material/core";



@Component({
  selector: "dynamic-table",
  templateUrl: "product-catalog-table.html",
  styleUrls: ["product-catalog-table.scss"],
  imports: [Ng<PERSON>lass, NgFor, NgIf, NgxPaginationModule, MatButtonModule, MatSlideToggleModule]
})
export class DynamicTableComponent implements OnInit, OnChanges {
  path: string;
  statusData: any;
  @Input() tableConfiguration: any = {
    showPagination: false,
    perPage: 15,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    actionsColumnName: "Actions",
    noDataMessage: "No data found",
    editBox: false,
    productIcon: false,
    showEdit: true,
    showStatus: false,
    changeStatus: false,
  };
  updatedData: any = {};
  @Input() tableData: any = [];
  @Input() tableHeads: Array<string> = new Array<string>();
  @Input() tableColName: Array<string> = new Array<string>();
  @Input() showIndex: any;
  @Input() innerTableHeaders:Array<string> = new Array<string>();
  @Input() innerTableKeys:Array<string> = new Array<string>();
  @Output() pageChange: any = new EventEmitter();
  @Output() brandDetails: any = new EventEmitter();
  @Output() onRowEdit: any = new EventEmitter();
  @Output() onStatusChange: any = new EventEmitter();
  @Output() toggleEdit = new EventEmitter<number>();
  public tableColNameGenerated: Array<any> = new Array<any>();
  private isTableColNameSet: Boolean = false;
  innerData: boolean = false;
  isAdmin: boolean = false;
  @ViewChild("changeStatus") changeStatus!: TemplateRef<any>;

  @ViewChild("statusConfirm") statusConfirm: any;

  color = 'green';
  checked = true;
  disabled = false;
  brandData: any;
  expandedRowIndex: number | null = null;

  constructor(
    public dialog: MatDialog,
    private router: Router,
    private editSkuDataService: EditSKUData,
    private events: GlobalEvents,
    private dashboardService: DashboardService
  ) {
    this.path = this.router.url;
    this.isAdmin = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;
  }
  ngOnInit() {}
  ngAfterViewInit() {
    this.events.onUserStatusChange.subscribe((item) => {
      if (item) {
      }
    });
  }

  /**
   *  Called automatically when there is a change in a component
   */
  ngOnChanges(changes: SimpleChanges) {
    if (changes["tableHeads"]) {
      if (this.tableHeads.length) {
        this.tableConfiguration.showActionsColumn
          ? this.tableHeads.push(this.tableConfiguration.actionsColumnName)
          : null;
      }
    }

    if (changes["tableData"]) {
      if (!this.isTableColNameSet) {
        if (this.tableData && this.tableData.length > 0) {
          this.tableColNameGenerated = this.getKeys(this.tableData[0]);

        }
      }
    }

    if (changes["tableColName"]) {
      if (this.tableColName.length > 0) {
        this.tableColNameGenerated = this.tableColName;
        this.isTableColNameSet = true;
      }
    }

    if (changes["tableConfiguration"]) {
    }
  }

  /**
   * This method will fetch all the property name and convert it into a list of String.
   * @param {Array<String>} head Pass in the list of String, which contains table header values
   * @param {Array<String>} col Pass in the list of String, which contains column property
   * name, which was received from Input or generated using this.getKeys()
   */
  private isHeadAndColLengthSame(
    head: Array<String>,
    col: Array<String>
  ): Boolean {
    return head.length === col.length;
  }

  /**
   * This method will fetch all the property name and convert it into a list of String.
   * @param {any} value Pass Instance of Object eg. new User()
   */
  private getKeys(value: any): Array<String> {
    return Object.keys(value);
  }

  /**
   * To get the data of page changed
   * @param page
   */
  public getPageData(page: any) {
    this.pageChange.emit(page);
  }

  /**()
   * To get the data of current row clicked
   * @param data
   */
  public editRowData(data: any, index: any): void {
    switch (this.path) {
      case "/zones":
        this.router.navigate(["zones/edit-zone"], {
          queryParams: { id: data.code },
        });
        break;
      case "/users":
        this.router.navigate(["users/edit-user"], {
          queryParams: { id: data.id },
        });
        break;
      case "/regions":
        this.router.navigate(["regions/edit-region"], {
          queryParams: { id: data.code },
        });
        break;
      case "/rewards-points":
        this.router.navigate(["rewards-points/edit-rewards-points"], {
          queryParams: { id: data.id },
        });
        break;
    }
    if (this.path.includes("/product-catalog/sku")) {
      this.showIndex.index = index;
    }
    this.onRowEdit.emit(data);
  }

  onConfirmStatusChange(): void {
    this.onStatusChange.emit(this.statusData);
    this.dialog.closeAll();
    this.dashboardService._disabledSidebar.emit(false);
  }

  valueCol(event: any, col: any): void {
    this.updatedData[col] = event.target.value;
  }
  updateRowData(): void {
    this.editSkuDataService.rowData.next(this.updatedData);
    this.updatedData = {};
  }

  updateFalse(): void {
    this.tableConfiguration.editBox = false;
    this.showIndex.index = null;
  }

  /**
   * Called to get the product details
   * @param data
   */
  productDetails(data: any): void {
    this.brandDetails.emit(data);
  }

  colData(event: any) { }

  /**
   * Triggered when user click on the status icon in the action column
   * @param data
   */
  onStatusClick(data: any): void {
    this.dashboardService._disabledSidebar.emit(true);
    if (data) {
      this.statusData = {};
      this.statusData = data;
      const dialogRef = this.dialog.open(this.changeStatus, {
        width: "400px",
        height: "252px",
        disableClose: false,
        panelClass: "confirm-dialog-container",
        data: data,
        hasBackdrop: true,
        autoFocus: false,
      });
      dialogRef.afterClosed().subscribe((item) => {
        this.dashboardService._disabledSidebar.emit(false);
      });

    }
  }
  onCloseStatus(): void {
    this.dialog.closeAll();
    this.dashboardService._disabledSidebar.emit(false);
  }
  toggleEditImage(index: number, event: Event) {
    event.stopPropagation();
    this.toggleEdit.emit(index);
  }

  toggleRowExpansion(index: number): void {
    this.expandedRowIndex = this.expandedRowIndex === index ? null : index;
  }
}
