import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DropdownService {
  private openDropdownId = new Subject<string>();
  
  // Observable that components can subscribe to
  public openDropdown$: Observable<string> = this.openDropdownId.asObservable();
  
  // Method to notify which dropdown is currently open
  setOpenDropdown(id: string): void {
    this.openDropdownId.next(id);
  }
}