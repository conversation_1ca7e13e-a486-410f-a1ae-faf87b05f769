import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class LogoutService {

  constructor(
    private router: Router,
    private toastr: ToastrService
  ) {}

  /**
   * Handle complete logout - clear all storage and redirect to login
   */
  handleLogout(showMessage: boolean = true): void {
    try {
      // Clear localStorage
      localStorage.clear();
      
      // Clear sessionStorage
      sessionStorage.clear();
      
      // Clear all cookies
      this.clearAllCookies();
      
      // Show success message if requested
      if (showMessage) {
        this.toastr.success('Signed Out Successfully');
      }
      
      // Navigate to login page
      this.router.navigate(['']);
      
      console.log('User logged out - all storage cleared');
    } catch (error) {
      console.error('Error during logout process:', error);
      // Still try to navigate even if clearing fails
      this.router.navigate(['']);
    }
  }

  /**
   * Handle 401 unauthorized error - automatic logout
   */
  handle401Error(): void {
    this.toastr.error('Session expired. Please login again.');
    this.handleLogout(false); // Don't show success message for 401
  }

  /**
   * Clear all cookies from the domain
   */
  private clearAllCookies(): void {
    try {
      // Get all cookies
      const cookies = document.cookie.split(";");
      
      // Clear each cookie
      for (let cookie of cookies) {
        const eqPos = cookie.indexOf("=");
        const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();
        
        if (name) {
          // Clear cookie for current path
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
          
          // Clear cookie for root path
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;`;
          
          // Clear cookie for current domain
          const domain = window.location.hostname;
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${domain}`;
          
          // Clear cookie for parent domain (if subdomain)
          if (domain.includes('.')) {
            const parentDomain = '.' + domain.split('.').slice(-2).join('.');
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${parentDomain}`;
          }
        }
      }
    } catch (error) {
      console.error('Error clearing cookies:', error);
    }
  }
}
