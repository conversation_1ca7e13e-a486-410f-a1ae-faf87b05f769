import { Injectable } from '@angular/core';
import { ToastrService, IndividualConfig } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class ToastFixService {
  private originalSuccess: any;
  private originalError: any;
  private originalWarning: any;
  private originalInfo: any;
  private isDevServer: boolean;
  private initialized: boolean = false;

  constructor(private toastr: ToastrService) {
    this.isDevServer = window.location.host.includes('localhost') || window.location.host.includes('127.0.0.1');
    
    if (this.isDevServer && !this.initialized) {
      // Store original methods
      this.originalSuccess = this.toastr.success;
      this.originalError = this.toastr.error;
      this.originalWarning = this.toastr.warning;
      this.originalInfo = this.toastr.info;
      
      // Override global toastr methods
      this.toastr.success = (message?: string, title?: string, override?: Partial<IndividualConfig>) => {
        const result = this.originalSuccess.call(this.toastr, message, title, override);
        this.fixToastVisibility();
        return result;
      };
      
      this.toastr.error = (message?: string, title?: string, override?: Partial<IndividualConfig>) => {
        const result = this.originalError.call(this.toastr, message, title, override);
        this.fixToastVisibility();
        return result;
      };
      
      this.toastr.warning = (message?: string, title?: string, override?: Partial<IndividualConfig>) => {
        const result = this.originalWarning.call(this.toastr, message, title, override);
        this.fixToastVisibility();
        return result;
      };
      
      this.toastr.info = (message?: string, title?: string, override?: Partial<IndividualConfig>) => {
        const result = this.originalInfo.call(this.toastr, message, title, override);
        this.fixToastVisibility();
        return result;
      };
      
      this.initialized = true;
    }
  }

  /**
   * Fix toast visibility manually
   */
  fixToastVisibility(): void {
    if (!this.isDevServer) return;
    
    setTimeout(() => {
      const toastContainer = document.querySelector('.toast-container');
      if (toastContainer) {
        toastContainer.setAttribute('style', 'position: fixed !important; z-index: 999999 !important; pointer-events: auto !important; top: 12px !important; right: 12px !important; width: auto !important; max-width: 350px !important; display: block !important; visibility: visible !important;');
        
        const toasts = toastContainer.querySelectorAll('.ngx-toastr');
        toasts.forEach(toast => {
          toast.setAttribute('style', 'opacity: 1 !important; box-shadow: 0 0 12px rgba(0, 0, 0, 0.25) !important; display: block !important; visibility: visible !important; pointer-events: auto !important;');
        });
      }
    }, 0);
  }
}
