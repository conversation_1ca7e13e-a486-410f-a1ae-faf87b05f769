import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class NumberFormatService {

  constructor() {}

  // formatNumber(value: any): string | number {
  //   if (value === null || value === undefined || value === 0) {
  //     return '0';
  //   }

  //   const num = Number(value);
  //   if (isNaN(num)) {
  //     return 'NA';
  //   }

  //   if (Number.isInteger(num) || num % 1 === 0) {
  //     return Math.round(num).toString();
  //   }
    
  //   return parseFloat(num.toFixed(2)).toString();
  // }

formatNumber(value: any): string {

  if (value === null || value === undefined || value === 0) {
    return '$0';
  }

  const num = Number(value);
  if (isNaN(num)) {
    return 'NA';
  }

  if (Number.isInteger(num)) {
    return `$${num.toLocaleString('en-US')}`;
  } else {
    return `$${num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  }
}

formatPercentage(value: any): string {

  if (value === null || value === undefined || value === 0) {
    return '0%';
  }

  const num = Number(value);
  if (isNaN(num)) {
    return 'NA';
  }

  if (Number.isInteger(num)) {
    return `${num}%`;
  } else {
    return `${num.toFixed(2)}%`;
  }
}


}

