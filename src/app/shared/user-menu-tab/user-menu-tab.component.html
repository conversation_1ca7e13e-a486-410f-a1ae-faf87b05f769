<div class="main-campaign">
  <div class="panel panel-primary">
    <div class="panel-body">
      <div class="wizard">
        <a
          *ngFor="let menu of totaMenulList"
          [routerLink]="menu?.path"
          class="profile-tab user-tab-width no-decoration"
          [ngClass]="{ 'current-tab': currentTab == menu?.path.split('/')[2] }"
        >
          <i class="fa fa-users tractor-icon" aria-hidden="true"></i>
          <span>{{ menu?.data?.menu?.title }}</span>
        </a>
      </div>
    </div>
  </div>
</div>
