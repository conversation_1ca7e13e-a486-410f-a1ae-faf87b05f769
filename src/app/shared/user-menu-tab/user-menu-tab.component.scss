@import "../../pages/users/users.scss";

.panel-body {
  .wizard {
    min-width: 100%;
    float: left;
    background: #fff;
    margin: 0;
    border: 1px solid #E0E0E0;
    font-size: 14px;
    .user-tab-width {
      width: 25%;
    }
    .current-tab {
      font-size: 14px;
      border-bottom: 3px solid #FF8033;
    }
  }
}

@media only screen and (max-width: 900px) {
  .panel-body {
    .wizard {
      .user-tab-width {
        width: 100%;
      }
    }
  }
}
