import {  waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';

import { UserMenuTabComponent } from './user-menu-tab.component';

describe('UserMenuTabComponent', () => {
  let component: UserMenuTabComponent;
  let fixture: ComponentFixture<UserMenuTabComponent>;

  beforeEach( waitForAsync(() => {
    TestBed.configureTestingModule({
    imports: [UserMenuTabComponent]
})
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UserMenuTabComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
