import { Component, Input, OnInit } from "@angular/core";
import { OnChanges } from "@angular/core";
import { SimpleChanges } from "@angular/core";
import { ActivatedRoute, RouterLink } from "@angular/router";
import { NgFor, NgClass } from "@angular/common";

@Component({
    selector: "nga-app-user-menu-tab",
    templateUrl: "./user-menu-tab.component.html",
    styleUrls: ["./user-menu-tab.component.scss"],
    imports: [NgFor, RouterLink, NgClass]
})
export class UserMenuTabComponent implements OnInit, OnChanges {
  currentTab: string = "admin";
  @Input() userMenu: any = [];
  totaMenulList: any = [];

  constructor(private routes: ActivatedRoute) {}
  ngOnInit() {
    this.routes.params.subscribe((item) => {
      if (!item["tab"]) {
        this.currentTab = "admin";
      } else if (item["tab"] === "af-swal-employee") {
        this.currentTab = "af-swal-employee";
      } else if (item["tab"] === "distributor") {
        this.currentTab = "distributor";
      } else if (item["tab"] === "farmer") {
        this.currentTab = "farmer";
      } else if (item["tab"] === "retailer") {
        this.currentTab = "retailer";
      } else {
        this.currentTab = "admin";
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes["userMenu"].currentValue) {
      this.totaMenulList = changes["userMenu"].currentValue;
    }
  }
}
