import {Injectable} from "@angular/core";
import * as CryptoJ<PERSON> from 'crypto-js';
import {environment} from "../../../environments/environment";

@Injectable({
  providedIn: "root"
})
export class Utility {
  
  constructor() {}

  encrypt(text: string) {
    let encryptedBase64Key =  environment.base64;
    let parsedBase64Key = CryptoJS.enc.Base64.parse(encryptedBase64Key);
    let encryptedData = CryptoJS.AES.encrypt(text, parsedBase64Key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    return encryptedData.toString();
  } 

  encryptString(text: any) {
    const payload = JSON.stringify(text)
    let encryptedBase64Key =  environment.base64;
    let parsedBase64Key = CryptoJS.enc.Base64.parse(encryptedBase64Key);
    let encryptedData = CryptoJS.AES.encrypt(payload, parsedBase64Key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    return {"encryptedBody": encryptedData.toString()};
  } 

  decrypt(text: string): string {
    try {
      const parsedBase64Key = CryptoJS.enc.Base64.parse(environment.base64);
      const decryptedData = CryptoJS.AES.decrypt(text, parsedBase64Key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });
  
      const decryptedText = decryptedData.toString(CryptoJS.enc.Utf8);
  
      // If decryption fails, this string will be empty
      if (!decryptedText) {
        throw new Error('Decryption returned empty string');
      }
  
      return decryptedText;
    } catch (error) {
      console.error('Decryption error:', error);
      // Return original text assuming it's not encrypted
      return text;
    }
  }
  
  decryptString(data: any): string {
    try {
      // 🔒 If already encrypted body object
      const payload = typeof data === 'string' ? JSON.parse(data) : data;
      const encrypted = payload?.encryptedBody || data;
  
      if (!encrypted || typeof encrypted !== 'string') {
        throw new Error('Missing encryptedBody');
      }
  
      const parsedBase64Key = CryptoJS.enc.Base64.parse(environment.base64);
      const decryptedData = CryptoJS.AES.decrypt(encrypted, parsedBase64Key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });
  
      const decryptedText = decryptedData.toString(CryptoJS.enc.Utf8);
      if (!decryptedText) throw new Error('Decryption returned empty');
  
      return decryptedText;
    } catch (error) {
      console.error('Decryption error:', error);
      return typeof data === 'string' ? data : JSON.stringify(data);
    }
  }

  decryptStringError(encryptedData: string): string {
    try {
      if (typeof encryptedData !== 'string' || !encryptedData) {
        throw new Error('Invalid input: encryptedData must be a non-empty string.');
      }
      const parsedBase64Key = CryptoJS.enc.Base64.parse(environment.base64);
      const decryptedBytes = CryptoJS.AES.decrypt(encryptedData, parsedBase64Key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });
      const decryptedText = decryptedBytes.toString(CryptoJS.enc.Utf8);
      if (!decryptedText) {
        throw new Error('Decryption resulted in an empty string. Possible incorrect key or data corruption.');
      }
      return decryptedText;
    } catch (error) {
      console.error('Decryption error:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to decrypt string: ${errorMessage}`);
    }
  }

  decryptErrorMessage(error: any): string {
    try {
      const encrypted = error?.error?.encryptedBody;
  
      if (!encrypted || typeof encrypted !== 'string') {
        return 'Invalid error format or missing encryptedBody.';
      }
  
      const parsedBase64Key = CryptoJS.enc.Base64.parse(environment.base64);
      const decryptedData = CryptoJS.AES.decrypt(encrypted, parsedBase64Key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });
  
      const decryptedText = decryptedData.toString(CryptoJS.enc.Utf8);
  
      if (!decryptedText) {
        return 'Decryption returned empty string.';
      }
  
      try {
        const parsed = JSON.parse(decryptedText);
        return parsed?.message || JSON.stringify(parsed);
      } catch {
        return decryptedText;
      }
  
    } catch (e) {
      console.error('Error during error decryption:', e);
      return 'Failed to decrypt error message.';
    }
  }

  decryptLSDAta(data: any): string {
    try {
      let encrypted: string;
  
      if (typeof data === 'string') {
        try {
          // Try to parse as JSON, if fails, treat as raw
          const payload = JSON.parse(data);
          encrypted = payload.encryptedBody || data;
        } catch {
          encrypted = data; // it's just a base64 string
        }
      } else {
        encrypted = data?.encryptedBody || data;
      }
  
      const parsedBase64Key = CryptoJS.enc.Base64.parse(environment.base64);
      const decryptedData = CryptoJS.AES.decrypt(encrypted, parsedBase64Key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });
  
      const decryptedText = decryptedData.toString(CryptoJS.enc.Utf8);
      // if (!decryptedText) throw new Error('Decryption returned empty');
  
      return decryptedText;
    } catch (error) {
      console.error('Decryption error:', error);
      return typeof data === 'string' ? data : JSON.stringify(data);
    }
  }  

  formatString(value: any): string {
    if (typeof value !== 'string') {
      if (value !== null && value !== undefined) {
        value = String(value);
      } else {
        return '';
      }
    }
  
    value = value.trim();
  
    if (value.length === 0) {
      return '';
    }
  
    return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  }

  formatNumberTwoDigits(value: number | string): string {
    if (value == null || value === '') return 'NA';
  
    const num = Number(value);
    if (isNaN(num)) return 'NA';
  
    // Check if whole number (no decimals)
    if (num % 1 === 0) {
      return num.toString();
    } else {
      return num.toFixed(2);
    }
  }


formatUSD(value: number | string | undefined | null): string {
  if (value === null || value === undefined || value === '' || isNaN(Number(value))) {
    return 'NA';
  }
 
  const number = Number(value);
  return '$' + number.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
 }

  preventSpecialCharacters(event: KeyboardEvent): void {
    const regex = /^[a-zA-Z0-9\s]*$/; // Allow only letters, numbers, and spaces
    const key = event.key;
    if (!regex.test(key)) {
      event.preventDefault(); // Prevent the input of special characters
    }
  } 
  capitalizeWords(str: string | undefined | null): string {
    if (!str) return 'NA';
    return str
      .toLowerCase()
      .split(' ')
      .filter(word => word.trim() !== '')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

formatActivityName(name: string): string {
  if (!name || typeof name !== 'string') {
    return '';
  }
  return name
    .split('_')
    .map(part => part.toUpperCase())
    .join(' ');                 
}

  toUpperCaseUtil(str: string | undefined | null): string {
    if (!str || str.trim() === '') return 'NA';
    return str.toUpperCase();
  }
}
