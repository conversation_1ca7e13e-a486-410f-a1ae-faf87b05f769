@import "../../../theme/sass/conf/conf";
$height: 52px;

.ba-back-top {
  position: fixed;
  width: $height;
  height: $height;
  cursor: pointer;
  z-index: 9999;
  display: none;
  text-decoration: none;
  right: 40px;
  bottom: 20px !important;
  font-size: 45px;
  text-align: center;
  opacity: 0.4;
  color: white;
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 50%;
  line-height: 46px;
  &:hover {
    opacity: 0.8;
  }
}




.back-to-top-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  padding: 10px;
  cursor: pointer;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
}

.back-to-top-button:hover {
  background-color: #0056b3;
}
