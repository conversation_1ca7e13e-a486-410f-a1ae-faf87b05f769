import { Component } from "@angular/core";
import { GlobalState } from "../../../helpers/global.state";
import { GlobalEvents } from "../../../helpers/global.events";
import { AuthenticationHelper } from "../../../helpers/authentication";
import { Location, NgClass, NgIf } from "@angular/common";
@Component({
    selector: "ba-content-top",
    standalone: true,
    styleUrls: ["./baContentTop.scss"],
    templateUrl: "./baContentTop.html",
    imports: [NgClass, NgIf]
})
export class BaContentTop {
  public activePageTitle: string = "";
  isTM: boolean = false;
  isMobileView: boolean = false;
  showBack: boolean = false;
  constructor(
    private _state: GlobalState,
    private events: GlobalEvents,
    private location: Location
  ) {
    this._state.subscribe("menu.activeLink", (activeLink: any) => {
      this.activePageTitle = activeLink.title;
    });
    this.isTM = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;

    this.isMobileView = window.innerWidth <= 1023 ? true : false;
    this.events.getChangedContentTopText().subscribe((item) => {
      if (item) {
        this.activePageTitle = item;
      }
    });
    this.events.showBack.subscribe((item) => {
      if (item) {
        this.showBack = true;
      } else {
        this.showBack = false;
      }
    });
  }

  ngAfterViewInit() {
    this.events.getChangedContentTopText().subscribe((item) => {
      if (item) {
        this.activePageTitle = item;
      }
    });
    this.events.showBack.subscribe((item) => {
      if (item) {
        this.showBack = true;
      } else {
        this.showBack = false;
      }
    });
  }

  onClicked() {
    this.location.back();
  }
}
