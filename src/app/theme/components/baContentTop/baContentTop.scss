@import "../../sass/conf/conf";

.content-top {
  padding: 5px 5px 5px;
  background-color: #ffffff;
  .backBtn {
    cursor: pointer;
    outline: none;
  }
}

.content-top-TM {
  padding-left: 0;
}

.padding-right-0 {
  padding-right: 0;
}

.padding-bottom-1 {
  padding-bottom: 1px;
}

.adjust-back-btn {
  position: relative;
  bottom: 8px;
}

.al-content {
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  z-index: 0;
  padding: 8px 27px 8px 40px;
}

.back-btn {
  background-color: #FF6F1F;
  border-radius: 3px;
  color: white;
  font-size: 15px;
  cursor: pointer;
}

h1.al-title {
  font-weight: $font-bold;
  color: #222222;
  float: left;
  width: auto;
  margin: 0;
  padding: 6px;
  font-size: 18px;
  // text-transform: uppercase;
  opacity: 0.9;
}

.al-breadcrumb {
  background: none;
  color: #374767;
  padding: 0;
  margin: 0;
  float: right;
  padding-top: 5px;
  li {
    font-size: 18px;
    font-weight: $font-normal;
    a {
      color: #374767;
    }

    &.breadcrumb-item.active {
      color: #374767;
    }
  }
}

.al-look {
  float: right;
  margin-right: 10px;
  padding-top: 10px;
  > a {
    font-size: 19px;
  }
}
