import { Component, Input, Output, EventEmitter } from "@angular/core";
import { Router, NavigationEnd } from "@angular/router";
import { Subscription } from "rxjs";
import { BaMenuService } from "../../services";
import { GlobalState } from "../../../helpers/global.state";
import { BaMenuItem } from "./components/baMenuItem/baMenuItem.component";
import { NgFor, NgStyle, NgClass } from "@angular/common";
import { BaSlimScroll } from "../../directives/baSlimScroll/baSlimScroll.directive";
import jQuery from "jquery";

@Component({
    selector: "ba-menu",
    templateUrl: "./baMenu.html",
    styleUrls: ["./baMenu.scss"],
    imports: [BaSlimScroll, NgFor, BaMenuItem, NgStyle, NgClass]
})
export class BaMenu {
  @Input() sidebarCollapsed: boolean = false;
  @Input()
  menuHeight!: number;

  @Output() expandMenu = new EventEmitter<any>();

  public menuItems!: any[];
  protected menuItemsSub!: Subscription;
  public showHoverElem!: boolean;
  public hoverElemHeight!: number;
  public hoverElemTop!: number;
  protected onRouteChange!: Subscription;
  public outOfArea: number = -200;
  activeMenu: string = "";

  constructor(
    private router: Router,
    private service: BaMenuService,
    private state: GlobalState
  ) {}

  public updateMenu(newMenuItems: any) {
    this.menuItems = newMenuItems;
    this.selectMenuAndNotify();
  }

  public selectMenuAndNotify(): void {
    this.activeMenu = this.router.url.split("/")[1];
    if (this.activeMenu && this.activeMenu.includes("?")) {
      let path = this.activeMenu.split("?")[0];
      this.activeMenu = "";
      this.activeMenu = path;
    }
    if (this.menuItems) {
      this.menuItems.forEach((item, index) => {
        item.route.path == this.activeMenu
          ? (item["isChildSelected"] = true)
          : (item["isChildSelected"] = false);
      });
      this.menuItems = this.service.selectMenuItem(this.menuItems);
      this.state.notifyDataChanged(
        "menu.activeLink",
        this.service.getCurrentItem()
      );
    }
  }

  public ngOnInit(): void {
    this.onRouteChange = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        if (this.menuItems) {
          this.selectMenuAndNotify();
        } else {
          setTimeout(() => this.selectMenuAndNotify());
        }
      }
    });

    this.menuItemsSub = this.service.menuItems.subscribe(
      this.updateMenu.bind(this)
    );
  }

  public ngOnDestroy(): void {
    this.onRouteChange.unsubscribe();
    this.menuItemsSub.unsubscribe();
  }

  public hoverItem($event: any): void {
    this.showHoverElem = true;
    this.hoverElemHeight = $event.currentTarget.clientHeight;
    this.hoverElemTop = $event.currentTarget.getBoundingClientRect().top - 66;
  }

  public toggleSubMenu($event: any): boolean {
    let submenu = jQuery($event.currentTarget).next();

    if (this.sidebarCollapsed) {
      this.expandMenu.emit(null);
      if (!$event.item.expanded) {
        $event.item.expanded = true;
      }
    } else {
      $event.item.expanded = !$event.item.expanded;
      submenu.slideToggle();
    }

    return false;
  }
}
