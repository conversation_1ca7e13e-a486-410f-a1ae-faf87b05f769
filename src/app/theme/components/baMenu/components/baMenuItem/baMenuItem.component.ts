import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
} from "@angular/core";
import { RouterLink } from "@angular/router";
import { NgIf, NgClass, NgFor } from "@angular/common";
import { DashboardService } from "src/app/app-services/dashboard.service";
import { MatTooltipModule } from '@angular/material/tooltip';
import { ChangeDetectorRef } from '@angular/core';

@Component({
    selector: "ba-menu-item",
    templateUrl: "./baMenuItem.html",
    styleUrls: ["./baMenuItem.scss"],
    imports: [NgIf, NgClass, RouterLink, NgFor, forwardRef(() => BaMenuItem), MatTooltipModule]
})
export class BaMenuItem {
  @Input() menuItem: any;
  @Input() child: boolean = false;
  stopClickEvent: boolean = false;
  isEllipsisActive = false;

  @Output() itemHover = new EventEmitter<any>();
  @Output() toggleSubMenu = new EventEmitter<any>();
   roleId: any = 1
supportTicketCount: any;

  constructor(private dashboardService  : DashboardService, private cdr: ChangeDetectorRef){
  }

  checkEllipsis(element: HTMLElement, title: string) {

    this.isEllipsisActive = element.offsetWidth < element.scrollWidth;
    this.cdr.detectChanges();
  }

  public ngOnInit(): void {
    this.dashboardService._disabledSidebar.subscribe((res) => {
      this.stopClickEvent = res;
    });
  }
  public onHoverItem($event: any): void {   
    this.itemHover.emit($event);
  }

  public onToggleSubMenu($event: any, item: any): boolean {
    $event.item = item;
    this.toggleSubMenu.emit($event);
    return false;
  }
}
