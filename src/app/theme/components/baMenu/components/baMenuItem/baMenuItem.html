<li class="list-width" *ngIf="!menuItem.hidden"
  [ngClass]="{
    'al-sidebar-list-item': !menuItem.children,
    'ba-sidebar-sublist-item': menuItem.children,
    'selected': (menuItem.selected || menuItem.isChildSelected) && !menuItem.expanded,
    'with-sub-menu': menuItem.children,
    'ba-sidebar-item-expanded': menuItem.expanded
  }"
  style="display: flex; flex-direction: column;">

  <span *ngIf="stopClickEvent === true" style="display: flex;">
    <a *ngIf="!menuItem.children && !menuItem.url"
      (mouseenter)="onHoverItem($event)"
      class="al-sidebar-list-link"
      style="display: flex; align-items: center; padding: 8px 11px; text-decoration: none;">
      
      <img *ngIf="menuItem.icon"
        [src]="menuItem.image"
        alt=""
        style="width: 20px; height: 20px; margin-right: 10px; flex-shrink: 0;" />
      
      <span translate
        [ngClass]="{ 'multi-line': menuItem?.large }"
        style="max-width: 155px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
        {{ menuItem.title }}
      </span>
    </a>
  </span>

  <span *ngIf="stopClickEvent === false" style="display: flex;">
    <a *ngIf="!menuItem.children && !menuItem.url"
       [routerLink]="menuItem.route.paths"
       (mouseenter)="onHoverItem($event)"
       class="al-sidebar-list-link"
       style="display: flex; align-items: center; padding: 8px 11px; text-decoration: none;">
      
      <span class="supportTicketAlignment" *ngIf="menuItem.title === '  Support'" style="margin-right: 8px;">{{supportTicketCount}}</span>
      
      <img *ngIf="menuItem.icon"
           [src]="menuItem.image"
           alt=""
           style="width: 20px; height: 20px; margin-right: 10px; flex-shrink: 0;" />
      
      <span translate
            [ngClass]="{ 'multi-line': menuItem?.large }"
            [attr.title]="menuItem.title"
            style="max-width: 155px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block; vertical-align: middle;">
        {{ menuItem.title }}
      </span>
    </a>
  </span>

  <a *ngIf="!menuItem.children && menuItem.url"
     (mouseenter)="onHoverItem($event)"
     [href]="menuItem.url"
     [target]="menuItem.target"
     class="al-sidebar-list-link"
     style="display: flex; align-items: center; padding: 8px 11px; text-decoration: none;">
    
    <img *ngIf="menuItem.icon"
         [src]="menuItem.image"
         alt=""
         style="margin-right: 12px; width: 20px; height: 20px;" />
    
    <span translate [ngClass]="{'multi-line': menuItem?.large}" style="max-width: 155px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
      {{ menuItem.title }}
    </span>
  </a>

  <a *ngIf="menuItem.children"
     (mouseenter)="onHoverItem($event)"
     href
     (click)="onToggleSubMenu($event, menuItem)"
     class="al-sidebar-list-link"
     style="display: flex;  position:relative; align-items: center; padding: 8px 11px; text-decoration: none;">

    <b class="fa"
       [class.adminSidebar]="roleId == 1"
       [class.otherSidebar]="roleId != 1"
       [ngClass]="{'fa-angle-up': menuItem.expanded, 'fa-angle-down': !menuItem.expanded}"
       style="margin-right: 10px; font-size: 14px; margin-top: 3px; font-weight: 600; position: absolute;">
    </b>

    <img *ngIf="menuItem.icon"
         [src]="menuItem.image"
         alt=""
         style="margin-right: 10px; width: 20px; height: 20px;" />
    
    <span translate [ngClass]="{'multi-line': menuItem?.large}" style="max-width: 155px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
      {{ menuItem.title }}
    </span>
  </a>

  <ul *ngIf="menuItem.children"
      class="al-sidebar-sublist"
      [ngClass]="{'slide-right': menuItem.slideRight}"
      >
    <ba-menu-item
      *ngFor="let subItem of menuItem.children"
      [menuItem]="subItem"
      (itemHover)="onHoverItem($event)"
      (toggleSubMenu)="onToggleSubMenu($event, subItem)">
    </ba-menu-item>
    
  </ul>
</li>
