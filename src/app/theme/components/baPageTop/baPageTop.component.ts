import { Component, inject } from "@angular/core";
import { GlobalState } from "../../../helpers/global.state";
import { Router } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { AppConstant } from "../../../constants/app.constant";
import { UtilityHelper } from "../../../helpers/utility";
import { AuthenticationHelper } from "../../../helpers/authentication";
import { CommonModule, NgClass } from "@angular/common";
import { BaScrollPosition } from "../../directives/baScrollPosition/baScrollPosition.directive";
import { MatMenuModule } from "@angular/material/menu";
import { DashboardService } from "src/app/app-services/dashboard.service";
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { Profile } from "src/app/pages/user-profile";
import { Utility } from "src/app/shared/utility/utility";

@Component({
  selector: "ba-page-top",
  templateUrl: "./baPageTop.html",
  styleUrls: ["./baPageTop.scss"],
  imports: [BaScrollPosition, NgClass, CommonModule, MatMenuModule, MatDialogModule]
})
export class BaPageTop {
  public isScrolled: boolean = false;
  loyaltyImage: any = AppConstant.LOYALTY_IMAGE;
  public isMenuCollapsed: boolean = false;
  public userName: any;
  public userProfileImage!: string;
  isTM: boolean = false;
  isMobileViewPageTop: boolean = false;
  stopHoverEvent: boolean = false;
  path: string;
  // readonly dialog = inject(MatDialog);
  constructor(
    public toastr: ToastrService,
    private state: GlobalState,
    public router: Router,
    public utility: UtilityHelper,
    private dashboardService: DashboardService,
    private dialog: MatDialog,
    private Utilitymain: Utility,
  ) {

    this.dashboardService._disabledSidebar.subscribe((res: any) => {
      this.stopHoverEvent = res;

    });

    this.state.subscribe("menu.isCollapsed", (isCollapsed: any) => {
      this.isMenuCollapsed = isCollapsed;
    });
    this.path = this.router.url;
    const encryptedName = localStorage.getItem("firstName");
    this.userName = this.Utilitymain.decryptLSDAta(encryptedName);
    this.dashboardService._FirstName.subscribe((res) => {
      this.userName = res;
    })
    this.isTM = parseInt(AuthenticationHelper.getRoleID() ?? "") === 2;
    this.isMobileViewPageTop = window.innerWidth <= 1023 ? true : false;
    this.router.events.subscribe((res: any) => {
      this.path = res.url;
    });
  }

  public toggleMenu() {
    this.isMenuCollapsed = !this.isMenuCollapsed;
    this.state.notifyDataChanged("menu.isCollapsed", this.isMenuCollapsed);
    return false;
  }
  public scrolledChanged(isScrolled: any) {
    this.isScrolled = isScrolled;
  }

  public loggedOff() {
    localStorage.clear();
    this.router.navigate(["sign-in"]);
    this.toastr.success("Signed Out Successfully");
  }
  public navigateToProfile() {
    // this.router.navigate(["profile"]);
    this.dialog.open(Profile, {
      width: '600px',
      height: 'auto',
      maxHeight: '90vh', 
      // panelClass: 'custom-dialog-class'  // Optional custom CSS
    });
    
  }

  public navigateToDashboard() {
    let roleID = parseInt(localStorage.getItem("roleID") ?? "0");

    if (this.router.url === '/dashboard') {
      window.location.reload();
    } else {
      this.router.navigate(["dashboard"]);
    }
  }

  onWindowResizePageTop() {
    this.isMobileViewPageTop = window.innerWidth <= 1023 ? true : false;
  }
}
