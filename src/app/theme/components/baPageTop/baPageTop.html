<div (window:resize)="onWindowResizePageTop();"></div>
<div
  class="page-top clearfix"
  baScrollPosition
  (scrollChange)="scrolledChanged($event)"
  [ngClass]="{'page-top-TM': (isTM && !isMobileViewPageTop), scrolled: isScrolled}"
>
  <a (click)="navigateToDashboard()" class="al-logo clearfix"
    ><img src="../../../../assets/img/UPL_OpenAg.svg"
  /></a>

  <a href (click)="toggleMenu()" class="collapse-menu-link ion-navicon"></a>

  <div class="user-profile clearfix">
    <div class="dropdown al-user-profile">
      <ng-container *ngIf="!stopHoverEvent; else showPlaceholder">
        <a
          class="profile-toggle-link dropdown-toggle"
          id="user-profile-dd"
          data-toggle="dropdown"
          aria-expanded="false"
          [matMenuTriggerFor]="toolbarUserMenu"
        >
          <div class="user-picture-circle">
            <img
              [src]="userProfileImage || '../../../../assets/img/default-user.svg'"
              alt="User Profile"
            />
          </div>
          <div class="profile-user-name">
            <span class="capitalise">
              Hi, {{userName}} <i class="fa fa-angle-down"></i>
            </span>
          </div>
        </a>
      </ng-container>
      <ng-template #showPlaceholder>
        <a
          class="profile-toggle-link dropdown-toggle"
          id="user-profile-dd"
          data-toggle="dropdown"
          aria-expanded="false" 
        >
          <div class="user-picture-circle">
            <img
              src="{{userProfileImage}}"
              alt='../../../../assets/img/default-user.svg'
            />
          </div>
          <div class="profile-user-name">
            <span class="capitalise">
              Hi, {{userName}} <i class="fa fa-angle-down"></i>
            </span>
          </div>
        </a>
      </ng-template>
    </div>
    
    
    
    
  </div>
</div>

<mat-menu #toolbarUserMenu="matMenu" class="headerMatMenu">
  <div
    (click)="navigateToProfile()"
    mat-menu-item
    class="user-profile-item userName"
  >
    <i class="fa fa-cog add-pading"></i> &nbsp; Account
  </div>
  <a (click)="loggedOff()" class="log-out" mat-menu-item>
    <i class="fa fa-power-off add-pading"></i> &nbsp; Sign Out
  </a> 
</mat-menu>
