@import "../../sass/conf/conf";

:host {
  .page-top {
    background-color: #FF6F1F;
    top: 0px;

    position: fixed;
    z-index: 904;
    box-shadow: 2px 0 3px rgba(0, 0, 0, 0.7);
    height: 56px;
    width: 100%;
    min-width: $resMin;
    padding: 0 10px;

    .dropdown-toggle::after {
      display: none;
    }
  }
  .page-top-TM {
    padding: 0 38px 0 40px;
  }
  .signout {
    cursor: pointer;
    &:hover,
    &:active,
    &:focus,
    &:visited {
      color: #292b2c !important;
    }
  }

  .blur {
    .page-top.scrolled {
      background-color: rgba(black, 0.85);
    }
  }

  a.al-logo {
    padding-top: 6px;
    color: $sidebar-text;
    display: block;
    font-size: 24px;
    font-family: $font-family;
    white-space: nowrap;
    float: left;
    outline: none !important;
    line-height: 40px;
    cursor: pointer;
    span {
      color: $primary;
    }
    img {
      object-fit: contain; /* or cover */
    }    
  }

  a.al-logo:hover {
    color: $primary;
  }

  .user-profile {
    float: right;
    min-width: 230px;
    margin-top: 8px;
    color: #374767;
  }

  .links-container {
    width: 34%;
    height: 66px;
    display: flex;
    display: -webkit-box;
    display: -ms-flexbox;
    justify-content: space-between;
    -webkit-box-pack: space-between;
    -ms-flex-pack: space-between;
    align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    text-align: right;
    .link {
      cursor: pointer;
      color: rgb(102, 102, 102);
      &:hover,
      &:focus {
        color: #FF8033;
        text-decoration: none;
      }
    }
    .link-decoration {
      color: #FF8033;
      text-decoration: none;
    }
  }

  .profile-user-name {
    align-items: center;
    float: right;
    margin-top: 10px;
    padding-left: 5px;
    color: white;
  }

  .user-picture-circle {
    display: inline-block;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-repeat: no-repeat;
    background-position: center;
    margin-top: 2.5px;
  }

  .al-user-profile {
    float: right;
    margin-right: 12px;
    transition: all 0.15s ease-in-out;
    padding: 0;
    border: 0;
    opacity: 1;
    position: relative;
    ul.profile-dropdown:after {
      bottom: 100%;
      right: 0;
      border: solid transparent;
      content: " ";
      height: 0;
      width: 0;
      position: absolute;
      pointer-events: none;
      border-color: rgba(255, 255, 255, 0);
      border-bottom-color: #fff;
      border-width: 10px;
      margin-right: 28px;
    }
    a {
      display: block;
    }
    img {
      width: 35px;
      height: 35px;
      border-radius: 50%;
      cursor: pointer;
    }
  }

  a.refresh-data {
    color: $sidebar-text;
    font-size: 13px;
    text-decoration: none;
    font-weight: $font-normal;
    float: right;
    margin-top: 13px;
    margin-right: 26px;

    &:hover {
      color: $warning !important;
    }
  }

  a.collapse-menu-link {
    font-size: 31px;
    cursor: pointer;
    display: block;
    text-decoration: none;
    line-height: 42px;
    color: white;
    padding: 0;
    float: left;
    margin: 5px 0 0 25px;

    &:hover {
      text-decoration: none;
      color: white;
    }
  }

  .al-skin-dropdown {
    float: right;
    margin-top: 14px;
    margin-right: 26px;

    .tpl-skin-panel {
      max-height: 300px;
      overflow-y: scroll;
      overflow-x: hidden;
    }
  }

  .icon-palette {
    display: inline-block;
    width: 14px;
    height: 13px;
    @include bg("theme/palette.png");
    background-size: cover;
  }

  .search {
    text-shadow: none;
    font-size: 13px;
    line-height: 25px;
    transition: all 0.5s ease;
    white-space: nowrap;
    overflow: hidden;
    width: 162px;
    float: left;
    margin: 20px 0 0 30px;

    label {
      cursor: pointer;
    }
    i {
      width: 16px;
      display: inline-block;
      cursor: pointer;
      padding-left: 1px;
      font-size: 16px;
      margin-right: 13px;
    }
    input {
      color: $sidebar-text;
      background: none;
      border: none;
      outline: none;
      width: 120px;
      padding: 0;
      margin: 0 0 0 -3px;
      height: 27px;
    }
  }

  .headerMatMenu{
    width: 200px;
    height: 100px;
  }

  @media screen and (max-width: $resS) {
    .search {
      display: none;
    }
  }

  @media screen and (max-width: $resXS) {
    .page-top {
      padding: 0 20px;
    }
  }

  @media (max-width: $resXXS) {
    .user-profile {
      min-width: 125px;
    }
    a.refresh-data {
      margin-right: 10px;
    }
    a.collapse-menu-link {
      margin-left: 9px;
    }

    .al-skin-dropdown {
      display: none;
    }
  }

  .profile-toggle-link {
    cursor: pointer;
  }
}

.add-pading {
  padding: 10px;
  background-color: #eeecec;
  border-radius: 10px;
  margin: 5px;
}


.log-out:hover{ 
    text-decoration: none;
}
 