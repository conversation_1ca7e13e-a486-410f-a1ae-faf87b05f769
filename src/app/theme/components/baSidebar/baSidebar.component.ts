import { Component, HostListener, ElementRef, EventEmitter, Output } from "@angular/core";
import { GlobalState } from "../../../helpers/global.state";
import { layoutSizes } from "../../../theme";
import { AppConstant } from "../../../constants/app.constant";
import { UtilityHelper } from "../../../helpers/utility";
import { BaMenu } from "../baMenu/baMenu.component";
import { DashboardService } from "src/app/app-services/dashboard.service";
import { CommonModule } from "@angular/common";
import { SidebarServiceService } from "src/app/app-services/sidebar-service.service";
import { Utility } from "src/app/shared/utility/utility";

@Component({
    selector: "ba-sidebar",
    templateUrl: "./baSidebar.html",
    styleUrls: ["./baSidebar.scss"],
    imports: [BaMenu, CommonModule,]
})
export class BaSidebar extends AppConstant {
  public menuHeight!: number;
  public isMenuCollapsed: boolean = false;
  public isMenuShouldCollapsed: boolean = false;
  public userProfileImag!: string;
  public userName: string;
  stopHoverEvent: boolean = false;
  toggleButtonValue: boolean = true;
  openedSidebarvalue: boolean = false;
  @Output() sidebarOpen: EventEmitter<void> = new EventEmitter<void>();



  constructor(
    private elementRef: ElementRef,
    public utility: UtilityHelper,
    private state: GlobalState,
    private dashboardService : DashboardService,
    private sidebarService : SidebarServiceService,
    private utilityMain: Utility,
  ) {
    super();

    this.state.subscribe("menu.isCollapsed", (isCollapsed: any) => {
      this.isMenuCollapsed = isCollapsed;
    });
    let username = this.utilityMain.decryptLSDAta(localStorage.getItem("username") || "Admin");
    this.userName = username;
  }

  public ngOnInit(): void {
    this.menuCollapse();
    this.dashboardService._disabledSidebar.subscribe((res:any) => {
      this.stopHoverEvent = res;   
      
    });
  }

  public toggleMenu(value: boolean) { 
    if (value == this.toggleButtonValue) {
      this.toggleButtonValue = false;
      this.sidebarService.setBooleanValue(this.toggleButtonValue ? true : false )
    } else {
      this.toggleButtonValue = true;
      this.sidebarService.setBooleanValue(this.toggleButtonValue)

          // this.rewardService._sidebarPin.emit(true);
    }
  }

  public ngAfterViewInit(): void {
    setTimeout(() => this.updateSidebarHeight());
  }

  @HostListener("window:resize")
  public onWindowResize(): void {
    let isMenuShouldCollapsed = this._shouldMenuCollapse();
    if (this.isMenuShouldCollapsed !== isMenuShouldCollapsed) {
      this.menuCollapseStateChange(isMenuShouldCollapsed);
    }

    this.isMenuShouldCollapsed = isMenuShouldCollapsed;
    this.updateSidebarHeight();
  }

  public menuExpand(): void { 
    if (this.stopHoverEvent == false) { 
      if (this.toggleButtonValue == true) {
        this.menuCollapseStateChange(false);
        this.openedSidebarvalue = true;
      }
      this.sidebarOpen.emit();
    } else {
    }
  }

  public menuCollapse(): void {
    if (this.stopHoverEvent == false) {
      this.dashboardService._closedPopup.emit(true);
      if (this.toggleButtonValue == true) {
        this.menuCollapseStateChange(true);
        this.openedSidebarvalue = false;
      }
    }
  }

  public menuCollapseStateChange(isCollapsed: boolean): void {
    this.isMenuCollapsed = isCollapsed; 
    this.state.notifyDataChanged("menu.isCollapsed", this.isMenuCollapsed);
  }

  public updateSidebarHeight(): void {
    this.menuHeight =
      this.elementRef.nativeElement.childNodes[0].clientHeight - 84;
  }

  private _shouldMenuCollapse(): boolean {
    return window.innerWidth <= layoutSizes.resWidthCollapseSidebar;
  }
}
