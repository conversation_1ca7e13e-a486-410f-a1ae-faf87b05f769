<aside
  (mouseenter)="menuExpand()"
  (mouseleave)="menuCollapse()"
  class="al-sidebar"
  sidebarResize
> 
<div class="toggle-pin">
  <span *ngIf="openedSidebarvalue == true">
    <span
      *ngIf="this.toggleButtonValue == true"
      (click)="toggleMenu(true)"
      class="toggle-icon-pin"
    >
      <img
        style="position: relative;left: 81%;"
        height="25px"
        width="25px"
        src="../../../../assets/img/Unpinned.svg"
        alt=""
      />
    </span>
    <span
      *ngIf="this.toggleButtonValue == false"
      (click)="toggleMenu(true)"
      class="toggle-icon-unpin"
    >
      <img
        style="position: relative;left: 81%;"
        height="25px"
        width="25px"
        src="../../../../assets/img/Pinned.svg"
        alt=""
      />
    </span>
  </span>
  <div class="userImageDiv"> 
  </div>
</div>
  <ba-menu
    [menuHeight]="menuHeight"
    [sidebarCollapsed]="isMenuCollapsed"
    (expandMenu)="menuExpand()"
  ></ba-menu>
</aside>
