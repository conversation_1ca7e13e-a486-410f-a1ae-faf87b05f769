@import "../../sass/conf/conf";

$sidebar-scroll-width: 4px;
$angle-left: "\f100";
$angle-right: "\f101";

@mixin default-sublist() {
  padding: 0;
  list-style: none;
  position: relative;
  display: none;
  &.expanded {
    display: block;
  }
  > ba-menu-item > li {
    display: block;
    float: none;
    padding: 0;
    border-bottom: none;
    position: relative;
    a {
      display: block;
      text-shadow: none;
      font-size: 13px;
      text-decoration: none;
      color: $sidebar-text;
      padding-left: 52px;
      height: auto;
      line-height: 29px;
    }
    &.selected:not(.with-sub-menu) > a {
      font-size: 14px;
      font-weight: 300;
      background-color: $selected-menu-color;
      border-top: 1px solid rgba(250, 250, 250, 0.4);
      border-bottom: 1px solid rgba(250, 250, 250, 0.4);
    }
  }
}

::ng-deep {
  .al-sidebar {
    width: 210px;
    top: 56px;
    left: 0;
    z-index: 1001;
    display: block;
    min-height: 100%;
    background-color: $sidebar-background;
    height: 100%;
    position: fixed;
    box-shadow: 3px 2px 3px 0px #d3d3d3;
  }

  .al-sidebar-list {
    margin: 0;
    overflow: hidden;
    padding: 18px 0 0 0;
    list-style: none;
  }

  .al-sidebar-sublist .subitem-submenu-list {
    padding-left: 15px;
  }

  .subitem-submenu-link {
    .fa {
      top: 7px;
    }
  }

  .toggle-pin {
    margin: 9px 4px 0 0;
    height: 20px;
    cursor: pointer;
  }

  // .toggle-icon-pin{
  //   cursor: pointer;
  // }

  // .toggle-icon-unpin{
  //   cursor: pointer;
  // }


  .al-sidebar-list-item {
    display: block;
    position: relative;
    float: none;
    padding: 0;
    &.selected:not(.with-sub-menu) {
      border-bottom: 1px solid #EBF3FF;
      border-top: 1px solid #EBF3FF;
      font-size: 14px;
      // font-weight: 300;
      font-weight: 600;
      width: 100%;
      // margin: 0 0px 0 2px;
      // border-radius: 8px 0 0 8px;
      a.al-sidebar-list-link {
        color: $sidebar-text;
        padding-left: 14px;

        b {
          color: $sidebar-text;
        }
      }

      .multi-line{
        font-weight: bolder;
      }
    }
  }
  // .al-sidebar-list-item :hover {
  // }
  .ba-sidebar-item-expanded {
    > ul.al-sidebar-sublist {
      display: block !important;
    }
  }

  .al-sidebar-list-item,
  .ba-sidebar-sublist-item {
    &.ba-sidebar-item-expanded {
      > .al-sidebar-list-link {
        // background-color: red;
        b {
          transform: rotate(180deg);
        }
      }

      > .al-sidebar-sublist {
        display: block;
      }
    }
  }

  a.al-sidebar-list-link {
    display: block;
    height: 42px;
    padding-left: 18px;
    text-shadow: none;
    font-size: 14px;
    text-decoration: none;
    color:#ddd;
    line-height: 42px;
    white-space: nowrap;
    overflow: hidden;
    cursor: pointer;
    i {
      margin-right: 12px;
      width: 16px;
      display: inline-block;
      font-size: 18px;
    }
    b {
      display: block;
      opacity: 1;
      width: 14px;
      height: 14px;
      line-height: 14px;
      text-shadow: none;
      font-size: 18px;
      position: absolute;
      right: 10px;
      top: 12px;
      padding: 0;
      text-align: center;
      color: $sidebar-text;
      transition: transform 0.2s linear;
    }
  }

  .slimScrollBar,
  .slimScrollRail {
    border-radius: 0;
    width: 4px !important;
    left: 210px - 4px;
  }

  .al-sidebar-sublist {
    @include default-sublist();
  }

  .sidebar-hover-elem {
    width: 4px;
    background: $primary;
    position: absolute;
    top: -150px;
    left: 210px - 4px;
    transition: all 0.5s ease;
    transition-property: top, height;
    height: 42px;
    display: none;
  }

  .sidebar-select-elem {
    display: block;
    top: 94px;
  }

  .menu-collapsed {
    .slimScrollBar,
    .slimScrollRail {
      display: none !important;
    }
    .userImageSidebar {
      display: none;
    }
    .userProfileName {
      display: none;
    }
    a.al-sidebar-list-link {
      i {
        width: 20px;
        font-size: 16px;
      }
    }
  }
  .userImageSidebar {
    margin-left: 18px;
    border-radius: 70px;
    outline: none;
    width: 70px;
    height: 70px;
    margin-top: 20px;
    display: inline-block;
    cursor: pointer;
  }
  .userImageDiv {
    display: inline-block;
  }
  .userProfileName {
    display: inline-block;
    margin-left: 12px;
    color: white;
  }
}
