import {
  Directive,
  Input,
  Output,
  ElementRef,
  EventEmitter,
} from "@angular/core";
declare const $: any;
import "jquery-slimscroll";

@Directive({
  selector: "[baSlimScroll]",
  standalone: true,
})
export class BaSlimScroll {
  @Input() public baSlimScrollOptions!: Object;

  constructor(private _elementRef: ElementRef) {}

  ngOnChanges() {
    this._scroll();
  }

  private _scroll() {
    this._destroy();
    this._init();
  }

  private _init(): void {
    $(this._elementRef.nativeElement).slimScroll(this.baSlimScrollOptions);
  }

  private _destroy(): void {
    $(this._elementRef.nativeElement).slimScroll({ destroy: true });
  }
}
