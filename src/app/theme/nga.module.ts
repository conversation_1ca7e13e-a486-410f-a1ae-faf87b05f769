import { ModuleWithProviders } from "@angular/core";
import {
  BaBackTop,
  BaContentTop,
  BaMenuItem,
  BaMenu,
  BaPageTop,
  BaSidebar,
} from "./components";
import { BaScrollPosition, BaSlimScroll } from "./directives";
import { BaMenuService, BaThemePreloader, BaThemeSpinner } from "./services";
import { EmailValidator, EqualPasswordsValidator } from "./validators";
import { PhoneValidator } from "./validators/phone.validator"; 


const NGA_COMPONENTS = [
  BaBackTop,
  BaContentTop,
  BaMenuItem,
  BaMenu,
  BaPageTop,
  BaSidebar,
];

const NGA_DIRECTIVES = [BaScrollPosition, BaSlimScroll];

const NGA_SERVICES = [BaThemePreloader, BaThemeSpinner, BaMenuService];

const NGA_VALIDATORS = [
  EmailValidator,
  EqualPasswordsValidator,
  PhoneValidator,
];


export class NgaModule {
  // TranslateModule.forRoot({
  //   loader: {
  //     provide: TranslateLoader,
  //     useFactory: HttpLoaderFactory,
  //     deps: [HttpClient]
  //   }
  // })
  static forRoot(): ModuleWithProviders<NgaModule> {
    return <ModuleWithProviders<NgaModule>>{
      providers: [...NGA_VALIDATORS, ...NGA_SERVICES],
       // TranslateModule.forRoot({
  //   loader: {
  //     provide: TranslateLoader,
  //     useFactory: HttpLoaderFactory,
  //     deps: [HttpClient]
  //   }
  // })
    };
  }
}
