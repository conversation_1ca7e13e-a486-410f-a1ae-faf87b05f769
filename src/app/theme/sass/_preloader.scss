@import 'conf/conf';

@-webkit-keyframes spin {
  0% {
    transform: rotate(0deg); /* Firefox 16+, IE 10+, Opera */
  }
  100% {
    transform: rotate(360deg); /* Firefox 16+, IE 10+, Opera */
  }
}

@-moz-keyframes spin {
  0% {
    -moz-transform: rotate(0deg); /* Firefox 16+*/
  }
  100% {
    -moz-transform: rotate(360deg); /* Firefox 16+*/
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg); /* Firefox 16+, IE 10+, Opera */
  }
  100% {
    transform: rotate(360deg); /* Firefox 16+, IE 10+, Opera */
  }
}


#preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 991003;
  background-color: rgba(153, 153, 153, 0.43);
}
#preloader > div {
  display: block;
  position: relative;
  left: 50%;
  top: 50%;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border-radius: 50%;
  border: 3px solid transparent;
  transform: translate3d(0, 0, 0);
  animation: spin 2s linear infinite;
}
#preloader > div::before,
#preloader > div::after {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border-radius: 50%;
  border: 13px solid transparent;
  border-top-color: #FF8033;
}
#preloader > div::after {
  animation: spin 2.9s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

body, html {
  position: static !important;
  overflow: auto !important;
}

/* Ensure dialogs/modal backdrops don't interfere */
.cdk-overlay-container {
  z-index: 1000 !important; /* Lower than loader */
}

/* Spinner animation styles */
.preloader-content {
  z-index: 1000000 !important;
  position: relative !important;
}




// @-webkit-keyframes spin {
//   0% {
//     transform: rotate(0deg); /* Firefox 16+, IE 10+, Opera */
//   }
//   100% {
//     transform: rotate(360deg); /* Firefox 16+, IE 10+, Opera */
//   }
// }

// @-moz-keyframes spin {
//   0% {
//     -moz-transform: rotate(0deg); /* Firefox 16+*/
//   }
//   100% {
//     -moz-transform: rotate(360deg); /* Firefox 16+*/
//   }
// }

// @keyframes spin {
//   0% {
//     transform: rotate(0deg); /* Firefox 16+, IE 10+, Opera */
//   }
//   100% {
//     transform: rotate(360deg); /* Firefox 16+, IE 10+, Opera */
//   }
// }

// #preloader {
//   position: fixed;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   z-index: 991003;
//   background: transparent;
//   background-color: rgba(153, 153, 153, 0.43);
//   & > div {
//     display: block;
//     position: relative;
//     left: 50%;
//     top: 50%;
//     width: 150px;
//     height: 150px;
//     margin: -75px 0 0 -75px;
//     border-radius: 50%;
//     border: 3px solid transparent;
//     border-top-color: $danger;
//     transform: translate3d(0, 0, 0);
//     animation: spin 2s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
//     &:before {
//       content: "";
//       position: absolute;
//       top: 5px;
//       left: 5px;
//       right: 5px;
//       bottom: 5px;
//       border-radius: 50%;
//       border: 3px solid transparent;
//       border-top-color: $primary;
//       -webkit-animation: spin 3s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
//       animation: spin 3s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
//     }
//     &:after {
//       content: "";
//       position: absolute;
//       top: 15px;
//       left: 15px;
//       right: 15px;
//       bottom: 15px;
//       border-radius: 50%;
//       border: 3px solid transparent;
//       border-top-color: $warning;
//       animation: spin 1.5s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
//     }
//   }
// }



#preloaderContent {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1003;
  & > div {
    display: block;
    position: relative;
    left: 50%;
    top: 50%;
    width: 150px;
    height: 150px;
    margin: -75px 0 0 -75px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: $danger; /* Using $danger variable from conf */
    transform: translate3d(0, 0, 0);
    animation: spin 2s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    &:before {
      content: "";
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: $primary; /* Using $primary variable from conf */
      -webkit-animation: spin 3s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
      animation: spin 3s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    }
    &:after {
      content: "";
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: $warning; /* Using $warning variable from conf */
      animation: spin 1.5s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    }
  }
}
