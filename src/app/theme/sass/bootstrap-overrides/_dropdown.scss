.dropdown-item {
  line-height: 1;
}
.dropdown-item:active {
  background: transparent;
}

.dropdown-menu {
  font-size: inherit;
}

ss-multiselect-dropdown .dropdown-block .dropdown-toggle {
  text-align: left;
  border-radius: 3px;
  height: 38px;
}

ss-multiselect-dropdown .dropdown-block .dropdown-toggle::after {
  float: right;
  margin-top: 6px;
}

ss-multiselect-dropdown .dropdown-menu {
  width: 100%;
  padding: 0;
}

ss-multiselect-dropdown .dropdown-menu .dropdown-item {
  line-height: 2;
  border-bottom: 1px solid rgba(128, 128, 128, 0.43);
  padding: 8px 1.5rem;
}

ss-multiselect-dropdown .dropdown-menu .dropdown-item.active,
ss-multiselect-dropdown .dropdown-menu .dropdown-item:active {
  background-color: #a22e2a;
}
