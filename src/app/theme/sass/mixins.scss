@mixin confirmButtons() {
  .confirmButtons{
    width:50%;
    margin:0 auto;
    .yesButton{
      width: 49%;
      float: left;
      .yesConfirm{
        width: 90%;
        height:35px;
        background-color: $button-color;
        border: none;
        font-size:$font-size;
        border-radius: $border-radius;
        outline: none;
        color:white;
        cursor: pointer;
      }
    }
    .noButton{
      width: 49%;
      float: left;
      .noConfirm{
        width:90%;
        height:35px;
        background-color: grey !important;
        border: none;
        font-size:$font-size;
        border-radius: $border-radius;
        outline: none;
        color:white;
        cursor: pointer;
      }
    }
  }
}

@mixin placeholderColor($color...) {
  .searchbox::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: $color;
  }
  .searchbox::-moz-placeholder { /* Firefox 19+ */
    color: $color;
  }
  .searchbox:-ms-input-placeholder { /* IE 10+ */
    color: $color;
  }
  .searchbox:-moz-placeholder { /* Firefox 18- */
    color: $color;
  }
}

@mixin gridSearchInput() {
  width: 55%;
  float: left;
  padding: 5px 5px 5px 0px;
  .search {
    width: 100%;
    height: 35px;
    color:#666666;
    padding-left: 2%;
    border: 1px solid gainsboro;
    outline: none;
    box-shadow: none;
    @media screen and (max-width: 767px) {
      height: 45px;
    }
  }
}

@mixin inputRightBorder($border-radius...) {
  .right-border {
    border-bottom-right-radius: $border-radius;
    border-top-right-radius: $border-radius;
  }
}

@mixin inputAddOnBorder($border-radius...) {
  .input-group-addon {
    border-bottom-left-radius: $border-radius;
    border-top-left-radius: $border-radius;
    padding: 6px 8px;
    background: #eceeef;
    border: 1px solid rgba(0,0,0,.1);
  }
}

@mixin addButtonContainer() {
  width: 35%;
  float: right;
  padding: 5px 0 0px 0px;
  .add {
    width: 50%;
    height: 35px;
    background-color: $button-color;
    outline: none;
    border: none;
    border-radius: $border-radius;
    color: white;
    font-size: 15px;
    cursor: pointer;
    float: right;
    @media screen and (max-width: 767px) {
      height: 45px;
      margin: 5px 0;
    }
    @media screen and (max-width: 1023px) {
      width: 100%;
      margin: 5px 0;
    }
  }
  .export-data {
    width: auto;
    float: left;
    height: 35px;
    background-color: $button-color;
    outline: none;
    border: none;
    border-radius: $border-radius;
    color: white;
    font-size: 15px;
    cursor: pointer;
    @media screen and (max-width: 767px) {
      height: 45px;
      margin: 5px 0;
    }
    @media screen and (max-width: 1023px) {
      width: 100%;
      margin: 5px 0;
    }
  }
}

@mixin uploadCancelButtonContainer() {
  width: 100%;
  height: 35px;
  background-color: #FF8033;
  outline: none;
  border: none;
  border-radius: 3px;
  color: white;
  font-size: 15px;
  cursor: pointer;
}

@mixin buttonContainer () {
  display: block;
  width: 50%;
  float: left;
}

@mixin confirmDialogueActiveInActive() {
  .modal-footer{
    display:none;
  }

  .modal{
    bottom: inherit;
  }

  .fade{
    opacity: 1 !important;
    top: 12%;
  }
  .confirmButtonsContainer{
    width:100%;
    height:50px;
    padding: 20px 0 0 0;
    @include confirmButtons();
  }
  .confirmBookingsContainer{
    width:100%;
    height:50px;
    @include confirmButtons();
  }
  .modal-dialog {
    position: relative;
    top: 50%;
    margin: 0 auto;
    transform: translateY(-50%) !important;
    -ms-transform: translateY(-50%) !important;
    -webkit-transform: translateY(-50%) !important;
    -moz-transform: translateY(-50%) !important;
    -o-transform: translateY(-50%) !important;

  }
  .modal-content {
    padding: 20px;
    float: left;
  }
  button.close {
    position: absolute;
    right: 0;
    padding-right: 10px;
    margin-top: -18px;
    cursor: pointer;
  }
  .modal-body {
    padding: 0 0 20px;
    top: 20px;
  }
  .month-wise-data .modal-dialog {
    max-width: 520px;
  }
}
