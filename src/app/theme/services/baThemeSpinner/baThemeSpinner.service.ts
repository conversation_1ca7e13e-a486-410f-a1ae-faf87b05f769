import { Injectable } from "@angular/core";

@Injectable({
  providedIn: 'root'
})
export class BaThemeSpinner {
  private _selector: string = "preloader";
  private _contentselector: string = "preloaderContent";

  private get _element(): HTMLElement | null {
    return document.getElementById(this._selector);
  }

  private get _contentElement(): HTMLElement | null {
    return document.getElementById(this._contentselector);
  }

  public show(): void {
    const el = this._element;
    if (el) {
      el.style["display"] = "block";
      el.style["zIndex"] = "999999";
    }
    this.showProgress();
  }

  public hide(delay: number = 0): void {
    setTimeout(() => {
      const el = this._element;
      if (el) {
        el.style["display"] = "none";
      }
      this.hideProgress();
    }, delay);
  }

  public showProgress(delay: number = 0): void {
    setTimeout(() => {
      const el = this._element;
      if (el) {
        el.style["display"] = "block";
      }
    }, delay);
  }

  public hideProgress(delay: number = 0): void {
    setTimeout(() => {
      const el = this._element;
      if (el) {
        el.style["display"] = "none";
      }
    }, delay);
  }
}
