import { importProvidersFrom } from "@angular/core";
import { App } from "./app/app.component";
import { RestangularModule } from "ngx-restangular";
import { provideAnimations } from "@angular/platform-browser/animations";
import { routing } from "./app/app.routing";
import { NgaModule } from "./app/theme/nga.module";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule, Router } from "@angular/router";
import { withInterceptorsFromDi, provideHttpClient, HTTP_INTERCEPTORS } from "@angular/common/http";
import { BrowserModule, bootstrapApplication } from "@angular/platform-browser";
import { EditSKUData } from "./app/app-services/edit-sku-data-service";
import { ToastrService, ToastrModule } from "ngx-toastr";
import { RewardPointsService } from "./app/app-services/reward-points-service";
import { TerritoriesService } from "./app/app-services/territories-service";
import { RegionsService } from "./app/app-services/regions-service";
import { ZonesService } from "./app/app-services/zones-service";
import { UserService } from "./app/app-services/user-service";
import { SbusGuard } from "./app/guards/sbus.guard";
import { RegionsGuard } from "./app/guards/regions.guard";
import { ZonesGuard } from "./app/guards/zones.guard";
import { GlobalEvents } from "./app/helpers/global.events";
import { UtilityHelper } from "./app/helpers/utility";
import { PagerService } from "./app/helpers/pager";
import { AuthenticationHelper } from "./app/helpers/authentication";
import { LoginGuard, AuthGuard, AdminGuard } from "./app/guards/app.guard";
import { GlobalState } from "./app/helpers/global.state";
import { AppState } from "./app/helpers/app.state";
import { BaThemePreloader } from "./app/theme/services/baThemePreloader";
import { BaThemeSpinner } from "./app/theme/services/baThemeSpinner";
import { BaMenuService } from "./app/theme/services/baMenu/baMenu.service";
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpClient } from '@angular/common/http';
import { AppTranslateService } from "./app/app-services/translate.service";
import { AuthInterceptor } from "./app/auth.interceptor";
export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

const APP_PROVIDERS = [
  AppState,
  GlobalState,
  LoginGuard,
  AuthGuard,
  AuthenticationHelper,
  PagerService,
  UtilityHelper,
  GlobalEvents,
  AdminGuard,
  ZonesGuard,
  RegionsGuard,
  SbusGuard,
];
let bearerToken = AuthenticationHelper.getToken();
const APP_SERVICE_PROVIDERS = [
  UserService,
  ZonesService,
  RegionsService,
  TerritoriesService,
  RewardPointsService,
  ToastrService,
];

bootstrapApplication(App, {
  providers: [
    importProvidersFrom(
      BrowserModule,
      RouterModule,
      FormsModule,
      ReactiveFormsModule,
      BaThemePreloader,
      BaThemeSpinner,
      BaMenuService,
      NgaModule.forRoot(),
      routing,
      TranslateModule.forRoot({
        defaultLanguage: 'en',
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient],  // <-- tell Angular to inject HttpClient here
        }
      }),
      ToastrModule.forRoot({
        timeOut: 3000,
        positionClass: 'toast-top-right',
        preventDuplicates: true,
        closeButton: false, // Change from true to false to remove the close icon
        progressBar: true,
        maxOpened: 1,
        easing: "ease-in",
        autoDismiss: true,
        // Add these settings for dev server compatibility
        toastClass: 'ngx-toastr',
        iconClasses: {
          error: 'toast-error',
          info: 'toast-info',
          success: 'toast-success',
          warning: 'toast-warning'
        }
      }),
      RestangularModule.forRoot([AuthenticationHelper, ToastrService, Router])
    ),
    {
      provide: 'INIT_TRANSLATION',
      useFactory: (appTranslate: AppTranslateService) => {
        return () => appTranslate.loadLanguage(); // returns a Promise
      },
      deps: [AppTranslateService],
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true // Allows multiple interceptors
    },
    APP_PROVIDERS,
    APP_SERVICE_PROVIDERS,
    EditSKUData,
    provideHttpClient(withInterceptorsFromDi()),
    provideAnimations(), // Required for toastr animations
  ],
});
