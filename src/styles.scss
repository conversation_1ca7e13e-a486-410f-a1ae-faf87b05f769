@import 'ngx-toastr/toastr';
@import "./app/theme/theme.scss";
@import "./app/theme/sass/conf/variables";
@import "./app/theme/sass/_auth";
@import "./app/theme/sass/mixins";
@import '@angular/material/prebuilt-themes/indigo-pink.css';
@import url("https://fonts.googleapis.com/icon?family=Material+Icons");
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css');

#pagination, #paginationOperator, #paginationAssignMachine, #paginationUnAssignMachine {
  padding: 0 0;
  display: inline-block;
  margin: 10px 2px;
  border-radius: 25px;
  ul {
    margin: 0;
    padding: 0;
    line-height: 1.5;

  }
  ul li {
    // border: 1px solid #d6d6d6;
    margin-left: -2px;
    height: 35px;
    text-align: center;
    min-width: 35px;
    line-height: 27px;
    border: none;
    // border-left: 1px solid #d6d6d6;
    border-right: 1px solid #d6d6d6;
  }
  .ngx-pagination {
    .current {
      background: $button-color !important;
      color: #fff;
      border: none;
    }
    .pagination-previous {
      border-bottom-left-radius: $border-radius;
      border-top-left-radius: $border-radius;
    }
    .pagination-next {
      // border-bottom-right-radius: $border-radius;
      // border-top-right-radius: $border-radius;
      border: none;
    }
    a {
      color: #606c71;
      text-decoration: none;
      // &:hover {
      // height: 35px;
      // }
    }
    .disabled {
      padding: 0.1875rem 0.625rem;
      margin-left: 0.9px;
      color: #cacaca;
      cursor: not-allowed;
      border: none !important;
    }
  }
}

@include scrollbars(.5em, #d9d9d9, rgba(0, 0, 0, 0));

.ngx-pagination a:hover, .ngx-pagination button:hover {
  background: none !important;
}
.btn:hover {
  transform: none;
}

//for input box
.inputbox {
  border-radius: $border-radius;
  background-color: white;
  width: 100%;
  height: 100%;
  margin-bottom: 0px;
  padding-left: 2%;
  border: 1px solid gainsboro;
  outline: none;
  box-shadow: none;
  color: #555555;
}

.inputbox::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: gainsboro;
}

.inputbox::-moz-placeholder { /* Firefox 19+ */
  color: gainsboro;
}

.inputbox:-ms-input-placeholder { /* IE 10+ */
  color: gainsboro;
}

.inputbox:-moz-placeholder { /* Firefox 18- */
  color: gainsboro;
}

.inputbox:focus, .inputbox:active {
  border-color: #A5C9BB;
  outline: 0;
  outline: thin dotted \9
;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(82, 168, 236, .6);
  box-shadow: inset 0 1px 1px rgba(0, 220, 0, .075), 0 0 8px rgba(0, 225, 0, .2);
}

.input-button {
  height: auto;
  line-height: 2.8;
  margin: 3% 0 -2% 0;
}

//for btn style
.btn-style {
  width: 100%;
  height: 100%;
  background-color: $button-color;
  color: white;
  font-size: 15px;
  font-weight: bold;
  border: none;
  border-radius: $border-radius;
  cursor: pointer;
  &:focus {
    outline: none;
  }
}

 .btn-style-fixed {
   width: 150px;
   height: 35px;
   background-color: $button-color;
   color: white;
   font-size: 15px;
   border: none;
   border-radius: $border-radius;
   cursor: pointer;
   &:focus {
     outline: none;
   }
 }

.btn-cancel-style {
  background-color: $button-cancel-color;
  color: white;
  font-weight: bold;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  width: auto;
  font-size: 14px;
  min-width: 150px;
  height: auto;
  &:focus {
    outline: none;
  }
}

.back-arrow {
  font-size: 30px;
}

.back-report-arrow {
  float: left;
  padding: 7px 10px 0 0;
}

.password-error-message {
  float: left;
  margin-top: -35px;
  width: 100%;
  margin-bottom: 10px;
}

.capitalise {
  text-transform: capitalize;
}

.fixed-header {
  min-width: 110px;
}
.hand-pointer {
  cursor: pointer;
}

.required {
  color: red;
}

.date-padding-top {
  padding-top: 10px;
}

.multi-line {
  white-space: pre-line;
  position: absolute;
  line-height: 21px;
  font-weight: 400;
}

body {
  width: 100%;
  display: block;
  float: left;
  margin: 0;
  padding: 0;
  color: #000;
  font-size: 14px;
}
.fa-male {
  font-size: 20px;
}
.gmnoprint .gm-style-mtc{
  display: none;
}

.modal-backdrop.fade {
  opacity: 0.6;
}

.confirmUserActiveContainer .fade {
  opacity: 1 !important;
}

.confirmUserActiveContainer .modal-dialog {
  position: relative;
  top: 50%;
  margin: 0 auto;
  transform: translateY(-50%) !important;
  -ms-transform: translateY(-50%) !important;
  -webkit-transform: translateY(-50%) !important;
  -moz-transform: translateY(-50%) !important;
  -o-transform: translateY(-50%) !important;
}
//.common-container, .booking-main, .user-container, .farmer-main {
  .confirmUserActiveContainer {
    .modal-body {
      top: 0 !important;
      padding-bottom: 0 !important;
    }
    .modal-header {
      padding: 0;
    }
    .modal-footer {
      display: none;
    }
    button.close {
      position: absolute;
      cursor: pointer;
      right: 10px !important;
      padding-right: 0 !important;
      margin-top: 0 !important;
    }
  }
//}
.filters-drop-style {
.input-group.search-container {
padding: 5px;
.input-group-prepend {
}
}
.button.btn.btn-default {
padding: 7px !important;
}
}

#products, .filters-drop-style {
.left-align {
text-align: left !important;
}
.dropdown-toggle::after {
display: inline-block;
width: 0;
float: right;
top: 6px;
position: relative;
height: 0;
margin-left: 0;
vertical-align: middle;
content: "";
border-top: 0.5em solid;
border-right: 0.2em solid transparent;
border-left: 0.2em solid transparent;
}
.input-group-prepend {
padding: 6px;
background: lightgrey;
border-top-left-radius: 4px;
border-bottom-left-radius: 4px;
}

button.btn.btn-default {
border-width: 1px;
color: #ffffff;
border-radius: 0;
padding: 8px;
background: #5f5f5f;
border-color: rgba(255, 255, 255, 0.5);
}
.dropdown-menu {
border-radius: 5px;
margin-bottom: 10px;
padding: 0;
}

.search-container {
padding: 10px 5px 5px;
position: sticky;
top: 0;
background: #fff;
z-index: 999;
}

.dropdown-item {
line-height: 1;
border-bottom: 1px solid rgba(128, 128, 128, 0.43);
padding: 8px 1.5rem;
}

.empty {
text-align: center;
padding-bottom: 15px;
}
}

.bill-image {
display: flex;
justify-content: center;
align-items: center;
.inputfile {
width: 0.1px;
height: 0.1px;
opacity: 0;
overflow: hidden;
position: relative;
z-index: -1;
}
.inputfile-1 + label {
color: $white-bg;
background-color: $button-color;
}

.inputfile-2 + label {
color: $white-bg;
background-color: $button-color;
}

.inputfile-3 + label {
color: $white-bg;
background-color: $trash-color;
}

.inputfile + label {
font-size: 15px;
text-overflow: ellipsis;
white-space: nowrap;
cursor: pointer;
border-radius: 3px;
display: inline-block;
overflow: hidden;
margin-bottom: 0;
}
.button-space {
padding: 6px;
margin-right: 10px;
}
.button-space-machine {
padding: 8px;
}
.upload-doc {
width: 40px;
padding: 8px;
margin-right: 5px;
text-align: center;
}
.view-file{
width: 40px;
}
.inputfile + label svg {
width: 1em;
height: 1em;
vertical-align: middle;
fill: currentColor;
margin-top: -0.25em;
margin-right: 0.25em;
}
}
.no-padding {
padding: 0;
}
.adjust-btn {
margin: 15px 17px 0;
float: right;
}
.machine-report-edit {
display: inline-block;
float: left;
padding: 15px 0 15px;
.date-lable{
padding-top: 5px;
}
.right-space {
margin-right: 15px;
}
}
.left-space {
margin-left: 15px;
}

#foo {
height: auto;
h2 {
font-weight: 700;
color: #4d5a77;
float: left;
padding: 0;
font-size: 14px;
text-transform: uppercase;
}
}
.no-line-height {
line-height: 0;
}

.full-width {
width: 100%;
}

.month-year {
width: 100%;
padding: 6px;
border-radius: 3px;
cursor: pointer;
outline: none;
color: #636363;
box-shadow: none;
border: 1px solid #cccccc;
}
.style-cal {
cursor: pointer;
font-size: 16px;
position: absolute;
right: 21px;
bottom: 10px;
color: black;
}
.filters-padding {
padding: 15px 0 15px;
}
.no-left-padding {
padding-left: 0;
}
.no-margin{
margin: 0;
}
.top-bottom-margin {
margin: 10px 0 10px;
float: left;
}
.margin-refresh {
margin-bottom: 10px;
}
.hamburger {
background: #4d5a77;
float: right;
cursor: pointer;
padding: 6px 10px;
margin-left: 10px;
outline: none;
i {
margin: 0;
font-size: 1em;
color: #fff;
font-weight: bold;
}
}
.overflow-visible {
overflow-y: visible !important;
}

#filters-section {
animation-duration: 0.3s;
}
.visible-filters {
display: block;
}
.hide-filters {
display: none;
}

.machine-preview {
.modal-body {
position: relative;
flex: 1 1 auto;
padding: 0;
}
}
.min-padding{
padding: 6px;
}
.min-margin-left {
margin-left: 5px;
}
.min-top{
top: 5px;
}
.no-data {
text-align: center;
height: 40px;
line-height: 40px;
border: 1px solid #ddd;
}


.c-btn{
background: #fff;
border: 1px solid #ccc;
color: #333;
}
.read-only {
pointer-events: none;
.c-btn.disabled {
background: #fff;
}
}
.selected-list{
.c-list{
.c-token{
  padding: 0 23px 0 5px !important;
  margin-top: 0 !important;
  background: #FF8033;
  .c-label{
    color: #fff;
  }
  .c-remove {
    svg {
      fill: #fff;
    }
  }

}
}
.c-angle-down, .c-angle-up{
svg {
}
}}
.dropdown-list{
ul{
li:hover{
  background: #f5f5f5;
}
}
}
.arrow-up, .arrow-down {
border-bottom: 15px solid #fff;
}

.arrow-2{
border-bottom: 15px solid #ccc;
}
.list-area{
border: 1px solid #ccc;
background: #fff;
box-shadow: 0 1px 5px #959595;
.filter-select-all {
padding: 10px 10px 5px;
text-align: center;
}
.list-message {
text-align: center;
margin: 0;
padding: 15px 0;
font-size: inherit;
font-weight: 400;
}
}
.select-all{
border-bottom: 1px solid #ccc;
}
.list-filter{
border-bottom: 1px solid #ccc;
.c-search{
svg {
  fill: #888;
}
}
.c-clear {
cursor: pointer;
svg {
  fill: #888;
}
}
}

.pure-checkbox {
input[type="checkbox"]:focus + label:before, input[type="checkbox"]:hover + label:before{
border-color: #0079FE;
background-color: #f2f2f2;
}
input[type="checkbox"] + label{
}
input[type="checkbox"] + label:before{
color: #FF8033;
border: 2px solid #FF8033;
}
input[type="checkbox"] + label:after{
background-color: #FF8033;
}
input[type="checkbox"]:disabled + label:before{
border-color: #cccccc;
}
input[type="checkbox"]:disabled:checked + label:before{
background-color: #cccccc;
}
input[type="checkbox"] + label:after{
border-color: #ffffff;
}
input[type="radio"]:checked + label:before{
background-color: white;
}
input[type="checkbox"]:checked + label:before{
background: #FF8033;
}
}
.selected-item{
background: #e9f4ff;
}
.machine-breakdown {
.break-down-header {
width: 100%;
text-align: center;
font-size: 15px;
font-weight: 500;
}
.close{
display: none !important;
}
}

.date-filter-content {
  .start-date, .end-date {
    .mydp .selectiongroup, .mydp .selection {
      height: 34px !important
    }
  }
}

.reward-delete-container {
  width: 100%;
  float: left;
  .modal-backdrop.fade {
    opacity: 0.6;
  }

  .fade {
    opacity: 1 !important;
    top: 12%;
  }

  .modal-content{
    width: 100%;
    float: left;
    .modal-body {
      padding: 0;
    }
    .modal-footer, .modal-header {
      display: none;
    }
  }
  .reward-modal {
    width: 100%;
    float: left;
    padding: 0 0 10px 0;
    .reward-delete-header {
      position: relative;
      padding: 20px 0;
      width: 100%;
      float: left;
      border-bottom: 1px solid gainsboro;
      .reward-delete-modal-header {
        width: 100%;
        float: left;
        text-align: center;
        margin: 0;
      }
      .reward-delete {
        position: absolute;
        right: 10px;
        font-size: 17px;
        top: 5px;
        cursor: pointer;
      }
    }
    .delete-fields {
      width: 100%;
      float: left;
      padding: 10px 20px;
    }
    .reward-delete-action {
      width: 100%;
      float: left;
      .reward-delete-action-container {
        width: 100%;
        float: left;
        padding: 10px;
        .action-btn {
          border-radius: 3px;
          height: 40px;
          background-color: grey;
          color: #fff;
          font-weight: bold;
          width: 35%;
          cursor: pointer;
        }

        .submit-delete {
          background-color: #FF8033;
          float: right;
        }
      }
    }
  }


}
.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input, .mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control {
  position: absolute;
  top: 10%;
  height: auto;
  font-size: 13px;
  width: 110%;
}

::ng-deep .mat-mdc-text-field-wrapper.mdc-text-field.mdc-text-field--outlined.mdc-text-field--no-label {
  height: 38px !important;
  border-radius: 8px;
}


// .cdk-overlay-connected-position-bounding-box {
//   width: 200px !important;
// }

// // Fix for toast notifications across all components
// body {
//   .toast-container {
//     position: fixed !important;
//     z-index: 999999 !important; 
//     pointer-events: auto !important;
//     top: 12px !important;
//     right: 12px !important;
//     width: auto !important;
//     padding :12px;
//     max-width: 350px !important;
    
//     .ngx-toastr {
//       background-color: #333333 !important;
//       box-shadow: 0 0 12px rgba(0, 0, 0, 0.25) !important;
//       color: #ffffff !important;
//       pointer-events: auto !important;
//       border-radius: 4px !important;
//       opacity: 1 !important;
      
//       &:hover {
//         box-shadow: 0 0 12px rgba(0, 0, 0, 0.4) !important;
//         opacity: 1 !important;
//       }
//     }
    
//     .toast-success {
//       background-color: #51A351 !important;
//     }
    
//     .toast-error {
//       background-color: #BD362F !important;
//     }
    
//     .toast-info {
//       background-color: #2F96B4 !important;
//     }
    
//     .toast-warning {
//       background-color: #F89406 !important;
//     }
//   }
// }


// // Fix toast animations and positioning
// .toast-top-right {
//   top: 12px !important;
//   right: 12px !important;
// }

// .toast-top-left {
//   top: 12px !important;
//   left: 12px !important;
// }

// .toast-bottom-right {
//   right: 12px !important;
//   bottom: 12px !important;
// }

// .toast-bottom-left {
//   bottom: 12px !important;
//   left: 12px !important;
// }

// // Fix toast title and message
// .toast-title {
//   font-weight: bold !important;
//   margin-bottom: 5px !important;
// }

// .toast-message {
//   word-wrap: break-word !important;
// }

// Ensure toasts are visible above all components
.cdk-overlay-container {
  z-index: 999998 !important;
}

// Table popup CSS

.common-popup-container{
  .popup-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .popup-content {
    background: white;
    padding: 0;
    border-radius: 8px;
    width: 80%;
    max-height: 80vh;
    overflow: hidden;
    position: relative;
  }
  
  .popup-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 20px;
    position: relative;
  }
  
  .al-title {
    font-weight: 600;
    color: #222222;
    margin: 0;
    font-size: 18px;
    text-align: center;
    flex-grow: 1;
  }
  
  .close-btn {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent; /* Ensures no background */
    border: none; /* Removes border completely */
    outline: none; /* Removes focus outline (add custom focus styles if needed) */
    font-size: 22px;
    cursor: pointer;
    padding: 2px;
    color: #6c757d;
    transition: color 0.2s ease;
    
    &:hover, &:focus {
      color: #495057;
      background: transparent; /* Ensures no background on hover */
      box-shadow: none; /* Removes any potential shadow */
    }
  }
  
  .table-container {
    padding: 0 10px 10px 10px;
    max-height: calc(80vh - 60px);
    overflow-y: auto;
  }
}

/* Toast fixes for dev server */
@media screen and (min-width: 0\0) {
  .toast-container {
    position: fixed !important;
    z-index: 999999 !important; 
    pointer-events: auto !important;
    top: 12px !important;
    right: 12px !important;
    width: auto !important;
    max-width: 350px !important;
    display: block !important;
    visibility: visible !important;
    padding: 50px !important; 
    
    .ngx-toastr {
      opacity: 1 !important;
      box-shadow: 0 0 12px rgba(0, 0, 0, 0.25) !important;
      display: block !important;
      visibility: visible !important;
      pointer-events: auto !important;
    }
    
    .toast-success {
      background-color: #51A351 !important;
    }
    
    .toast-error {
      background-color: #BD362F !important;
    }
    
    .toast-info {
      background-color: #2F96B4 !important;
    }
    
    .toast-warning {
      background-color: #F89406 !important;
    }
  }
}
